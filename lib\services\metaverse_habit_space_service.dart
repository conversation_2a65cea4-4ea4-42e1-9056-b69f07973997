import 'dart:async';
import 'dart:math';

// 가상 습관 공간
class VirtualHabitSpace {
  final String id;
  final String name;
  final String theme;
  final String environment;
  final Map<String, dynamic> decorations;
  final List<String> availableActivities;
  final Map<String, dynamic> ambientSettings;
  final int maxOccupancy;
  final bool isPublic;

  VirtualHabitSpace({
    required this.id,
    required this.name,
    required this.theme,
    required this.environment,
    required this.decorations,
    required this.availableActivities,
    required this.ambientSettings,
    required this.maxOccupancy,
    required this.isPublic,
  });
}

// 가상 아바타
class VirtualAvatar {
  final String id;
  final String userId;
  final String name;
  final String appearance;
  final Map<String, dynamic> customizations;
  final Map<String, dynamic> achievements;
  final List<String> equippedItems;
  final Map<String, double> position; // x, y, z 좌표

  VirtualAvatar({
    required this.id,
    required this.userId,
    required this.name,
    required this.appearance,
    required this.customizations,
    required this.achievements,
    required this.equippedItems,
    required this.position,
  });
}

// 메타버스 이벤트
class MetaverseEvent {
  final String type;
  final String userId;
  final String spaceId;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  MetaverseEvent({
    required this.type,
    required this.userId,
    required this.spaceId,
    required this.data,
    required this.timestamp,
  });
}

// 메타버스 가상 습관 공간 서비스
class MetaverseHabitSpaceService {
  static bool _isInitialized = false;
  static bool _isConnected = false;
  static VirtualAvatar? _userAvatar;
  static VirtualHabitSpace? _currentSpace;
  static List<VirtualHabitSpace> _availableSpaces = [];
  static List<VirtualAvatar> _nearbyAvatars = [];
  static Timer? _metaverseTimer;

  // 실시간 메타버스 이벤트 스트림
  static final StreamController<MetaverseEvent> _eventStreamController =
      StreamController<MetaverseEvent>.broadcast();
  static Stream<MetaverseEvent> get eventStream =>
      _eventStreamController.stream;

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 메타버스 플랫폼 연결
      await _connectToMetaverse();

      // 사용자 아바타 생성/로드
      await _initializeUserAvatar();

      // 가상 공간들 로드
      await _loadVirtualSpaces();

      // 실시간 동기화 시작
      _startMetaverseSync();

      _isInitialized = true;
      return true;
    } catch (e) {
      print('메타버스 습관 공간 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 메타버스 플랫폼 연결
  static Future<void> _connectToMetaverse() async {
    print('🌐 메타버스 플랫폼 연결 중...');
    await Future.delayed(const Duration(seconds: 3));
    _isConnected = true;
    print('✅ 메타버스 네트워크 연결 완료');
  }

  // 사용자 아바타 초기화
  static Future<void> _initializeUserAvatar() async {
    _userAvatar = VirtualAvatar(
      id: 'avatar_${DateTime.now().millisecondsSinceEpoch}',
      userId: 'user_${Random().nextInt(10000)}',
      name: '습관 마스터',
      appearance: 'default_avatar',
      customizations: {
        'skinColor': '#FFE4B5',
        'hairColor': '#8B4513',
        'eyeColor': '#4169E1',
        'outfit': 'casual_sporty',
      },
      achievements: {
        'totalHabits': 0,
        'longestStreak': 0,
        'nftCount': 0,
        'level': 1,
      },
      equippedItems: ['basic_outfit', 'motivation_badge'],
      position: {'x': 0.0, 'y': 0.0, 'z': 0.0},
    );

    print('👤 가상 아바타 생성 완료: ${_userAvatar!.name}');
  }

  // 가상 공간들 로드
  static Future<void> _loadVirtualSpaces() async {
    _availableSpaces = [
      VirtualHabitSpace(
        id: 'zen_garden',
        name: '선 가든 (Zen Garden)',
        theme: 'mindfulness',
        environment: 'peaceful_japanese_garden',
        decorations: {
          'cherry_blossoms': true,
          'koi_pond': true,
          'meditation_stones': true,
          'bamboo_fountain': true,
        },
        availableActivities: [
          'meditation',
          'breathing_exercises',
          'mindful_walking'
        ],
        ambientSettings: {
          'lighting': 'soft_golden',
          'sounds': 'nature_water_birds',
          'weather': 'gentle_breeze',
          'temperature': 'comfortable',
        },
        maxOccupancy: 20,
        isPublic: true,
      ),
      VirtualHabitSpace(
        id: 'cyber_gym',
        name: '사이버 체육관 (Cyber Gym)',
        theme: 'fitness',
        environment: 'futuristic_fitness_center',
        decorations: {
          'holographic_equipment': true,
          'energy_fields': true,
          'motivational_displays': true,
          'achievement_walls': true,
        },
        availableActivities: [
          'virtual_workouts',
          'ar_training',
          'group_challenges'
        ],
        ambientSettings: {
          'lighting': 'energetic_neon',
          'sounds': 'upbeat_electronic',
          'weather': 'controlled_climate',
          'temperature': 'cool_energizing',
        },
        maxOccupancy: 50,
        isPublic: true,
      ),
      VirtualHabitSpace(
        id: 'study_sanctuary',
        name: '학습 성소 (Study Sanctuary)',
        theme: 'learning',
        environment: 'magical_library',
        decorations: {
          'floating_books': true,
          'knowledge_crystals': true,
          'wisdom_trees': true,
          'inspiration_portals': true,
        },
        availableActivities: [
          'focused_study',
          'group_learning',
          'knowledge_sharing'
        ],
        ambientSettings: {
          'lighting': 'warm_scholarly',
          'sounds': 'gentle_classical',
          'weather': 'calm_atmosphere',
          'temperature': 'focused_cool',
        },
        maxOccupancy: 30,
        isPublic: true,
      ),
      VirtualHabitSpace(
        id: 'creativity_cosmos',
        name: '창의성 우주 (Creativity Cosmos)',
        theme: 'creativity',
        environment: 'artistic_space_station',
        decorations: {
          'color_nebulas': true,
          'inspiration_stars': true,
          'creativity_portals': true,
          'artistic_tools': true,
        },
        availableActivities: [
          'creative_workshops',
          'art_collaboration',
          'idea_brainstorming'
        ],
        ambientSettings: {
          'lighting': 'dynamic_colorful',
          'sounds': 'ambient_creative',
          'weather': 'inspiring_energy',
          'temperature': 'comfortable_flow',
        },
        maxOccupancy: 25,
        isPublic: true,
      ),
    ];

    print('✅ ${_availableSpaces.length}개의 가상 습관 공간 로드 완료');
  }

  // 가상 공간 입장
  static Future<bool> enterSpace(String spaceId) async {
    try {
      final space = _availableSpaces.firstWhere((s) => s.id == spaceId);
      _currentSpace = space;

      // 아바타 위치 설정
      if (_userAvatar != null) {
        _userAvatar!.position['x'] = Random().nextDouble() * 100;
        _userAvatar!.position['y'] = 0.0;
        _userAvatar!.position['z'] = Random().nextDouble() * 100;
      }

      // 주변 아바타들 로드
      await _loadNearbyAvatars();

      // 입장 이벤트 발생
      _eventStreamController.add(MetaverseEvent(
        type: 'space_entered',
        userId: _userAvatar!.userId,
        spaceId: spaceId,
        data: {
          'spaceName': space.name,
          'theme': space.theme,
          'position': _userAvatar!.position,
        },
        timestamp: DateTime.now(),
      ));

      print('🚪 가상 공간 입장: ${space.name}');
      return true;
    } catch (e) {
      print('가상 공간 입장 오류: $e');
      return false;
    }
  }

  // 가상 공간 퇴장
  static Future<void> exitSpace() async {
    if (_currentSpace != null && _userAvatar != null) {
      _eventStreamController.add(MetaverseEvent(
        type: 'space_exited',
        userId: _userAvatar!.userId,
        spaceId: _currentSpace!.id,
        data: {
          'spaceName': _currentSpace!.name,
          'timeSpent': DateTime.now().millisecondsSinceEpoch,
        },
        timestamp: DateTime.now(),
      ));

      print('🚪 가상 공간 퇴장: ${_currentSpace!.name}');
    }

    _currentSpace = null;
    _nearbyAvatars.clear();
  }

  // 주변 아바타들 로드
  static Future<void> _loadNearbyAvatars() async {
    // 실제로는 서버에서 같은 공간의 다른 사용자들 조회
    _nearbyAvatars = [
      VirtualAvatar(
        id: 'avatar_friend1',
        userId: 'user_friend1',
        name: '운동왕김철수',
        appearance: 'athletic_avatar',
        customizations: {'outfit': 'gym_wear'},
        achievements: {'level': 15, 'totalHabits': 50},
        equippedItems: ['fitness_badge', 'streak_crown'],
        position: {'x': 25.0, 'y': 0.0, 'z': 30.0},
      ),
      VirtualAvatar(
        id: 'avatar_friend2',
        userId: 'user_friend2',
        name: '명상마스터이영희',
        appearance: 'zen_avatar',
        customizations: {'outfit': 'meditation_robes'},
        achievements: {'level': 12, 'totalHabits': 35},
        equippedItems: ['mindfulness_aura', 'peace_symbol'],
        position: {'x': 75.0, 'y': 0.0, 'z': 60.0},
      ),
    ];
  }

  // 실시간 메타버스 동기화
  static void _startMetaverseSync() {
    _metaverseTimer = Timer.periodic(const Duration(seconds: 5), (timer) async {
      if (_isConnected && _currentSpace != null) {
        await _syncMetaverseState();
      }
    });
  }

  // 메타버스 상태 동기화
  static Future<void> _syncMetaverseState() async {
    // 아바타 위치 업데이트
    await _updateAvatarPosition();

    // 주변 아바타 상태 업데이트
    await _updateNearbyAvatars();

    // 공간 이벤트 처리
    await _processSpaceEvents();
  }

  // 아바타 위치 업데이트
  static Future<void> _updateAvatarPosition() async {
    if (_userAvatar == null) return;

    // 랜덤 이동 시뮬레이션
    final random = Random();
    if (random.nextDouble() < 0.3) {
      _userAvatar!.position['x'] = (_userAvatar!.position['x']! as double) +
          (random.nextDouble() - 0.5) * 10;
      _userAvatar!.position['z'] = (_userAvatar!.position['z']! as double) +
          (random.nextDouble() - 0.5) * 10;

      // 경계 체크
      _userAvatar!.position['x'] =
          _userAvatar!.position['x']!.clamp(0.0, 100.0);
      _userAvatar!.position['z'] =
          _userAvatar!.position['z']!.clamp(0.0, 100.0);
    }
  }

  // 주변 아바타 업데이트
  static Future<void> _updateNearbyAvatars() async {
    for (final avatar in _nearbyAvatars) {
      // 다른 아바타들의 위치도 업데이트
      final random = Random();
      if (random.nextDouble() < 0.2) {
        avatar.position['x'] =
            (avatar.position['x']! as double) + (random.nextDouble() - 0.5) * 5;
        avatar.position['z'] =
            (avatar.position['z']! as double) + (random.nextDouble() - 0.5) * 5;

        avatar.position['x'] = avatar.position['x']!.clamp(0.0, 100.0);
        avatar.position['z'] = avatar.position['z']!.clamp(0.0, 100.0);
      }
    }
  }

  // 공간 이벤트 처리
  static Future<void> _processSpaceEvents() async {
    final random = Random();

    // 랜덤 이벤트 발생
    if (random.nextDouble() < 0.1) {
      final eventTypes = [
        'avatar_interaction',
        'space_activity',
        'achievement_unlock',
        'group_formation',
      ];

      final eventType = eventTypes[random.nextInt(eventTypes.length)];
      await _triggerSpaceEvent(eventType);
    }
  }

  // 공간 이벤트 트리거
  static Future<void> _triggerSpaceEvent(String eventType) async {
    if (_currentSpace == null || _userAvatar == null) return;

    switch (eventType) {
      case 'avatar_interaction':
        if (_nearbyAvatars.isNotEmpty) {
          final nearbyAvatar =
              _nearbyAvatars[Random().nextInt(_nearbyAvatars.length)];
          _eventStreamController.add(MetaverseEvent(
            type: 'avatar_interaction',
            userId: _userAvatar!.userId,
            spaceId: _currentSpace!.id,
            data: {
              'interactionType': 'greeting',
              'targetAvatar': nearbyAvatar.name,
              'message': '안녕하세요! 함께 습관을 실천해봐요! 👋',
            },
            timestamp: DateTime.now(),
          ));
        }
        break;

      case 'space_activity':
        final activities = _currentSpace!.availableActivities;
        final activity = activities[Random().nextInt(activities.length)];
        _eventStreamController.add(MetaverseEvent(
          type: 'space_activity',
          userId: _userAvatar!.userId,
          spaceId: _currentSpace!.id,
          data: {
            'activity': activity,
            'participants': _nearbyAvatars.length + 1,
            'duration': Random().nextInt(30) + 10,
          },
          timestamp: DateTime.now(),
        ));
        break;

      case 'achievement_unlock':
        _eventStreamController.add(MetaverseEvent(
          type: 'achievement_unlock',
          userId: _userAvatar!.userId,
          spaceId: _currentSpace!.id,
          data: {
            'achievement': '메타버스 탐험가',
            'description': '가상 공간에서 10분 이상 활동',
            'reward': 'explorer_badge',
          },
          timestamp: DateTime.now(),
        ));
        break;
    }
  }

  // 아바타 커스터마이징
  static Future<void> customizeAvatar(
      Map<String, dynamic> customizations) async {
    if (_userAvatar == null) return;

    _userAvatar!.customizations.addAll(customizations);

    _eventStreamController.add(MetaverseEvent(
      type: 'avatar_customized',
      userId: _userAvatar!.userId,
      spaceId: _currentSpace?.id ?? '',
      data: {
        'customizations': customizations,
        'newAppearance': _userAvatar!.appearance,
      },
      timestamp: DateTime.now(),
    ));
  }

  // 가상 아이템 장착
  static Future<void> equipItem(String itemId) async {
    if (_userAvatar == null) return;

    if (!_userAvatar!.equippedItems.contains(itemId)) {
      _userAvatar!.equippedItems.add(itemId);

      _eventStreamController.add(MetaverseEvent(
        type: 'item_equipped',
        userId: _userAvatar!.userId,
        spaceId: _currentSpace?.id ?? '',
        data: {
          'itemId': itemId,
          'equippedItems': _userAvatar!.equippedItems,
        },
        timestamp: DateTime.now(),
      ));
    }
  }

  // 그룹 활동 시작
  static Future<void> startGroupActivity(String activityType) async {
    if (_currentSpace == null || _userAvatar == null) return;

    _eventStreamController.add(MetaverseEvent(
      type: 'group_activity_started',
      userId: _userAvatar!.userId,
      spaceId: _currentSpace!.id,
      data: {
        'activityType': activityType,
        'participants': _nearbyAvatars.map((a) => a.name).toList(),
        'maxParticipants': 10,
        'duration': 30,
      },
      timestamp: DateTime.now(),
    ));
  }

  // Getter 메서드들
  static List<VirtualHabitSpace> get availableSpaces => _availableSpaces;
  static VirtualHabitSpace? get currentSpace => _currentSpace;
  static VirtualAvatar? get userAvatar => _userAvatar;
  static List<VirtualAvatar> get nearbyAvatars => _nearbyAvatars;
  static bool get isConnected => _isConnected;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _metaverseTimer?.cancel();
    _eventStreamController.close();
    _isInitialized = false;
    _isConnected = false;
  }
}

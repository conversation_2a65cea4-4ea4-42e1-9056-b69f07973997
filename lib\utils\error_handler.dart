import 'package:flutter/material.dart';

/// Utility class for handling errors and showing user-friendly messages
class ErrorHandler {
  /// Shows a snackbar with an error message
  static void showError(BuildContext context, String message, {Color? backgroundColor}) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: backgroundColor ?? Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Shows a snackbar with a success message
  static void showSuccess(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 2),
      ),
    );
  }

  /// Shows a snackbar with a warning message
  static void showWarning(BuildContext context, String message) {
    if (!context.mounted) return;
    
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.orange,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  /// Handles common exceptions and returns user-friendly messages
  static String getErrorMessage(dynamic error) {
    if (error is FormatException) {
      return '데이터 형식 오류가 발생했습니다.';
    } else if (error is StateError) {
      return '앱 상태 오류가 발생했습니다.';
    } else if (error.toString().contains('SharedPreferences')) {
      return '데이터 저장 중 오류가 발생했습니다.';
    } else {
      return '알 수 없는 오류가 발생했습니다.';
    }
  }
}

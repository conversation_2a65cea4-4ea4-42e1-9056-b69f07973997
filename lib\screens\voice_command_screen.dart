import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/voice_command_service.dart';

class VoiceCommandScreen extends StatefulWidget {
  const VoiceCommandScreen({super.key});

  @override
  State<VoiceCommandScreen> createState() => _VoiceCommandScreenState();
}

class _VoiceCommandScreenState extends State<VoiceCommandScreen>
    with TickerProviderStateMixin {
  bool _isInitialized = false;
  bool _isListening = false;
  String _recognizedText = '';
  String _lastResult = '';
  late AnimationController _pulseController;
  late AnimationController _waveController;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _initializeVoiceService();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    VoiceCommandService.stopListening();
    super.dispose();
  }

  Future<void> _initializeVoiceService() async {
    final initialized = await VoiceCommandService.initialize();
    setState(() {
      _isInitialized = initialized;
    });

    if (!initialized) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('음성 인식을 사용할 수 없습니다. 마이크 권한을 확인해주세요.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _startListening() async {
    if (!_isInitialized || _isListening) return;

    setState(() {
      _isListening = true;
      _recognizedText = '';
      _lastResult = '';
    });

    _pulseController.repeat();
    _waveController.repeat();

    try {
      await VoiceCommandService.speak('무엇을 도와드릴까요?');
      
      final recognizedText = await VoiceCommandService.startListening();
      
      if (recognizedText != null && recognizedText.isNotEmpty) {
        setState(() {
          _recognizedText = recognizedText;
        });

        // 음성 명령 처리
        final result = await VoiceCommandService.processVoiceCommand(recognizedText);
        
        setState(() {
          _lastResult = result.message;
        });

        // 결과에 따른 피드백
        if (result.success) {
          await VoiceCommandService.speak(result.message);
        } else {
          await VoiceCommandService.speak('죄송합니다. ${result.message}');
        }
      } else {
        setState(() {
          _lastResult = '음성을 인식하지 못했습니다. 다시 시도해주세요.';
        });
        await VoiceCommandService.speak('음성을 인식하지 못했습니다.');
      }
    } catch (e) {
      setState(() {
        _lastResult = '음성 처리 중 오류가 발생했습니다.';
      });
    } finally {
      setState(() {
        _isListening = false;
      });
      _pulseController.stop();
      _waveController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '음성 명령',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.help_outline),
            onPressed: _showHelpDialog,
            tooltip: '도움말',
          ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          children: [
            Expanded(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 음성 인식 상태 표시
                  _buildVoiceIndicator(),
                  const SizedBox(height: 32),
                  
                  // 인식된 텍스트 표시
                  if (_recognizedText.isNotEmpty)
                    _buildRecognizedText(),
                  
                  // 결과 표시
                  if (_lastResult.isNotEmpty)
                    _buildResultCard(),
                  
                  const SizedBox(height: 32),
                  
                  // 음성 명령 버튼
                  _buildVoiceButton(),
                  
                  const SizedBox(height: 24),
                  
                  // 상태 텍스트
                  _buildStatusText(),
                ],
              ),
            ),
            
            // 지원되는 명령어 목록
            _buildCommandsList(),
          ],
        ),
      ),
    );
  }

  Widget _buildVoiceIndicator() {
    return SizedBox(
      width: 200,
      height: 200,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 외부 파동 효과
          if (_isListening)
            AnimatedBuilder(
              animation: _waveController,
              builder: (context, child) {
                return Container(
                  width: 200 * (1 + _waveController.value * 0.3),
                  height: 200 * (1 + _waveController.value * 0.3),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.3 - _waveController.value * 0.3),
                      width: 2,
                    ),
                  ),
                );
              },
            ),
          
          // 중간 파동 효과
          if (_isListening)
            AnimatedBuilder(
              animation: _waveController,
              builder: (context, child) {
                return Container(
                  width: 160 * (1 + _waveController.value * 0.2),
                  height: 160 * (1 + _waveController.value * 0.2),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color: Colors.blue.withOpacity(0.5 - _waveController.value * 0.3),
                      width: 2,
                    ),
                  ),
                );
              },
            ),
          
          // 메인 마이크 아이콘
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Container(
                width: 120 + (_isListening ? _pulseController.value * 20 : 0),
                height: 120 + (_isListening ? _pulseController.value * 20 : 0),
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: _isListening ? Colors.red : Colors.blue,
                  boxShadow: [
                    BoxShadow(
                      color: (_isListening ? Colors.red : Colors.blue).withOpacity(0.3),
                      blurRadius: 20,
                      spreadRadius: _isListening ? _pulseController.value * 10 : 0,
                    ),
                  ],
                ),
                child: Icon(
                  _isListening ? Icons.mic : Icons.mic_none,
                  size: 48,
                  color: Colors.white,
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRecognizedText() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.hearing, color: Colors.blue[600]),
                const SizedBox(width: 8),
                const Text(
                  '인식된 음성:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              '"$_recognizedText"',
              style: const TextStyle(
                fontSize: 16,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: 0.3, end: 0);
  }

  Widget _buildResultCard() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(Icons.smart_toy, color: Colors.green[600]),
                const SizedBox(width: 8),
                const Text(
                  'AI 응답:',
                  style: TextStyle(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Text(
              _lastResult,
              style: const TextStyle(fontSize: 16),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 500.ms).slideY(begin: -0.3, end: 0);
  }

  Widget _buildVoiceButton() {
    return SizedBox(
      width: 200,
      height: 60,
      child: ElevatedButton.icon(
        onPressed: _isInitialized && !_isListening ? _startListening : null,
        icon: Icon(_isListening ? Icons.stop : Icons.mic),
        label: Text(_isListening ? '듣는 중...' : '음성 명령 시작'),
        style: ElevatedButton.styleFrom(
          backgroundColor: _isListening ? Colors.red : Colors.blue,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(30),
          ),
        ),
      ),
    );
  }

  Widget _buildStatusText() {
    String statusText;
    Color statusColor;

    if (!_isInitialized) {
      statusText = '음성 인식을 사용할 수 없습니다';
      statusColor = Colors.red;
    } else if (_isListening) {
      statusText = '음성을 듣고 있습니다...';
      statusColor = Colors.blue;
    } else {
      statusText = '버튼을 눌러 음성 명령을 시작하세요';
      statusColor = Colors.grey;
    }

    return Text(
      statusText,
      style: TextStyle(
        color: statusColor,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      textAlign: TextAlign.center,
    );
  }

  Widget _buildCommandsList() {
    final commands = VoiceCommandService.getSupportedCommands();
    
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '지원되는 명령어:',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ...commands.map((command) {
              return Padding(
                padding: const EdgeInsets.only(bottom: 8),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text('• ', style: TextStyle(color: Colors.blue)),
                    Expanded(
                      child: Text(
                        command,
                        style: const TextStyle(fontSize: 14),
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  void _showHelpDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('음성 명령 도움말'),
        content: const SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '음성 명령 사용법:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 12),
              Text('1. "음성 명령 시작" 버튼을 누르세요'),
              Text('2. 명령어를 또렷하게 말하세요'),
              Text('3. AI가 명령을 처리합니다'),
              SizedBox(height: 16),
              Text(
                '팁:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              SizedBox(height: 8),
              Text('• 조용한 환경에서 사용하세요'),
              Text('• 명확하고 천천히 말하세요'),
              Text('• 습관 이름을 정확히 발음하세요'),
              Text('• 마이크 권한이 필요합니다'),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }
}

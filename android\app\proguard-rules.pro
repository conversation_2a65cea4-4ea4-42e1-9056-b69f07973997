# Add project specific ProGuard rules here.
# You can control the set of applied configuration files using the
# proguardFiles setting in build.gradle.

# Flutter 관련 규칙
-keep class io.flutter.app.** { *; }
-keep class io.flutter.plugin.**  { *; }
-keep class io.flutter.util.**  { *; }
-keep class io.flutter.view.**  { *; }
-keep class io.flutter.**  { *; }
-keep class io.flutter.plugins.**  { *; }

# 삼성 기기 호환성
-dontwarn com.samsung.**
-keep class com.samsung.** { *; }

# 센서 관련
-keep class android.hardware.** { *; }
-dontwarn android.hardware.**

# 블루투스 관련
-keep class android.bluetooth.** { *; }
-dontwarn android.bluetooth.**

# 위치 서비스
-keep class android.location.** { *; }
-dontwarn android.location.**

# 카메라 관련
-keep class android.hardware.camera2.** { *; }
-dontwarn android.hardware.camera2.**

# 일반적인 Android 규칙
-keepattributes *Annotation*
-keepattributes SourceFile,LineNumberTable
-keep public class * extends android.app.Activity
-keep public class * extends android.app.Application
-keep public class * extends android.app.Service
-keep public class * extends android.content.BroadcastReceiver
-keep public class * extends android.content.ContentProvider

# Gson 관련 (JSON 처리)
-keepattributes Signature
-keepattributes *Annotation*
-dontwarn sun.misc.**
-keep class com.google.gson.** { *; }
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# 네트워킹
-dontwarn okhttp3.**
-dontwarn okio.**
-dontwarn javax.annotation.**
-keepnames class okhttp3.internal.publicsuffix.PublicSuffixDatabase

# 기본 최적화 방지
-dontoptimize
-dontobfuscate

import 'dart:async';
import 'dart:math';

// 뇌파 데이터 모델
class BrainwaveData {
  final double alpha; // 8-12 Hz (이완 상태)
  final double beta; // 13-30 Hz (집중 상태)
  final double theta; // 4-7 Hz (창의성)
  final double delta; // 0.5-3 Hz (깊은 수면)
  final double gamma; // 30-100 Hz (고도 집중)
  final double focusLevel; // 0.0-1.0
  final double stressLevel; // 0.0-1.0
  final double creativityLevel; // 0.0-1.0
  final double meditationDepth; // 0.0-1.0
  final DateTime timestamp;

  BrainwaveData({
    required this.alpha,
    required this.beta,
    required this.theta,
    required this.delta,
    required this.gamma,
    required this.focusLevel,
    required this.stressLevel,
    required this.creativityLevel,
    required this.meditationDepth,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
        'alpha': alpha,
        'beta': beta,
        'theta': theta,
        'delta': delta,
        'gamma': gamma,
        'focusLevel': focusLevel,
        'stressLevel': stressLevel,
        'creativityLevel': creativityLevel,
        'meditationDepth': meditationDepth,
        'timestamp': timestamp.toIso8601String(),
      };
}

// 뇌파 기반 습관 추천
class BrainwaveHabitRecommendation {
  final String habitName;
  final String reason;
  final double optimalTime;
  final String brainwaveState;
  final double confidence;

  BrainwaveHabitRecommendation({
    required this.habitName,
    required this.reason,
    required this.optimalTime,
    required this.brainwaveState,
    required this.confidence,
  });
}

// 뇌파 모니터링 서비스
class BrainwaveMonitoringService {
  static bool _isInitialized = false;
  static bool _isConnected = false;
  static Timer? _monitoringTimer;
  static List<BrainwaveData> _dataHistory = [];

  // 실시간 뇌파 데이터 스트림
  static final StreamController<BrainwaveData> _brainwaveStreamController =
      StreamController<BrainwaveData>.broadcast();
  static Stream<BrainwaveData> get brainwaveStream =>
      _brainwaveStreamController.stream;

  // 뇌파 기반 추천 스트림
  static final StreamController<BrainwaveHabitRecommendation>
      _recommendationStreamController =
      StreamController<BrainwaveHabitRecommendation>.broadcast();
  static Stream<BrainwaveHabitRecommendation> get recommendationStream =>
      _recommendationStreamController.stream;

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 뇌파 센서 연결 시뮬레이션
      await _connectBrainwaveSensor();

      // 실시간 모니터링 시작
      _startRealTimeMonitoring();

      _isInitialized = true;
      return true;
    } catch (e) {
      print('뇌파 모니터링 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 뇌파 센서 연결
  static Future<void> _connectBrainwaveSensor() async {
    print('🧠 뇌파 센서 연결 중...');
    await Future.delayed(const Duration(seconds: 2));

    // 실제로는 EEG 헤드셋 (Muse, NeuroSky 등) 연결
    _isConnected = true;
    print('✅ 뇌파 센서 연결 완료');
  }

  // 실시간 모니터링 시작
  static void _startRealTimeMonitoring() {
    _monitoringTimer =
        Timer.periodic(const Duration(seconds: 1), (timer) async {
      if (_isConnected) {
        final brainwaveData = await _collectBrainwaveData();
        _dataHistory.add(brainwaveData);

        // 최근 100개 데이터만 유지
        if (_dataHistory.length > 100) {
          _dataHistory.removeAt(0);
        }

        _brainwaveStreamController.add(brainwaveData);

        // 뇌파 기반 습관 추천 분석
        await _analyzeBrainwaveForHabits(brainwaveData);
      }
    });
  }

  // 뇌파 데이터 수집
  static Future<BrainwaveData> _collectBrainwaveData() async {
    final random = Random();
    final now = DateTime.now();
    final hour = now.hour;

    // 시간대별 뇌파 패턴 시뮬레이션
    double baseAlpha = 0.3;
    double baseBeta = 0.4;
    double baseTheta = 0.2;
    double baseDelta = 0.1;
    double baseGamma = 0.15;

    // 시간대별 조정
    if (hour >= 6 && hour <= 9) {
      // 아침: 높은 베타파 (집중력)
      baseBeta += 0.3;
      baseAlpha += 0.1;
    } else if (hour >= 10 && hour <= 17) {
      // 낮: 균형잡힌 상태
      baseBeta += 0.2;
      baseGamma += 0.1;
    } else if (hour >= 18 && hour <= 21) {
      // 저녁: 높은 알파파 (이완)
      baseAlpha += 0.4;
      baseTheta += 0.2;
    } else {
      // 밤: 높은 델타파 (수면 준비)
      baseDelta += 0.5;
      baseAlpha += 0.2;
    }

    // 랜덤 변동 추가
    final alpha =
        (baseAlpha + (random.nextDouble() - 0.5) * 0.2).clamp(0.0, 1.0);
    final beta = (baseBeta + (random.nextDouble() - 0.5) * 0.2).clamp(0.0, 1.0);
    final theta =
        (baseTheta + (random.nextDouble() - 0.5) * 0.2).clamp(0.0, 1.0);
    final delta =
        (baseDelta + (random.nextDouble() - 0.5) * 0.2).clamp(0.0, 1.0);
    final gamma =
        (baseGamma + (random.nextDouble() - 0.5) * 0.2).clamp(0.0, 1.0);

    // 파생 지표 계산
    final focusLevel = (beta * 0.6 + gamma * 0.4).clamp(0.0, 1.0);
    final stressLevel = (beta * 0.8 - alpha * 0.3).clamp(0.0, 1.0);
    final creativityLevel = (theta * 0.7 + alpha * 0.3).clamp(0.0, 1.0);
    final meditationDepth =
        (alpha * 0.5 + theta * 0.3 + delta * 0.2).clamp(0.0, 1.0);

    return BrainwaveData(
      alpha: alpha,
      beta: beta,
      theta: theta,
      delta: delta,
      gamma: gamma,
      focusLevel: focusLevel,
      stressLevel: stressLevel,
      creativityLevel: creativityLevel,
      meditationDepth: meditationDepth,
      timestamp: now,
    );
  }

  // 뇌파 기반 습관 분석
  static Future<void> _analyzeBrainwaveForHabits(BrainwaveData data) async {
    final recommendations = <BrainwaveHabitRecommendation>[];

    // 높은 집중력 상태
    if (data.focusLevel > 0.8) {
      recommendations.add(BrainwaveHabitRecommendation(
        habitName: '집중 학습',
        reason: '현재 뇌파가 최적의 집중 상태입니다. 어려운 학습이나 업무에 도전해보세요!',
        optimalTime: 30.0,
        brainwaveState: 'high_focus',
        confidence: 0.95,
      ));
    }

    // 높은 창의성 상태
    if (data.creativityLevel > 0.7) {
      recommendations.add(BrainwaveHabitRecommendation(
        habitName: '창작 활동',
        reason: '세타파가 활성화되어 창의성이 높은 상태입니다. 글쓰기나 그림 그리기를 해보세요!',
        optimalTime: 25.0,
        brainwaveState: 'creative',
        confidence: 0.88,
      ));
    }

    // 높은 스트레스 상태
    if (data.stressLevel > 0.7) {
      recommendations.add(BrainwaveHabitRecommendation(
        habitName: '명상 및 호흡',
        reason: '스트레스 수치가 높습니다. 즉시 명상이나 깊은 호흡을 통해 마음을 진정시키세요.',
        optimalTime: 10.0,
        brainwaveState: 'stressed',
        confidence: 0.92,
      ));
    }

    // 깊은 명상 상태
    if (data.meditationDepth > 0.8) {
      recommendations.add(BrainwaveHabitRecommendation(
        habitName: '심화 명상',
        reason: '완벽한 명상 상태입니다. 더 깊은 명상이나 자기 성찰 시간을 가져보세요.',
        optimalTime: 20.0,
        brainwaveState: 'deep_meditation',
        confidence: 0.96,
      ));
    }

    // 추천사항 전송
    for (final recommendation in recommendations) {
      _recommendationStreamController.add(recommendation);
    }
  }

  // 뇌파 패턴 분석
  static Map<String, dynamic> analyzeBrainwavePatterns() {
    if (_dataHistory.length < 10) {
      return {'status': 'insufficient_data'};
    }

    final recentData = _dataHistory.length > 30
        ? _dataHistory.sublist(_dataHistory.length - 30)
        : _dataHistory;

    // 평균 계산
    double avgFocus =
        recentData.map((d) => d.focusLevel).reduce((a, b) => a + b) /
            recentData.length;
    double avgStress =
        recentData.map((d) => d.stressLevel).reduce((a, b) => a + b) /
            recentData.length;
    double avgCreativity =
        recentData.map((d) => d.creativityLevel).reduce((a, b) => a + b) /
            recentData.length;
    double avgMeditation =
        recentData.map((d) => d.meditationDepth).reduce((a, b) => a + b) /
            recentData.length;

    // 패턴 분석
    String dominantState = 'balanced';
    if (avgFocus > 0.7)
      dominantState = 'focused';
    else if (avgCreativity > 0.7)
      dominantState = 'creative';
    else if (avgStress > 0.6)
      dominantState = 'stressed';
    else if (avgMeditation > 0.7) dominantState = 'meditative';

    return {
      'status': 'analyzed',
      'avgFocus': avgFocus,
      'avgStress': avgStress,
      'avgCreativity': avgCreativity,
      'avgMeditation': avgMeditation,
      'dominantState': dominantState,
      'dataPoints': recentData.length,
      'recommendations':
          _generatePatternRecommendations(dominantState, avgStress),
    };
  }

  // 패턴 기반 추천
  static List<String> _generatePatternRecommendations(
      String dominantState, double avgStress) {
    final recommendations = <String>[];

    switch (dominantState) {
      case 'focused':
        recommendations.add('집중력이 뛰어납니다! 어려운 과제나 학습에 도전해보세요.');
        recommendations.add('집중 상태를 유지하기 위해 규칙적인 휴식을 취하세요.');
        break;
      case 'creative':
        recommendations.add('창의성이 활발합니다! 새로운 아이디어나 프로젝트를 시작해보세요.');
        recommendations.add('브레인스토밍이나 자유로운 사고 활동을 해보세요.');
        break;
      case 'stressed':
        recommendations.add('스트레스 관리가 필요합니다. 명상이나 요가를 시도해보세요.');
        recommendations.add('충분한 수면과 휴식을 취하세요.');
        break;
      case 'meditative':
        recommendations.add('명상 상태가 좋습니다! 자기 성찰이나 영적 활동을 해보세요.');
        recommendations.add('이 평온한 상태를 유지하며 감사 인사를 실천해보세요.');
        break;
      default:
        recommendations.add('균형잡힌 뇌파 상태입니다. 다양한 활동을 시도해보세요.');
    }

    if (avgStress > 0.6) {
      recommendations.add('⚠️ 스트레스 수치가 높습니다. 즉시 휴식을 취하세요.');
    }

    return recommendations;
  }

  // 최적 습관 시간 예측
  static Map<String, double> predictOptimalHabitTimes() {
    if (_dataHistory.length < 50) {
      return {};
    }

    final hourlyFocus = <int, List<double>>{};
    final hourlyCreativity = <int, List<double>>{};

    // 시간대별 데이터 분류
    for (final data in _dataHistory) {
      final hour = data.timestamp.hour;
      hourlyFocus.putIfAbsent(hour, () => []).add(data.focusLevel);
      hourlyCreativity.putIfAbsent(hour, () => []).add(data.creativityLevel);
    }

    // 평균 계산
    final avgHourlyFocus = <int, double>{};
    final avgHourlyCreativity = <int, double>{};

    for (final entry in hourlyFocus.entries) {
      avgHourlyFocus[entry.key] =
          entry.value.reduce((a, b) => a + b) / entry.value.length;
    }

    for (final entry in hourlyCreativity.entries) {
      avgHourlyCreativity[entry.key] =
          entry.value.reduce((a, b) => a + b) / entry.value.length;
    }

    // 최적 시간 찾기
    int bestFocusHour =
        avgHourlyFocus.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    int bestCreativityHour = avgHourlyCreativity.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;

    return {
      'study': bestFocusHour.toDouble(),
      'work': bestFocusHour.toDouble(),
      'creative': bestCreativityHour.toDouble(),
      'meditation': (bestCreativityHour + 2) % 24,
    };
  }

  // Getter 메서드들
  static List<BrainwaveData> get dataHistory => _dataHistory;
  static bool get isConnected => _isConnected;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _monitoringTimer?.cancel();
    _brainwaveStreamController.close();
    _recommendationStreamController.close();
    _isInitialized = false;
    _isConnected = false;
  }
}

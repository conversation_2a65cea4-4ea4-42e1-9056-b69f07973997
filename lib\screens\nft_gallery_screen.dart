import 'package:flutter/material.dart';
import 'dart:async';
import '../services/blockchain_nft_service.dart';

class NFTGalleryScreen extends StatefulWidget {
  const NFTGalleryScreen({super.key});

  @override
  State<NFTGalleryScreen> createState() => _NFTGalleryScreenState();
}

class _NFTGalleryScreenState extends State<NFTGalleryScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _shimmerController;
  StreamSubscription? _nftEventSubscription;
  
  List<HabitNFT> _userNFTs = [];
  List<HabitNFT> _marketplaceNFTs = [];
  double _tokenBalance = 0.0;
  String _walletAddress = '';

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _shimmerController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _initializeNFTData();
  }

  void _initializeNFTData() {
    _userNFTs = BlockchainNFTService.userNFTs;
    _tokenBalance = BlockchainNFTService.tokenBalance;
    _walletAddress = BlockchainNFTService.walletAddress;

    _nftEventSubscription = BlockchainNFTService.nftEventStream.listen((event) {
      setState(() {
        if (event['type'] == 'nft_minted') {
          _userNFTs = BlockchainNFTService.userNFTs;
          _tokenBalance = BlockchainNFTService.tokenBalance;
        }
      });
    });
  }

  @override
  void dispose() {
    _tabController.dispose();
    _shimmerController.dispose();
    _nftEventSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        title: const Text('⛓️ NFT 갤러리', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: Colors.amber,
          labelColor: Colors.amber,
          unselectedLabelColor: Colors.white70,
          tabs: const [
            Tab(text: '내 컬렉션'),
            Tab(text: '마켓플레이스'),
            Tab(text: '지갑'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildMyCollection(),
          _buildMarketplace(),
          _buildWallet(),
        ],
      ),
    );
  }

  Widget _buildMyCollection() {
    return Column(
      children: [
        _buildCollectionHeader(),
        Expanded(
          child: _userNFTs.isEmpty
              ? _buildEmptyCollection()
              : _buildNFTGrid(_userNFTs, isOwned: true),
        ),
      ],
    );
  }

  Widget _buildCollectionHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1A1A2E), Color(0xFF16213E)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.amber.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: const LinearGradient(
                colors: [Colors.amber, Colors.orange],
              ),
            ),
            child: const Icon(Icons.account_balance_wallet, color: Colors.white, size: 30),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '내 NFT 컬렉션',
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${_userNFTs.length}개의 습관 NFT 보유',
                  style: const TextStyle(color: Colors.white70, fontSize: 14),
                ),
                const SizedBox(height: 8),
                Row(
                  children: [
                    Icon(Icons.monetization_on, color: Colors.amber, size: 16),
                    const SizedBox(width: 4),
                    Text(
                      '${_tokenBalance.toStringAsFixed(1)} HABIT',
                      style: const TextStyle(color: Colors.amber, fontWeight: FontWeight.bold),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyCollection() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _shimmerController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + 0.1 * _shimmerController.value,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Colors.amber.withOpacity(0.3),
                        Colors.orange.withOpacity(0.3),
                      ],
                    ),
                  ),
                  child: const Icon(
                    Icons.collections,
                    size: 60,
                    color: Colors.amber,
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          const Text(
            '아직 NFT가 없습니다',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '습관을 꾸준히 실천하여 NFT를 획득하세요!',
            style: TextStyle(color: Colors.white70, fontSize: 14),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _simulateMintNFT,
            style: ElevatedButton.styleFrom(
              backgroundColor: Colors.amber,
              foregroundColor: Colors.black,
              padding: const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(25),
              ),
            ),
            child: const Text('첫 NFT 민팅하기'),
          ),
        ],
      ),
    );
  }

  Widget _buildNFTGrid(List<HabitNFT> nfts, {bool isOwned = false}) {
    return GridView.builder(
      padding: const EdgeInsets.all(16),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        childAspectRatio: 0.8,
        crossAxisSpacing: 16,
        mainAxisSpacing: 16,
      ),
      itemCount: nfts.length,
      itemBuilder: (context, index) {
        return _buildNFTCard(nfts[index], isOwned: isOwned);
      },
    );
  }

  Widget _buildNFTCard(HabitNFT nft, {bool isOwned = false}) {
    final rarityColors = {
      'common': Colors.grey,
      'rare': Colors.blue,
      'epic': Colors.purple,
      'legendary': Colors.orange,
      'mythic': Colors.red,
    };

    return GestureDetector(
      onTap: () => _showNFTDetails(nft),
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              rarityColors[nft.rarity]!.withOpacity(0.2),
              rarityColors[nft.rarity]!.withOpacity(0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: rarityColors[nft.rarity]!.withOpacity(0.5),
            width: 2,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // NFT 이미지 영역
            Expanded(
              flex: 3,
              child: Container(
                width: double.infinity,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(top: Radius.circular(14)),
                  gradient: LinearGradient(
                    colors: [
                      rarityColors[nft.rarity]!.withOpacity(0.3),
                      rarityColors[nft.rarity]!.withOpacity(0.1),
                    ],
                  ),
                ),
                child: Stack(
                  children: [
                    Center(
                      child: Icon(
                        _getHabitIcon(nft.habitType),
                        size: 60,
                        color: rarityColors[nft.rarity],
                      ),
                    ),
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: rarityColors[nft.rarity]!.withOpacity(0.8),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Text(
                          nft.rarity.toUpperCase(),
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
            
            // NFT 정보
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      nft.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    const SizedBox(height: 4),
                    Row(
                      children: [
                        Icon(Icons.local_fire_department, color: Colors.orange, size: 16),
                        const SizedBox(width: 4),
                        Text(
                          '${nft.streakAchieved}일',
                          style: const TextStyle(color: Colors.orange, fontSize: 12),
                        ),
                      ],
                    ),
                    const Spacer(),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Row(
                          children: [
                            Icon(Icons.monetization_on, color: Colors.amber, size: 16),
                            const SizedBox(width: 4),
                            Text(
                              '${nft.marketValue.toStringAsFixed(3)} ETH',
                              style: const TextStyle(color: Colors.amber, fontSize: 12),
                            ),
                          ],
                        ),
                        if (!isOwned)
                          Icon(Icons.shopping_cart, color: Colors.green, size: 16),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMarketplace() {
    return Column(
      children: [
        _buildMarketplaceHeader(),
        Expanded(
          child: _buildNFTGrid(_marketplaceNFTs, isOwned: false),
        ),
      ],
    );
  }

  Widget _buildMarketplaceHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1A2E1A), Color(0xFF162116)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: const BoxDecoration(
              shape: BoxShape.circle,
              gradient: LinearGradient(
                colors: [Colors.green, Colors.teal],
              ),
            ),
            child: const Icon(Icons.store, color: Colors.white, size: 30),
          ),
          const SizedBox(width: 16),
          const Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'NFT 마켓플레이스',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  '다른 사용자들의 습관 NFT를 구매하세요',
                  style: TextStyle(color: Colors.white70, fontSize: 14),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWallet() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          _buildWalletInfo(),
          const SizedBox(height: 20),
          _buildTransactionHistory(),
        ],
      ),
    );
  }

  Widget _buildWalletInfo() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF2E1A2E), Color(0xFF211621)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Container(
                width: 60,
                height: 60,
                decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                    colors: [Colors.purple, Colors.pink],
                  ),
                ),
                child: const Icon(Icons.account_balance_wallet, color: Colors.white, size: 30),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '내 지갑',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _walletAddress.substring(0, 10) + '...' + _walletAddress.substring(_walletAddress.length - 8),
                      style: const TextStyle(color: Colors.white70, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Row(
            children: [
              Expanded(
                child: _buildBalanceCard('HABIT 토큰', _tokenBalance.toString(), Colors.amber),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildBalanceCard('NFT 개수', _userNFTs.length.toString(), Colors.purple),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard(String title, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(
            title,
            style: const TextStyle(color: Colors.white70, fontSize: 12),
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionHistory() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(20),
      ),
      child: const Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '거래 내역',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 16),
          Text(
            '아직 거래 내역이 없습니다.',
            style: TextStyle(color: Colors.white70),
          ),
        ],
      ),
    );
  }

  void _showNFTDetails(HabitNFT nft) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        backgroundColor: Colors.transparent,
        child: Container(
          padding: const EdgeInsets.all(24),
          decoration: BoxDecoration(
            color: const Color(0xFF1A1A2E),
            borderRadius: BorderRadius.circular(20),
            border: Border.all(color: Colors.cyan.withOpacity(0.3)),
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                nft.name,
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 16),
              Text(
                nft.description,
                style: const TextStyle(color: Colors.white70),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 24),
              ElevatedButton(
                onPressed: () => Navigator.pop(context),
                child: const Text('닫기'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _simulateMintNFT() async {
    final nft = await BlockchainNFTService.mintHabitNFT(
      habitName: '물 마시기',
      habitType: 'health',
      streakAchieved: 7,
      achievementData: {'completionRate': 0.9},
    );

    if (nft != null) {
      setState(() {
        _userNFTs = BlockchainNFTService.userNFTs;
        _tokenBalance = BlockchainNFTService.tokenBalance;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('🎉 NFT "${nft.name}" 민팅 완료!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  IconData _getHabitIcon(String habitType) {
    switch (habitType) {
      case 'fitness':
        return Icons.fitness_center;
      case 'health':
        return Icons.local_hospital;
      case 'mindfulness':
        return Icons.self_improvement;
      case 'productivity':
        return Icons.work;
      case 'learning':
        return Icons.school;
      default:
        return Icons.star;
    }
  }
}

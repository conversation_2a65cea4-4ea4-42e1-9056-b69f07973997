name: habit_tracker
description: "초간단 습관 트래커 - 습관 형성을 위한 간단한 앱"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  shared_preferences: ^2.2.2
  intl: ^0.19.0

  # 새로운 기능을 위한 패키지들
  fl_chart: ^0.68.0
  flutter_animate: ^4.5.0
  flutter_local_notifications: ^17.2.3
  cached_network_image: ^3.4.1
  confetti: ^0.7.0

  # 추가 개선사항을 위한 패키지들
  provider: ^6.1.2
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  share_plus: ^10.1.2
  url_launcher: ^6.3.1
  home_widget: ^0.6.0
  timezone: ^0.9.4
  workmanager: ^0.5.2
  file_picker: ^8.1.4
  flutter_colorpicker: ^1.1.0

  # AI/ML 및 고급 기능을 위한 패키지들
  speech_to_text: ^7.0.0
  flutter_tts: ^4.2.0
  camera: ^0.11.0+2
  tflite_flutter: ^0.10.4
  image: ^4.2.0
  google_mlkit_text_recognition: ^0.13.1
  google_mlkit_pose_detection: ^0.12.0
  sensors_plus: ^6.0.1
  geolocator: ^13.0.1
  http: ^1.2.2

  # 고급 기능을 위한 패키지들
  health: ^13.0.1
  socket_io_client: ^2.0.3+1
  web_socket_channel: ^3.0.1
  dio: ^5.7.0
  connectivity_plus: ^6.1.0
  uuid: ^4.5.1
  crypto: ^3.0.6

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  mockito: ^5.4.4
  build_runner: ^2.4.13

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package

import 'package:intl/intl.dart';

/// Utility class for date-related operations
class DateUtils {
  static const String _dateFormat = 'yyyy-MM-dd';
  static final DateFormat _formatter = DateFormat(_dateFormat);

  /// Formats a DateTime to the standard string format used in the app
  static String formatDate(DateTime date) {
    return _formatter.format(date);
  }

  /// Parses a date string to DateTime
  static DateTime parseDate(String dateString) {
    return _formatter.parse(dateString);
  }

  /// Gets today's date as a formatted string
  static String getTodayString() {
    return formatDate(DateTime.now());
  }

  /// Gets yesterday's date as a formatted string
  static String getYesterdayString() {
    return formatDate(DateTime.now().subtract(const Duration(days: 1)));
  }

  /// Gets a list of date strings for the past N days (including today)
  static List<String> getPastDays(int days) {
    final now = DateTime.now();
    return List.generate(days, (index) {
      final date = now.subtract(Duration(days: index));
      return formatDate(date);
    });
  }

  /// Gets the start of the current week (Monday)
  static DateTime getStartOfWeek([DateTime? date]) {
    final target = date ?? DateTime.now();
    final daysFromMonday = target.weekday - 1;
    return target.subtract(Duration(days: daysFromMonday));
  }

  /// Gets the end of the current week (Sunday)
  static DateTime getEndOfWeek([DateTime? date]) {
    final startOfWeek = getStartOfWeek(date);
    return startOfWeek.add(const Duration(days: 6));
  }

  /// Checks if two dates are the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }

  /// Checks if a date is today
  static bool isToday(DateTime date) {
    return isSameDay(date, DateTime.now());
  }

  /// Checks if a date is yesterday
  static bool isYesterday(DateTime date) {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return isSameDay(date, yesterday);
  }

  /// Gets a human-readable relative date string
  static String getRelativeDateString(DateTime date) {
    if (isToday(date)) {
      return '오늘';
    } else if (isYesterday(date)) {
      return '어제';
    } else {
      final difference = DateTime.now().difference(date).inDays;
      if (difference < 7) {
        return '$difference일 전';
      } else if (difference < 30) {
        final weeks = (difference / 7).floor();
        return '$weeks주 전';
      } else {
        return formatDate(date);
      }
    }
  }

  /// Gets the number of days between two dates
  static int daysBetween(DateTime start, DateTime end) {
    return end.difference(start).inDays;
  }

  /// Checks if a date is within the last N days
  static bool isWithinLastDays(DateTime date, int days) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;
    return difference >= 0 && difference < days;
  }
}

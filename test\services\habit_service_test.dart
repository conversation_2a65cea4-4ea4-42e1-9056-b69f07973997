import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:habit_tracker/services/habit_service.dart';
import 'package:habit_tracker/models/habit.dart';
import 'dart:convert';

// Generate mocks
@GenerateMocks([SharedPreferences])
import 'habit_service_test.mocks.dart';

void main() {
  group('HabitService Tests', () {
    late MockSharedPreferences mockPrefs;

    setUp(() {
      mockPrefs = MockSharedPreferences();
    });

    group('loadHabits', () {
      test('should return empty list when no habits stored', () async {
        // Arrange
        when(mockPrefs.getString('habits')).thenReturn(null);
        SharedPreferences.setMockInitialValues({});

        // Act
        final habits = await HabitService.loadHabits();

        // Assert
        expect(habits, isEmpty);
      });

      test('should return list of habits when data exists', () async {
        // Arrange
        final testHabits = [
          Habit.create('Test Habit 1'),
          Habit.create('Test Habit 2'),
        ];
        final habitsJson = json.encode(testHabits.map((h) => h.toJson()).toList());
        
        SharedPreferences.setMockInitialValues({'habits': habitsJson});

        // Act
        final habits = await HabitService.loadHabits();

        // Assert
        expect(habits, hasLength(2));
        expect(habits[0].name, equals('Test Habit 1'));
        expect(habits[1].name, equals('Test Habit 2'));
      });

      test('should return empty list when JSON is invalid', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({'habits': 'invalid json'});

        // Act
        final habits = await HabitService.loadHabits();

        // Assert
        expect(habits, isEmpty);
      });
    });

    group('saveHabits', () {
      test('should save habits successfully', () async {
        // Arrange
        final testHabits = [Habit.create('Test Habit')];
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await HabitService.saveHabits(testHabits);

        // Assert
        expect(result, isTrue);
      });

      test('should handle save errors gracefully', () async {
        // This test is more conceptual since we can't easily mock SharedPreferences failures
        // In a real scenario, you might use dependency injection to make this more testable
        final testHabits = [Habit.create('Test Habit')];
        SharedPreferences.setMockInitialValues({});

        final result = await HabitService.saveHabits(testHabits);

        expect(result, isA<bool>());
      });
    });

    group('addHabit', () {
      test('should add new habit successfully', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await HabitService.addHabit('New Habit');

        // Assert
        expect(result, isTrue);
        
        // Verify the habit was actually added
        final habits = await HabitService.loadHabits();
        expect(habits, hasLength(1));
        expect(habits.first.name, equals('New Habit'));
      });

      test('should add habit to existing list', () async {
        // Arrange
        final existingHabit = Habit.create('Existing Habit');
        final habitsJson = json.encode([existingHabit.toJson()]);
        SharedPreferences.setMockInitialValues({'habits': habitsJson});

        // Act
        final result = await HabitService.addHabit('New Habit');

        // Assert
        expect(result, isTrue);
        
        final habits = await HabitService.loadHabits();
        expect(habits, hasLength(2));
        expect(habits.map((h) => h.name), contains('Existing Habit'));
        expect(habits.map((h) => h.name), contains('New Habit'));
      });
    });

    group('toggleHabitCompletion', () {
      test('should toggle habit completion successfully', () async {
        // Arrange
        final testHabit = Habit.create('Test Habit');
        final habitsJson = json.encode([testHabit.toJson()]);
        SharedPreferences.setMockInitialValues({'habits': habitsJson});

        // Act
        final result = await HabitService.toggleHabitCompletion(
          testHabit.id, 
          DateTime.now()
        );

        // Assert
        expect(result, isTrue);
        
        final habits = await HabitService.loadHabits();
        expect(habits.first.isCompletedToday(), isTrue);
      });

      test('should return false for non-existent habit', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await HabitService.toggleHabitCompletion(
          'non-existent-id', 
          DateTime.now()
        );

        // Assert
        expect(result, isFalse);
      });
    });

    group('deleteHabit', () {
      test('should delete habit successfully', () async {
        // Arrange
        final habit1 = Habit.create('Habit 1');
        await Future.delayed(const Duration(milliseconds: 1)); // Ensure unique IDs
        final habit2 = Habit.create('Habit 2');
        final habitsJson = json.encode([habit1.toJson(), habit2.toJson()]);
        SharedPreferences.setMockInitialValues({'habits': habitsJson});

        // Act
        final result = await HabitService.deleteHabit(habit1.id);

        // Assert
        expect(result, isTrue);

        final habits = await HabitService.loadHabits();
        expect(habits, hasLength(1));
        expect(habits.first.id, equals(habit2.id));
      });

      test('should handle deletion of non-existent habit', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final result = await HabitService.deleteHabit('non-existent-id');

        // Assert
        expect(result, isTrue); // Should still return true as operation "succeeded"
      });
    });

    group('calculateOverallCompletionRate', () {
      test('should return 0.0 for no habits', () async {
        // Arrange
        SharedPreferences.setMockInitialValues({});

        // Act
        final rate = await HabitService.calculateOverallCompletionRate();

        // Assert
        expect(rate, equals(0.0));
      });

      test('should calculate average completion rate correctly', () async {
        // Arrange
        final now = DateTime.now();
        final habit1 = Habit(
          id: '1',
          name: 'Habit 1',
          createdDate: now,
          completedDates: {
            // 7 out of 7 days = 100%
            for (int i = 0; i < 7; i++)
              '${now.subtract(Duration(days: i)).year}-${now.subtract(Duration(days: i)).month.toString().padLeft(2, '0')}-${now.subtract(Duration(days: i)).day.toString().padLeft(2, '0')}': true,
          },
        );
        
        final habit2 = Habit(
          id: '2',
          name: 'Habit 2',
          createdDate: now,
          completedDates: {}, // 0 out of 7 days = 0%
        );

        final habitsJson = json.encode([habit1.toJson(), habit2.toJson()]);
        SharedPreferences.setMockInitialValues({'habits': habitsJson});

        // Act
        final rate = await HabitService.calculateOverallCompletionRate();

        // Assert
        // Average of 100% and 0% should be 50%
        expect(rate, closeTo(0.5, 0.01));
      });
    });
  });
}

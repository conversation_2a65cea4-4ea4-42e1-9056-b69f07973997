import 'dart:convert';

class UserStats {
  final int level;
  final int experience;
  final int totalHabitsCompleted;
  final int currentStreak;
  final int longestStreak;
  final List<String> achievements;
  final DateTime lastActiveDate;
  final Map<String, int> weeklyStats; // 요일별 완료 통계

  UserStats({
    required this.level,
    required this.experience,
    required this.totalHabitsCompleted,
    required this.currentStreak,
    required this.longestStreak,
    required this.achievements,
    required this.lastActiveDate,
    required this.weeklyStats,
  });

  // 경험치로부터 레벨 계산
  static int calculateLevel(int experience) {
    return (experience / 100).floor() + 1;
  }

  // 다음 레벨까지 필요한 경험치
  int get experienceToNextLevel {
    final currentLevelExp = (level - 1) * 100;
    final nextLevelExp = level * 100;
    return nextLevelExp - experience;
  }

  // 현재 레벨에서의 진행률 (0.0 ~ 1.0)
  double get levelProgress {
    final currentLevelExp = (level - 1) * 100;
    final nextLevelExp = level * 100;
    final progressInLevel = experience - currentLevelExp;
    return progressInLevel / (nextLevelExp - currentLevelExp);
  }

  // JSON 직렬화
  Map<String, dynamic> toJson() {
    return {
      'level': level,
      'experience': experience,
      'totalHabitsCompleted': totalHabitsCompleted,
      'currentStreak': currentStreak,
      'longestStreak': longestStreak,
      'achievements': achievements,
      'lastActiveDate': lastActiveDate.toIso8601String(),
      'weeklyStats': weeklyStats,
    };
  }

  // JSON 역직렬화
  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      level: json['level'] ?? 1,
      experience: json['experience'] ?? 0,
      totalHabitsCompleted: json['totalHabitsCompleted'] ?? 0,
      currentStreak: json['currentStreak'] ?? 0,
      longestStreak: json['longestStreak'] ?? 0,
      achievements: List<String>.from(json['achievements'] ?? []),
      lastActiveDate: json['lastActiveDate'] != null 
          ? DateTime.parse(json['lastActiveDate'])
          : DateTime.now(),
      weeklyStats: Map<String, int>.from(json['weeklyStats'] ?? {}),
    );
  }

  // 기본 사용자 통계 생성
  factory UserStats.initial() {
    return UserStats(
      level: 1,
      experience: 0,
      totalHabitsCompleted: 0,
      currentStreak: 0,
      longestStreak: 0,
      achievements: [],
      lastActiveDate: DateTime.now(),
      weeklyStats: {
        'Monday': 0,
        'Tuesday': 0,
        'Wednesday': 0,
        'Thursday': 0,
        'Friday': 0,
        'Saturday': 0,
        'Sunday': 0,
      },
    );
  }

  // 습관 완료 시 통계 업데이트
  UserStats updateOnHabitCompletion(DateTime completionDate) {
    final today = DateTime.now();
    final isToday = completionDate.year == today.year &&
                   completionDate.month == today.month &&
                   completionDate.day == today.day;

    int newCurrentStreak = currentStreak;
    int newLongestStreak = longestStreak;
    
    if (isToday) {
      // 연속 일수 계산
      final daysDifference = today.difference(lastActiveDate).inDays;
      if (daysDifference <= 1) {
        newCurrentStreak = currentStreak + 1;
      } else {
        newCurrentStreak = 1;
      }
      
      newLongestStreak = newCurrentStreak > longestStreak ? newCurrentStreak : longestStreak;
    }

    // 요일별 통계 업데이트
    final weekday = _getWeekdayName(completionDate.weekday);
    final newWeeklyStats = Map<String, int>.from(weeklyStats);
    newWeeklyStats[weekday] = (newWeeklyStats[weekday] ?? 0) + 1;

    // 경험치 증가 (기본 10 + 스트릭 보너스)
    final expGain = 10 + (newCurrentStreak > 5 ? 5 : 0);
    final newExperience = experience + expGain;
    final newLevel = calculateLevel(newExperience);

    return UserStats(
      level: newLevel,
      experience: newExperience,
      totalHabitsCompleted: totalHabitsCompleted + 1,
      currentStreak: newCurrentStreak,
      longestStreak: newLongestStreak,
      achievements: _checkNewAchievements(newCurrentStreak, newLongestStreak, totalHabitsCompleted + 1),
      lastActiveDate: isToday ? today : lastActiveDate,
      weeklyStats: newWeeklyStats,
    );
  }

  // 새로운 업적 확인
  List<String> _checkNewAchievements(int streak, int longestStreak, int totalCompleted) {
    final newAchievements = List<String>.from(achievements);
    
    // 스트릭 업적
    if (streak >= 7 && !newAchievements.contains('week_warrior')) {
      newAchievements.add('week_warrior');
    }
    if (streak >= 30 && !newAchievements.contains('month_master')) {
      newAchievements.add('month_master');
    }
    if (streak >= 100 && !newAchievements.contains('century_champion')) {
      newAchievements.add('century_champion');
    }
    
    // 총 완료 업적
    if (totalCompleted >= 50 && !newAchievements.contains('half_century')) {
      newAchievements.add('half_century');
    }
    if (totalCompleted >= 100 && !newAchievements.contains('centurion')) {
      newAchievements.add('centurion');
    }
    if (totalCompleted >= 500 && !newAchievements.contains('legend')) {
      newAchievements.add('legend');
    }
    
    return newAchievements;
  }

  // 요일 이름 반환
  String _getWeekdayName(int weekday) {
    switch (weekday) {
      case 1: return 'Monday';
      case 2: return 'Tuesday';
      case 3: return 'Wednesday';
      case 4: return 'Thursday';
      case 5: return 'Friday';
      case 6: return 'Saturday';
      case 7: return 'Sunday';
      default: return 'Monday';
    }
  }
}

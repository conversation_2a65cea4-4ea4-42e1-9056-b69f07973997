import 'dart:async';
import 'dart:math';
import 'package:flutter_tts/flutter_tts.dart';

// 음성 프로필
class VoiceProfile {
  final String id;
  final String name;
  final String gender;
  final String accent;
  final double pitch; // 0.5 - 2.0
  final double speed; // 0.1 - 3.0
  final double volume; // 0.0 - 1.0
  final String emotionalTone;
  final Map<String, dynamic> personalityTraits;

  VoiceProfile({
    required this.id,
    required this.name,
    required this.gender,
    required this.accent,
    required this.pitch,
    required this.speed,
    required this.volume,
    required this.emotionalTone,
    required this.personalityTraits,
  });
}

// 감정 기반 음성 설정
class EmotionalVoiceSettings {
  final String emotion;
  final double pitchModifier;
  final double speedModifier;
  final double volumeModifier;
  final String tonalQuality;

  EmotionalVoiceSettings({
    required this.emotion,
    required this.pitchModifier,
    required this.speedModifier,
    required this.volumeModifier,
    required this.tonalQuality,
  });
}

// AI 기반 개인 맞춤형 음성 합성 서비스
class AIVoiceSynthesisService {
  static bool _isInitialized = false;
  static FlutterTts? _tts;
  static VoiceProfile? _currentProfile;
  static List<VoiceProfile> _availableProfiles = [];
  static Map<String, EmotionalVoiceSettings> _emotionalSettings = {};
  
  // 실시간 음성 이벤트 스트림
  static final StreamController<Map<String, dynamic>> _voiceEventStreamController = 
      StreamController<Map<String, dynamic>>.broadcast();
  static Stream<Map<String, dynamic>> get voiceEventStream => _voiceEventStreamController.stream;

  // 초기화
  static Future<bool> initialize() async {
    try {
      // TTS 엔진 초기화
      await _initializeTTSEngine();
      
      // AI 음성 프로필 생성
      await _createAIVoiceProfiles();
      
      // 감정 기반 설정 로드
      await _loadEmotionalSettings();
      
      // 사용자 맞춤 음성 학습
      await _learnUserPreferences();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('AI 음성 합성 서비스 초기화 오류: $e');
      return false;
    }
  }

  // TTS 엔진 초기화
  static Future<void> _initializeTTSEngine() async {
    _tts = FlutterTts();
    
    // TTS 설정
    await _tts!.setLanguage('ko-KR');
    await _tts!.setSpeechRate(0.5);
    await _tts!.setVolume(1.0);
    await _tts!.setPitch(1.0);
    
    print('🎤 AI 음성 합성 엔진 초기화 완료');
  }

  // AI 음성 프로필 생성
  static Future<void> _createAIVoiceProfiles() async {
    _availableProfiles = [
      VoiceProfile(
        id: 'coach_alex',
        name: '알렉스 코치',
        gender: 'male',
        accent: 'standard',
        pitch: 0.9,
        speed: 0.6,
        volume: 0.8,
        emotionalTone: 'energetic',
        personalityTraits: {
          'enthusiasm': 0.9,
          'supportiveness': 0.8,
          'directness': 0.7,
          'warmth': 0.6,
        },
      ),
      
      VoiceProfile(
        id: 'mentor_sophia',
        name: '소피아 멘토',
        gender: 'female',
        accent: 'gentle',
        pitch: 1.1,
        speed: 0.5,
        volume: 0.7,
        emotionalTone: 'caring',
        personalityTraits: {
          'empathy': 0.9,
          'patience': 0.9,
          'wisdom': 0.8,
          'gentleness': 0.8,
        },
      ),
      
      VoiceProfile(
        id: 'trainer_max',
        name: '맥스 트레이너',
        gender: 'male',
        accent: 'strong',
        pitch: 0.8,
        speed: 0.7,
        volume: 0.9,
        emotionalTone: 'intense',
        personalityTraits: {
          'intensity': 0.9,
          'motivation': 0.9,
          'discipline': 0.8,
          'confidence': 0.8,
        },
      ),
      
      VoiceProfile(
        id: 'guide_luna',
        name: '루나 가이드',
        gender: 'female',
        accent: 'soft',
        pitch: 1.2,
        speed: 0.4,
        volume: 0.6,
        emotionalTone: 'peaceful',
        personalityTraits: {
          'tranquility': 0.9,
          'mindfulness': 0.9,
          'serenity': 0.8,
          'balance': 0.8,
        },
      ),
    ];
    
    print('✅ ${_availableProfiles.length}개의 AI 음성 프로필 생성 완료');
  }

  // 감정 기반 설정 로드
  static Future<void> _loadEmotionalSettings() async {
    _emotionalSettings = {
      'excited': EmotionalVoiceSettings(
        emotion: 'excited',
        pitchModifier: 1.3,
        speedModifier: 1.2,
        volumeModifier: 1.0,
        tonalQuality: 'bright',
      ),
      'calm': EmotionalVoiceSettings(
        emotion: 'calm',
        pitchModifier: 0.9,
        speedModifier: 0.8,
        volumeModifier: 0.7,
        tonalQuality: 'smooth',
      ),
      'encouraging': EmotionalVoiceSettings(
        emotion: 'encouraging',
        pitchModifier: 1.1,
        speedModifier: 0.9,
        volumeModifier: 0.8,
        tonalQuality: 'warm',
      ),
      'serious': EmotionalVoiceSettings(
        emotion: 'serious',
        pitchModifier: 0.8,
        speedModifier: 0.7,
        volumeModifier: 0.9,
        tonalQuality: 'firm',
      ),
      'playful': EmotionalVoiceSettings(
        emotion: 'playful',
        pitchModifier: 1.4,
        speedModifier: 1.1,
        volumeModifier: 0.8,
        tonalQuality: 'light',
      ),
    };
  }

  // 사용자 맞춤 음성 학습
  static Future<void> _learnUserPreferences() async {
    // 실제로는 사용자의 음성 선호도 데이터를 분석
    print('🧠 사용자 음성 선호도 학습 중...');
    await Future.delayed(const Duration(seconds: 1));
    print('✅ 개인 맞춤 음성 프로필 학습 완료');
  }

  // 음성 프로필 설정
  static Future<void> setVoiceProfile(String profileId) async {
    try {
      final profile = _availableProfiles.firstWhere((p) => p.id == profileId);
      _currentProfile = profile;
      
      // TTS 설정 적용
      await _applyVoiceProfile(profile);
      
      _voiceEventStreamController.add({
        'type': 'profile_changed',
        'profileId': profileId,
        'profileName': profile.name,
        'timestamp': DateTime.now(),
      });
      
      print('🎭 음성 프로필 변경: ${profile.name}');
    } catch (e) {
      print('음성 프로필 설정 오류: $e');
    }
  }

  // 음성 프로필 적용
  static Future<void> _applyVoiceProfile(VoiceProfile profile) async {
    if (_tts == null) return;
    
    await _tts!.setPitch(profile.pitch);
    await _tts!.setSpeechRate(profile.speed);
    await _tts!.setVolume(profile.volume);
  }

  // 감정 기반 음성 출력
  static Future<void> speakWithEmotion({
    required String text,
    required String emotion,
    Map<String, dynamic>? context,
  }) async {
    if (_tts == null || _currentProfile == null) return;
    
    try {
      // 감정 설정 적용
      final emotionalSettings = _emotionalSettings[emotion];
      if (emotionalSettings != null) {
        await _applyEmotionalSettings(emotionalSettings);
      }
      
      // 컨텍스트 기반 텍스트 조정
      final adjustedText = _adjustTextForContext(text, emotion, context);
      
      // 음성 출력
      await _tts!.speak(adjustedText);
      
      // 이벤트 발생
      _voiceEventStreamController.add({
        'type': 'speech_started',
        'text': adjustedText,
        'emotion': emotion,
        'profile': _currentProfile!.name,
        'timestamp': DateTime.now(),
      });
      
    } catch (e) {
      print('감정 기반 음성 출력 오류: $e');
    }
  }

  // 감정 설정 적용
  static Future<void> _applyEmotionalSettings(EmotionalVoiceSettings settings) async {
    if (_tts == null || _currentProfile == null) return;
    
    final adjustedPitch = (_currentProfile!.pitch * settings.pitchModifier).clamp(0.5, 2.0);
    final adjustedSpeed = (_currentProfile!.speed * settings.speedModifier).clamp(0.1, 3.0);
    final adjustedVolume = (_currentProfile!.volume * settings.volumeModifier).clamp(0.0, 1.0);
    
    await _tts!.setPitch(adjustedPitch);
    await _tts!.setSpeechRate(adjustedSpeed);
    await _tts!.setVolume(adjustedVolume);
  }

  // 컨텍스트 기반 텍스트 조정
  static String _adjustTextForContext(
    String text, 
    String emotion, 
    Map<String, dynamic>? context,
  ) {
    String adjustedText = text;
    
    // 감정에 따른 텍스트 조정
    switch (emotion) {
      case 'excited':
        adjustedText = _addExcitement(text);
        break;
      case 'calm':
        adjustedText = _addCalmness(text);
        break;
      case 'encouraging':
        adjustedText = _addEncouragement(text);
        break;
      case 'serious':
        adjustedText = _addSeriousness(text);
        break;
      case 'playful':
        adjustedText = _addPlayfulness(text);
        break;
    }
    
    // 컨텍스트 기반 개인화
    if (context != null) {
      adjustedText = _personalizeText(adjustedText, context);
    }
    
    return adjustedText;
  }

  // 감정별 텍스트 조정 메서드들
  static String _addExcitement(String text) {
    return text.replaceAll('.', '!').replaceAll('?', '?!');
  }

  static String _addCalmness(String text) {
    return text.replaceAll('!', '.').toLowerCase();
  }

  static String _addEncouragement(String text) {
    final encouragingWords = ['정말 잘하고 있어요', '훌륭해요', '멋져요'];
    final random = Random();
    return '${encouragingWords[random.nextInt(encouragingWords.length)]}! $text';
  }

  static String _addSeriousness(String text) {
    return text.replaceAll('!', '.').replaceAll('?', '.');
  }

  static String _addPlayfulness(String text) {
    final playfulEmojis = ['😊', '🎉', '✨', '🌟'];
    final random = Random();
    return '$text ${playfulEmojis[random.nextInt(playfulEmojis.length)]}';
  }

  // 개인화된 텍스트
  static String _personalizeText(String text, Map<String, dynamic> context) {
    String personalizedText = text;
    
    // 사용자 이름 추가
    if (context['userName'] != null) {
      personalizedText = '${context['userName']}님, $personalizedText';
    }
    
    // 시간대 고려
    if (context['timeOfDay'] != null) {
      final timeGreeting = _getTimeGreeting(context['timeOfDay']);
      personalizedText = '$timeGreeting $personalizedText';
    }
    
    // 성과 언급
    if (context['achievement'] != null) {
      personalizedText = '$personalizedText ${context['achievement']}을 달성하신 것을 축하드려요!';
    }
    
    return personalizedText;
  }

  // 시간대별 인사
  static String _getTimeGreeting(String timeOfDay) {
    switch (timeOfDay) {
      case 'morning':
        return '좋은 아침이에요!';
      case 'afternoon':
        return '좋은 오후에요!';
      case 'evening':
        return '좋은 저녁이에요!';
      case 'night':
        return '늦은 시간이네요.';
      default:
        return '안녕하세요!';
    }
  }

  // 실시간 음성 조정
  static Future<void> adjustVoiceRealTime({
    double? pitch,
    double? speed,
    double? volume,
  }) async {
    if (_tts == null) return;
    
    if (pitch != null) {
      await _tts!.setPitch(pitch.clamp(0.5, 2.0));
    }
    
    if (speed != null) {
      await _tts!.setSpeechRate(speed.clamp(0.1, 3.0));
    }
    
    if (volume != null) {
      await _tts!.setVolume(volume.clamp(0.0, 1.0));
    }
  }

  // 음성 프로필 커스터마이징
  static Future<VoiceProfile> createCustomProfile({
    required String name,
    required Map<String, dynamic> preferences,
  }) async {
    final customProfile = VoiceProfile(
      id: 'custom_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      gender: preferences['gender'] ?? 'neutral',
      accent: preferences['accent'] ?? 'standard',
      pitch: preferences['pitch'] ?? 1.0,
      speed: preferences['speed'] ?? 0.5,
      volume: preferences['volume'] ?? 0.8,
      emotionalTone: preferences['emotionalTone'] ?? 'balanced',
      personalityTraits: preferences['personalityTraits'] ?? {},
    );
    
    _availableProfiles.add(customProfile);
    
    _voiceEventStreamController.add({
      'type': 'custom_profile_created',
      'profileId': customProfile.id,
      'profileName': customProfile.name,
      'timestamp': DateTime.now(),
    });
    
    return customProfile;
  }

  // 음성 학습 데이터 수집
  static void recordUserFeedback({
    required String profileId,
    required String emotion,
    required double satisfaction, // 0.0 - 1.0
    Map<String, dynamic>? feedback,
  }) {
    // 실제로는 ML 모델 학습을 위한 데이터 수집
    _voiceEventStreamController.add({
      'type': 'feedback_recorded',
      'profileId': profileId,
      'emotion': emotion,
      'satisfaction': satisfaction,
      'feedback': feedback,
      'timestamp': DateTime.now(),
    });
  }

  // Getter 메서드들
  static List<VoiceProfile> get availableProfiles => _availableProfiles;
  static VoiceProfile? get currentProfile => _currentProfile;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _tts?.stop();
    _voiceEventStreamController.close();
    _isInitialized = false;
  }
}

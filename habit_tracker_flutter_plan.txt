[사장-개발자 모바일 앱 10개 수익화 프로젝트]

1. 목표 및 방향성
- 모바일(안드로이드/iOS) 크로스플랫폼 앱 10개 개발, 일 수익 $10 달성
- 다양한 수익화 방식(광고, 유료, 구독 등) 실험
- 완전히 새로운 환경에서 시작

2. 기술 스택 및 개발 환경
- Flutter(Dart) 기반, 신규 폴더(habit_tracker_flutter 등)에서 개발
- Flutter SDK, Android Studio/VSCode, GitHub 신규 저장소
- Google AdMob, in_app_purchase, Firebase Analytics 등 연동

3. 1차 앱 아이템
- 앱명: 초간단 습관 트래커
- 기능: 습관 등록/체크/달성률, 광고(AdMob), 프리미엄(광고 제거) 인앱결제, 심플 UI/UX
- 타겟: 습관 형성에 관심 있는 일반 사용자

4. 개발/배포/운영 전략
- MVP 우선, 1주 1앱, Play Store 우선 배포, 코드 관리 자동화

5. Implementation Checklist
1. 신규 폴더(habit_tracker_flutter) 생성 및 이동
2. Flutter 개발 환경(Flutter SDK, IDE, 에뮬레이터) 신규 세팅
3. GitHub 저장소 신규 생성 및 초기 커밋
4. "초간단 습관 트래커" Flutter 프로젝트 생성
5. 기본 UI(습관 등록/체크/달성률) 구현
6. Google AdMob 광고 연동(테스트 광고)
7. 인앱결제(광고 제거) 옵션 구현
8. Firebase Analytics, Crashlytics 연동
9. Play Store 개발자 계정 준비 및 앱 등록
10. 앱 출시 및 다운로드/수익 모니터링
11. 사장(저)에게 주간 보고(성과, 이슈, 개선점)

---

*이 파일을 새로운 폴더로 복사해가면, 언제든지 계획을 참고할 수 있습니다. 추가 지시나 질문이 있으면 언제든 말씀해 주세요.*
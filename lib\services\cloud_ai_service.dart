import 'dart:async';
import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:crypto/crypto.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';
import '../services/habit_service.dart';
import '../services/user_stats_service.dart';

class CloudAIResponse {
  final String type;
  final Map<String, dynamic> data;
  final double confidence;
  final String message;
  final DateTime timestamp;

  CloudAIResponse({
    required this.type,
    required this.data,
    required this.confidence,
    required this.message,
    required this.timestamp,
  });

  factory CloudAIResponse.fromJson(Map<String, dynamic> json) {
    return CloudAIResponse(
      type: json['type'] ?? '',
      data: json['data'] ?? {},
      confidence: json['confidence']?.toDouble() ?? 0.0,
      message: json['message'] ?? '',
      timestamp: DateTime.parse(json['timestamp']),
    );
  }
}

class CloudAIService {
  static const String _baseUrl = 'https://api.habittracker.ai'; // 가상의 API 엔드포인트
  static const String _apiKey = 'your_api_key_here'; // 실제 API 키로 교체 필요
  static late Dio _dio;
  static bool _isInitialized = false;
  static String? _userId;

  // 초기화
  static Future<bool> initialize() async {
    try {
      _dio = Dio();
      _dio.options.baseUrl = _baseUrl;
      _dio.options.headers = {
        'Authorization': 'Bearer $_apiKey',
        'Content-Type': 'application/json',
      };
      _dio.options.connectTimeout = const Duration(seconds: 10);
      _dio.options.receiveTimeout = const Duration(seconds: 30);

      // 사용자 ID 생성 (디바이스 기반)
      _userId = await _generateUserId();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('클라우드 AI 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 사용자 ID 생성
  static Future<String> _generateUserId() async {
    try {
      // 디바이스 정보를 기반으로 고유 ID 생성
      final deviceInfo = 'device_${DateTime.now().millisecondsSinceEpoch}';
      final bytes = utf8.encode(deviceInfo);
      final digest = sha256.convert(bytes);
      return digest.toString().substring(0, 16);
    } catch (e) {
      return 'anonymous_user';
    }
  }

  // 연결 상태 확인
  static Future<bool> _checkConnectivity() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      return false;
    }
  }

  // 고급 습관 추천 요청
  static Future<List<Map<String, dynamic>>> getAdvancedRecommendations() async {
    if (!_isInitialized || !await _checkConnectivity()) {
      return _getFallbackRecommendations();
    }

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();

      final requestData = {
        'userId': _userId,
        'userStats': userStats.toJson(),
        'habits': habits.map((h) => h.toJson()).toList(),
        'timestamp': DateTime.now().toIso8601String(),
        'requestType': 'advanced_recommendations',
      };

      final response = await _dio.post('/ai/recommendations', data: requestData);
      
      if (response.statusCode == 200) {
        final cloudResponse = CloudAIResponse.fromJson(response.data);
        return List<Map<String, dynamic>>.from(cloudResponse.data['recommendations'] ?? []);
      }
    } catch (e) {
      print('고급 추천 요청 오류: $e');
    }

    return _getFallbackRecommendations();
  }

  // 개인화된 동기부여 메시지 생성
  static Future<List<String>> generatePersonalizedMotivation() async {
    if (!_isInitialized || !await _checkConnectivity()) {
      return _getFallbackMotivation();
    }

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();

      final requestData = {
        'userId': _userId,
        'userStats': userStats.toJson(),
        'habits': habits.map((h) => h.toJson()).toList(),
        'currentTime': DateTime.now().toIso8601String(),
        'requestType': 'personalized_motivation',
      };

      final response = await _dio.post('/ai/motivation', data: requestData);
      
      if (response.statusCode == 200) {
        final cloudResponse = CloudAIResponse.fromJson(response.data);
        return List<String>.from(cloudResponse.data['messages'] ?? []);
      }
    } catch (e) {
      print('개인화된 동기부여 생성 오류: $e');
    }

    return _getFallbackMotivation();
  }

  // 고급 습관 실패 예측
  static Future<Map<String, dynamic>> predictHabitFailures() async {
    if (!_isInitialized || !await _checkConnectivity()) {
      return _getFallbackPrediction();
    }

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();

      final requestData = {
        'userId': _userId,
        'userStats': userStats.toJson(),
        'habits': habits.map((h) => h.toJson()).toList(),
        'environmentalFactors': await _getEnvironmentalFactors(),
        'requestType': 'failure_prediction',
      };

      final response = await _dio.post('/ai/predict', data: requestData);
      
      if (response.statusCode == 200) {
        final cloudResponse = CloudAIResponse.fromJson(response.data);
        return cloudResponse.data;
      }
    } catch (e) {
      print('습관 실패 예측 오류: $e');
    }

    return _getFallbackPrediction();
  }

  // 환경적 요인 수집
  static Future<Map<String, dynamic>> _getEnvironmentalFactors() async {
    try {
      final now = DateTime.now();
      return {
        'timeOfDay': now.hour,
        'dayOfWeek': now.weekday,
        'isWeekend': now.weekday >= 6,
        'season': _getSeason(now),
        'weather': await _getWeatherInfo(), // 실제로는 날씨 API 연동 필요
      };
    } catch (e) {
      return {};
    }
  }

  // 계절 정보
  static String _getSeason(DateTime date) {
    final month = date.month;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  // 날씨 정보 (간단한 버전)
  static Future<Map<String, dynamic>> _getWeatherInfo() async {
    try {
      // 실제로는 날씨 API 연동
      return {
        'temperature': 22.0,
        'humidity': 60,
        'condition': 'sunny',
      };
    } catch (e) {
      return {};
    }
  }

  // 사용자 행동 패턴 분석
  static Future<Map<String, dynamic>> analyzeUserBehavior() async {
    if (!_isInitialized || !await _checkConnectivity()) {
      return _getFallbackBehaviorAnalysis();
    }

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();

      final requestData = {
        'userId': _userId,
        'userStats': userStats.toJson(),
        'habits': habits.map((h) => h.toJson()).toList(),
        'analysisType': 'behavior_pattern',
        'timeRange': '30_days',
      };

      final response = await _dio.post('/ai/analyze', data: requestData);
      
      if (response.statusCode == 200) {
        final cloudResponse = CloudAIResponse.fromJson(response.data);
        return cloudResponse.data;
      }
    } catch (e) {
      print('사용자 행동 분석 오류: $e');
    }

    return _getFallbackBehaviorAnalysis();
  }

  // 스마트 알림 최적화
  static Future<Map<String, dynamic>> optimizeNotifications() async {
    if (!_isInitialized || !await _checkConnectivity()) {
      return _getFallbackNotificationOptimization();
    }

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();

      final requestData = {
        'userId': _userId,
        'userStats': userStats.toJson(),
        'habits': habits.map((h) => h.toJson()).toList(),
        'userPreferences': await _getUserPreferences(),
        'requestType': 'notification_optimization',
      };

      final response = await _dio.post('/ai/optimize-notifications', data: requestData);
      
      if (response.statusCode == 200) {
        final cloudResponse = CloudAIResponse.fromJson(response.data);
        return cloudResponse.data;
      }
    } catch (e) {
      print('알림 최적화 오류: $e');
    }

    return _getFallbackNotificationOptimization();
  }

  // 사용자 선호도 가져오기
  static Future<Map<String, dynamic>> _getUserPreferences() async {
    try {
      // 실제로는 사용자 설정에서 가져와야 함
      return {
        'preferredNotificationTime': [9, 18], // 오전 9시, 오후 6시
        'notificationFrequency': 'moderate',
        'motivationStyle': 'encouraging',
      };
    } catch (e) {
      return {};
    }
  }

  // 사용자 데이터 업로드 (개인정보 보호 고려)
  static Future<bool> uploadAnonymizedData() async {
    if (!_isInitialized || !await _checkConnectivity()) {
      return false;
    }

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();

      // 개인정보 제거한 익명화된 데이터
      final anonymizedData = {
        'userId': _userId, // 해시된 ID
        'statsOnly': {
          'level': userStats.level,
          'totalCompleted': userStats.totalHabitsCompleted,
          'longestStreak': userStats.longestStreak,
          'weeklyStats': userStats.weeklyStats,
        },
        'habitPatterns': habits.map((h) => {
          'category': h.categoryId,
          'completionRate': h.getWeeklyCompletionRate(),
          'createdDaysAgo': DateTime.now().difference(h.createdDate).inDays,
        }).toList(),
        'timestamp': DateTime.now().toIso8601String(),
      };

      final response = await _dio.post('/data/upload', data: anonymizedData);
      return response.statusCode == 200;
    } catch (e) {
      print('익명화된 데이터 업로드 오류: $e');
      return false;
    }
  }

  // 폴백 추천 (오프라인용)
  static List<Map<String, dynamic>> _getFallbackRecommendations() {
    return [
      {
        'name': '물 8잔 마시기',
        'description': '하루에 물 8잔 마시기',
        'categoryId': 'health',
        'confidence': 0.8,
        'reason': '기본 건강 습관입니다.',
      },
      {
        'name': '10분 명상',
        'description': '매일 10분간 명상하기',
        'categoryId': 'mindfulness',
        'confidence': 0.7,
        'reason': '스트레스 관리에 도움됩니다.',
      },
    ];
  }

  // 폴백 동기부여 메시지
  static List<String> _getFallbackMotivation() {
    return [
      '오늘도 좋은 하루 되세요! 💪',
      '작은 습관이 큰 변화를 만듭니다! ✨',
      '꾸준함이 가장 큰 힘입니다! 🌟',
    ];
  }

  // 폴백 예측
  static Map<String, dynamic> _getFallbackPrediction() {
    return {
      'predictions': [],
      'overallRisk': 'low',
      'recommendations': ['꾸준히 진행하고 계십니다!'],
    };
  }

  // 폴백 행동 분석
  static Map<String, dynamic> _getFallbackBehaviorAnalysis() {
    return {
      'patterns': [],
      'insights': ['더 많은 데이터가 필요합니다.'],
      'recommendations': ['계속 습관을 기록해주세요.'],
    };
  }

  // 폴백 알림 최적화
  static Map<String, dynamic> _getFallbackNotificationOptimization() {
    return {
      'optimalTimes': [9, 18], // 오전 9시, 오후 6시
      'frequency': 'daily',
      'style': 'encouraging',
    };
  }

  // 연결 상태 확인
  static bool get isConnected => _isInitialized;

  // 사용자 ID 가져오기
  static String? get userId => _userId;

  // 정리
  static void dispose() {
    _dio.close();
    _isInitialized = false;
  }
}

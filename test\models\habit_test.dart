import 'package:flutter_test/flutter_test.dart';
import 'package:habit_tracker/models/habit.dart';
import 'package:intl/intl.dart';

void main() {
  group('Habit Model Tests', () {
    late Habit testHabit;
    late DateTime testDate;

    setUp(() {
      testDate = DateTime(2024, 1, 15);
      testHabit = Habit(
        id: 'test-id',
        name: 'Test Habit',
        createdDate: testDate,
        completedDates: {
          '2024-01-15': true,
          '2024-01-14': false,
          '2024-01-13': true,
          '2024-01-12': true,
          '2024-01-11': false,
          '2024-01-10': true,
          '2024-01-09': true,
        },
      );
    });

    group('Factory Constructor', () {
      test('should create habit with correct properties', () {
        final habit = Habit.create('New Habit');

        expect(habit.name, equals('New Habit'));
        expect(habit.id, isNotEmpty);
        expect(habit.createdDate, isA<DateTime>());
        expect(habit.completedDates, isEmpty);
      });

      test('should create unique IDs for different habits', () async {
        final habit1 = Habit.create('Habit 1');
        // Add a small delay to ensure different timestamps
        await Future.delayed(const Duration(milliseconds: 1));
        final habit2 = Habit.create('Habit 2');

        expect(habit1.id, isNot(equals(habit2.id)));
      });
    });

    group('JSON Serialization', () {
      test('should serialize to JSON correctly', () {
        final json = testHabit.toJson();

        expect(json['id'], equals('test-id'));
        expect(json['name'], equals('Test Habit'));
        expect(json['createdDate'], equals(testDate.toIso8601String()));
        expect(json['completedDates'], isA<Map<String, bool>>());
      });

      test('should deserialize from JSON correctly', () {
        final json = {
          'id': 'test-id',
          'name': 'Test Habit',
          'createdDate': testDate.toIso8601String(),
          'completedDates': {
            '2024-01-15': true,
            '2024-01-14': false,
          },
        };

        final habit = Habit.fromJson(json);

        expect(habit.id, equals('test-id'));
        expect(habit.name, equals('Test Habit'));
        expect(habit.createdDate, equals(testDate));
        expect(habit.completedDates['2024-01-15'], isTrue);
        expect(habit.completedDates['2024-01-14'], isFalse);
      });

      test('should handle null completedDates in JSON', () {
        final json = {
          'id': 'test-id',
          'name': 'Test Habit',
          'createdDate': testDate.toIso8601String(),
          'completedDates': null,
        };

        final habit = Habit.fromJson(json);

        expect(habit.completedDates, isEmpty);
      });
    });

    group('Completion Status', () {
      test('should return true when habit is completed today', () {
        final today = DateTime.now();
        final todayStr = DateFormat('yyyy-MM-dd').format(today);
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: today,
          completedDates: {todayStr: true},
        );

        expect(habit.isCompletedToday(), isTrue);
      });

      test('should return false when habit is not completed today', () {
        final today = DateTime.now();
        final todayStr = DateFormat('yyyy-MM-dd').format(today);
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: today,
          completedDates: {todayStr: false},
        );

        expect(habit.isCompletedToday(), isFalse);
      });

      test('should return false when no entry exists for today', () {
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: DateTime.now(),
          completedDates: {},
        );

        expect(habit.isCompletedToday(), isFalse);
      });
    });

    group('Weekly Completion Rate', () {
      test('should calculate correct weekly completion rate', () {
        // Use actual current date for testing
        final now = DateTime.now();
        final completedDates = <String, bool>{};

        // Add 5 out of 7 days as completed
        for (int i = 0; i < 7; i++) {
          final date = now.subtract(Duration(days: i));
          final dateStr = DateFormat('yyyy-MM-dd').format(date);
          // Complete days 0, 1, 3, 4, 6 (5 out of 7)
          completedDates[dateStr] = [0, 1, 3, 4, 6].contains(i);
        }

        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: now,
          completedDates: completedDates,
        );

        // 5 out of 7 days = 5/7 ≈ 0.714
        final rate = habit.getWeeklyCompletionRate();
        expect(rate, closeTo(5/7, 0.01));
      });

      test('should return 0.0 for no completed days', () {
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: DateTime.now(),
          completedDates: {},
        );

        expect(habit.getWeeklyCompletionRate(), equals(0.0));
      });

      test('should return 1.0 for all completed days', () {
        final now = DateTime.now();
        final completedDates = <String, bool>{};
        
        // Add 7 days of completed habits
        for (int i = 0; i < 7; i++) {
          final date = now.subtract(Duration(days: i));
          final dateStr = DateFormat('yyyy-MM-dd').format(date);
          completedDates[dateStr] = true;
        }

        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: now,
          completedDates: completedDates,
        );

        expect(habit.getWeeklyCompletionRate(), equals(1.0));
      });
    });

    group('Toggle Completion', () {
      test('should toggle completion from false to true', () {
        final date = DateTime(2024, 1, 16);
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: DateTime.now(),
          completedDates: {'2024-01-16': false},
        );

        final updatedHabit = habit.toggleCompletion(date);

        expect(updatedHabit.completedDates['2024-01-16'], isTrue);
        expect(updatedHabit.id, equals(habit.id));
        expect(updatedHabit.name, equals(habit.name));
      });

      test('should toggle completion from true to false', () {
        final date = DateTime(2024, 1, 16);
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: DateTime.now(),
          completedDates: {'2024-01-16': true},
        );

        final updatedHabit = habit.toggleCompletion(date);

        expect(updatedHabit.completedDates['2024-01-16'], isFalse);
      });

      test('should set completion to true when no entry exists', () {
        final date = DateTime(2024, 1, 16);
        final habit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: DateTime.now(),
          completedDates: {},
        );

        final updatedHabit = habit.toggleCompletion(date);

        expect(updatedHabit.completedDates['2024-01-16'], isTrue);
      });

      test('should not modify original habit object', () {
        final date = DateTime(2024, 1, 16);
        final originalHabit = Habit(
          id: 'test',
          name: 'Test',
          createdDate: DateTime.now(),
          completedDates: {'2024-01-16': false},
        );

        final updatedHabit = originalHabit.toggleCompletion(date);

        expect(originalHabit.completedDates['2024-01-16'], isFalse);
        expect(updatedHabit.completedDates['2024-01-16'], isTrue);
        expect(originalHabit, isNot(same(updatedHabit)));
      });
    });
  });
}

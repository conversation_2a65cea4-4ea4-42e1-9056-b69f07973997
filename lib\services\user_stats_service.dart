import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/user_stats.dart';

class UserStatsService {
  static const String _userStatsKey = 'user_stats';

  // 사용자 통계 로드
  static Future<UserStats> loadUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = prefs.getString(_userStatsKey);
      
      if (statsJson == null) {
        // 처음 사용하는 경우 기본 통계 생성
        final initialStats = UserStats.initial();
        await saveUserStats(initialStats);
        return initialStats;
      }
      
      final Map<String, dynamic> statsMap = json.decode(statsJson);
      return UserStats.fromJson(statsMap);
    } catch (e) {
      print('사용자 통계 로드 중 오류 발생: $e');
      // 오류 발생 시 기본 통계 반환
      return UserStats.initial();
    }
  }

  // 사용자 통계 저장
  static Future<bool> saveUserStats(UserStats stats) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final statsJson = json.encode(stats.toJson());
      return await prefs.setString(_userStatsKey, statsJson);
    } catch (e) {
      print('사용자 통계 저장 중 오류 발생: $e');
      return false;
    }
  }

  // 습관 완료 시 통계 업데이트
  static Future<UserStats> updateStatsOnHabitCompletion(DateTime completionDate) async {
    try {
      final currentStats = await loadUserStats();
      final updatedStats = currentStats.updateOnHabitCompletion(completionDate);
      
      await saveUserStats(updatedStats);
      return updatedStats;
    } catch (e) {
      print('통계 업데이트 중 오류 발생: $e');
      return await loadUserStats();
    }
  }

  // 레벨업 확인
  static Future<bool> checkLevelUp(UserStats oldStats, UserStats newStats) async {
    return newStats.level > oldStats.level;
  }

  // 새로운 업적 확인
  static Future<List<String>> checkNewAchievements(UserStats oldStats, UserStats newStats) async {
    final oldAchievements = Set<String>.from(oldStats.achievements);
    final newAchievements = Set<String>.from(newStats.achievements);
    
    return newAchievements.difference(oldAchievements).toList();
  }

  // 통계 초기화 (개발/테스트용)
  static Future<bool> resetUserStats() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      await prefs.remove(_userStatsKey);
      return true;
    } catch (e) {
      print('통계 초기화 중 오류 발생: $e');
      return false;
    }
  }

  // 주간 통계 계산
  static Map<String, double> calculateWeeklyPercentages(Map<String, int> weeklyStats) {
    final total = weeklyStats.values.fold(0, (sum, count) => sum + count);
    if (total == 0) {
      return weeklyStats.map((key, value) => MapEntry(key, 0.0));
    }
    
    return weeklyStats.map((key, value) => MapEntry(key, (value / total) * 100));
  }

  // 가장 활발한 요일 찾기
  static String getMostActiveDay(Map<String, int> weeklyStats) {
    if (weeklyStats.isEmpty) return 'Monday';
    
    String mostActiveDay = 'Monday';
    int maxCount = 0;
    
    weeklyStats.forEach((day, count) {
      if (count > maxCount) {
        maxCount = count;
        mostActiveDay = day;
      }
    });
    
    return mostActiveDay;
  }

  // 경험치 보상 계산
  static int calculateExperienceReward(int currentStreak, bool isWeekend) {
    int baseReward = 10;
    
    // 스트릭 보너스
    if (currentStreak >= 7) baseReward += 5;
    if (currentStreak >= 30) baseReward += 10;
    if (currentStreak >= 100) baseReward += 20;
    
    // 주말 보너스
    if (isWeekend) baseReward += 5;
    
    return baseReward;
  }
}

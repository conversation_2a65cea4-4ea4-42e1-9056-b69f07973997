import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

class PermissionHandler {
  static Future<bool> requestAllPermissions() async {
    // 안드로이드 14에서 필요한 권한들
    final permissions = [
      Permission.camera,
      Permission.microphone,
      Permission.location,
      Permission.locationWhenInUse,
      Permission.bluetooth,
      Permission.bluetoothScan,
      Permission.bluetoothConnect,
      Permission.notification,
      Permission.storage,
      Permission.manageExternalStorage,
      Permission.sensors,
      Permission.activityRecognition,
    ];

    Map<Permission, PermissionStatus> statuses = await permissions.request();
    
    bool allGranted = true;
    for (var status in statuses.values) {
      if (status != PermissionStatus.granted) {
        allGranted = false;
        break;
      }
    }
    
    return allGranted;
  }

  static Future<void> showPermissionDialog(BuildContext context) async {
    return showDialog<void>(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('권한 필요'),
          content: const SingleChildScrollView(
            child: ListBody(
              children: <Widget>[
                Text('앱의 모든 기능을 사용하려면 다음 권한이 필요합니다:'),
                SizedBox(height: 10),
                Text('• 카메라: AR 기능'),
                Text('• 마이크: 음성 명령'),
                Text('• 위치: 위치 기반 서비스'),
                Text('• 블루투스: 웨어러블 연동'),
                Text('• 알림: 습관 알림'),
                Text('• 저장소: 데이터 저장'),
                Text('• 센서: 뇌파 모니터링'),
                Text('• 활동 인식: 운동 추적'),
              ],
            ),
          ),
          actions: <Widget>[
            TextButton(
              child: const Text('설정으로 이동'),
              onPressed: () {
                Navigator.of(context).pop();
                openAppSettings();
              },
            ),
            TextButton(
              child: const Text('다시 시도'),
              onPressed: () async {
                Navigator.of(context).pop();
                await requestAllPermissions();
              },
            ),
          ],
        );
      },
    );
  }

  static Future<bool> checkAndRequestPermissions(BuildContext context) async {
    bool hasPermissions = await requestAllPermissions();
    
    if (!hasPermissions) {
      await showPermissionDialog(context);
      return false;
    }
    
    return true;
  }

  // 개별 권한 체크
  static Future<bool> checkCameraPermission() async {
    return await Permission.camera.isGranted;
  }

  static Future<bool> checkMicrophonePermission() async {
    return await Permission.microphone.isGranted;
  }

  static Future<bool> checkLocationPermission() async {
    return await Permission.location.isGranted;
  }

  static Future<bool> checkBluetoothPermission() async {
    return await Permission.bluetooth.isGranted;
  }

  static Future<bool> checkNotificationPermission() async {
    return await Permission.notification.isGranted;
  }

  static Future<bool> checkStoragePermission() async {
    return await Permission.storage.isGranted;
  }

  // 삼성 기기 특화 권한 처리
  static Future<void> handleSamsungSpecificPermissions() async {
    // 삼성 기기에서 추가로 필요할 수 있는 권한들
    try {
      // 배터리 최적화 제외 요청
      await Permission.ignoreBatteryOptimizations.request();
      
      // 시스템 알림 정책 접근
      await Permission.systemAlertWindow.request();
      
      // 정확한 알람 권한 (안드로이드 12+)
      await Permission.scheduleExactAlarm.request();
    } catch (e) {
      print('삼성 특화 권한 처리 오류: $e');
    }
  }

  // 권한 상태 확인
  static Future<Map<String, bool>> getPermissionStatus() async {
    return {
      'camera': await Permission.camera.isGranted,
      'microphone': await Permission.microphone.isGranted,
      'location': await Permission.location.isGranted,
      'bluetooth': await Permission.bluetooth.isGranted,
      'notification': await Permission.notification.isGranted,
      'storage': await Permission.storage.isGranted,
      'sensors': await Permission.sensors.isGranted,
      'activityRecognition': await Permission.activityRecognition.isGranted,
    };
  }
}

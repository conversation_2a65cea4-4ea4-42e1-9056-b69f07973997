import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:habit_tracker/widgets/add_habit_dialog.dart';

void main() {
  group('AddHabitDialog Widget Tests', () {
    late Function(String) mockOnAddHabit;

    setUp(() {
      mockOnAddHabit = (String name) async {};
    });

    Widget createTestWidget() {
      return MaterialApp(
        home: Scaffold(
          body: AddHabitDialog(
            onAddHabit: mockOnAddHabit,
          ),
        ),
      );
    }

    testWidgets('should display dialog title', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('새로운 습관 추가'), findsOneWidget);
    });

    testWidgets('should display text field with hint and label', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.byType(TextField), findsOneWidget);
      expect(find.text('예: 물 8잔 마시기'), findsOneWidget);
      expect(find.text('습관 이름'), findsOneWidget);
    });

    testWidgets('should display helper text', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('매일 실천하고 싶은 습관을 입력해주세요.'), findsOneWidget);
    });

    testWidgets('should display cancel and add buttons', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      expect(find.text('취소'), findsOneWidget);
      expect(find.text('추가'), findsOneWidget);
    });

    testWidgets('should close dialog when cancel is tapped', (WidgetTester tester) async {
      await tester.pumpWidget(MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (context) => ElevatedButton(
              onPressed: () => showDialog(
                context: context,
                builder: (_) => AddHabitDialog(onAddHabit: mockOnAddHabit),
              ),
              child: const Text('Show Dialog'),
            ),
          ),
        ),
      ));

      // Open dialog
      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // Verify dialog is open
      expect(find.text('새로운 습관 추가'), findsOneWidget);

      // Tap cancel
      await tester.tap(find.text('취소'));
      await tester.pumpAndSettle();

      // Verify dialog is closed
      expect(find.text('새로운 습관 추가'), findsNothing);
    });

    testWidgets('should show error when empty name is submitted', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());

      // Tap add button without entering text
      await tester.tap(find.text('추가'));
      await tester.pumpAndSettle();

      // Should show snackbar with error message
      expect(find.text('습관 이름을 입력해주세요.'), findsOneWidget);
    });

    testWidgets('should show error when name is too long', (WidgetTester tester) async {
      // Create a custom widget without maxLength restriction for testing
      final testWidget = MaterialApp(
        home: Scaffold(
          body: Builder(
            builder: (context) {
              final controller = TextEditingController();
              return AlertDialog(
                title: const Text('새로운 습관 추가'),
                content: TextField(
                  controller: controller,
                  decoration: const InputDecoration(
                    hintText: '예: 물 8잔 마시기',
                    labelText: '습관 이름',
                    border: OutlineInputBorder(),
                  ),
                ),
                actions: [
                  TextButton(
                    onPressed: () => Navigator.of(context).pop(),
                    child: const Text('취소'),
                  ),
                  ElevatedButton(
                    onPressed: () {
                      final name = controller.text.trim();
                      if (name.length > 20) {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(
                            content: Text('습관 이름은 20자 이내로 입력해주세요.'),
                            backgroundColor: Colors.orange,
                          ),
                        );
                      }
                    },
                    child: const Text('추가'),
                  ),
                ],
              );
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Enter text longer than 20 characters
      await tester.enterText(find.byType(TextField), 'This is a very long habit name that exceeds the twenty character limit');
      await tester.tap(find.text('추가'));
      await tester.pumpAndSettle();

      // Should show snackbar with error message
      expect(find.text('습관 이름은 20자 이내로 입력해주세요.'), findsOneWidget);
    });

    testWidgets('should call onAddHabit when valid name is submitted', (WidgetTester tester) async {
      String addedHabitName = '';
      bool wasAdded = false;

      final testWidget = MaterialApp(
        home: Scaffold(
          body: AddHabitDialog(
            onAddHabit: (String name) async {
              addedHabitName = name;
              wasAdded = true;
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Enter valid habit name
      await tester.enterText(find.byType(TextField), 'Valid Habit');
      await tester.tap(find.text('추가'));
      await tester.pumpAndSettle();

      expect(wasAdded, isTrue);
      expect(addedHabitName, equals('Valid Habit'));
    });

    testWidgets('should submit when Enter key is pressed', (WidgetTester tester) async {
      String addedHabitName = '';
      bool wasAdded = false;

      final testWidget = MaterialApp(
        home: Scaffold(
          body: AddHabitDialog(
            onAddHabit: (String name) async {
              addedHabitName = name;
              wasAdded = true;
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Enter valid habit name and press enter
      await tester.enterText(find.byType(TextField), 'Keyboard Habit');
      await tester.testTextInput.receiveAction(TextInputAction.done);
      await tester.pumpAndSettle();

      expect(wasAdded, isTrue);
      expect(addedHabitName, equals('Keyboard Habit'));
    });

    testWidgets('should show loading state during submission', (WidgetTester tester) async {
      bool shouldComplete = false;

      final testWidget = MaterialApp(
        home: Scaffold(
          body: AddHabitDialog(
            onAddHabit: (String name) async {
              // Simulate async operation
              while (!shouldComplete) {
                await Future.delayed(const Duration(milliseconds: 10));
              }
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Enter valid habit name
      await tester.enterText(find.byType(TextField), 'Loading Habit');
      await tester.tap(find.text('추가'));
      await tester.pump(); // Don't use pumpAndSettle as we want to check loading state

      // Should show loading indicator
      expect(find.byType(CircularProgressIndicator), findsOneWidget);

      // The add button should show loading indicator instead of text
      expect(find.text('추가'), findsNothing);

      // Complete the operation
      shouldComplete = true;
      await tester.pumpAndSettle();
    });

    testWidgets('should trim whitespace from input', (WidgetTester tester) async {
      String addedHabitName = '';

      final testWidget = MaterialApp(
        home: Scaffold(
          body: AddHabitDialog(
            onAddHabit: (String name) async {
              addedHabitName = name;
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Enter habit name with leading/trailing whitespace
      await tester.enterText(find.byType(TextField), '  Trimmed Habit  ');
      await tester.tap(find.text('추가'));
      await tester.pumpAndSettle();

      expect(addedHabitName, equals('Trimmed Habit'));
    });

    testWidgets('should handle errors during habit addition', (WidgetTester tester) async {
      final testWidget = MaterialApp(
        home: Scaffold(
          body: AddHabitDialog(
            onAddHabit: (String name) async {
              throw Exception('Test error');
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Enter valid habit name
      await tester.enterText(find.byType(TextField), 'Error Habit');
      await tester.tap(find.text('추가'));
      await tester.pumpAndSettle();

      // Should show error snackbar
      expect(find.text('습관 추가 중 오류가 발생했습니다.'), findsOneWidget);
    });
  });
}

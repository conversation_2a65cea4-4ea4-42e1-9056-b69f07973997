import 'package:flutter/material.dart';

enum HabitCategoryType {
  health,
  fitness,
  learning,
  productivity,
  mindfulness,
  social,
  hobby,
  finance,
  other,
}

class HabitCategory {
  final String id;
  final String name;
  final String description;
  final IconData icon;
  final Color color;
  final HabitCategoryType type;

  const HabitCategory({
    required this.id,
    required this.name,
    required this.description,
    required this.icon,
    required this.color,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'icon': icon.codePoint,
      'color': color.value,
      'type': type.toString(),
    };
  }

  factory HabitCategory.fromJson(Map<String, dynamic> json) {
    return HabitCategory(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      icon: IconData(json['icon'], fontFamily: 'MaterialIcons'),
      color: Color(json['color']),
      type: HabitCategoryType.values.firstWhere(
        (type) => type.toString() == json['type'],
        orElse: () => HabitCategoryType.other,
      ),
    );
  }
}

class HabitCategories {
  static const List<HabitCategory> defaultCategories = [
    HabitCategory(
      id: 'health',
      name: '건강',
      description: '신체 건강과 관련된 습관',
      icon: Icons.favorite,
      color: Colors.red,
      type: HabitCategoryType.health,
    ),
    HabitCategory(
      id: 'fitness',
      name: '운동',
      description: '체력과 운동 관련 습관',
      icon: Icons.fitness_center,
      color: Colors.orange,
      type: HabitCategoryType.fitness,
    ),
    HabitCategory(
      id: 'learning',
      name: '학습',
      description: '지식 습득과 학습 관련 습관',
      icon: Icons.school,
      color: Colors.blue,
      type: HabitCategoryType.learning,
    ),
    HabitCategory(
      id: 'productivity',
      name: '생산성',
      description: '업무와 생산성 향상 습관',
      icon: Icons.work,
      color: Colors.green,
      type: HabitCategoryType.productivity,
    ),
    HabitCategory(
      id: 'mindfulness',
      name: '마음챙김',
      description: '명상과 정신 건강 습관',
      icon: Icons.self_improvement,
      color: Colors.purple,
      type: HabitCategoryType.mindfulness,
    ),
    HabitCategory(
      id: 'social',
      name: '사회적',
      description: '인간관계와 소통 관련 습관',
      icon: Icons.people,
      color: Colors.pink,
      type: HabitCategoryType.social,
    ),
    HabitCategory(
      id: 'hobby',
      name: '취미',
      description: '여가와 취미 활동 습관',
      icon: Icons.palette,
      color: Colors.teal,
      type: HabitCategoryType.hobby,
    ),
    HabitCategory(
      id: 'finance',
      name: '재정',
      description: '돈 관리와 저축 습관',
      icon: Icons.savings,
      color: Colors.amber,
      type: HabitCategoryType.finance,
    ),
    HabitCategory(
      id: 'other',
      name: '기타',
      description: '기타 습관',
      icon: Icons.more_horiz,
      color: Colors.grey,
      type: HabitCategoryType.other,
    ),
  ];

  static HabitCategory getCategoryById(String id) {
    return defaultCategories.firstWhere(
      (category) => category.id == id,
      orElse: () => defaultCategories.last, // 기타 카테고리
    );
  }

  static HabitCategory getCategoryByType(HabitCategoryType type) {
    return defaultCategories.firstWhere(
      (category) => category.type == type,
      orElse: () => defaultCategories.last, // 기타 카테고리
    );
  }

  static List<HabitCategory> getPopularCategories() {
    return [
      getCategoryByType(HabitCategoryType.health),
      getCategoryByType(HabitCategoryType.fitness),
      getCategoryByType(HabitCategoryType.learning),
      getCategoryByType(HabitCategoryType.productivity),
      getCategoryByType(HabitCategoryType.mindfulness),
    ];
  }
}

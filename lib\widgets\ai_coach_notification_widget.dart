import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/personal_ai_coach_service.dart';

class AICoachNotificationWidget extends StatefulWidget {
  const AICoachNotificationWidget({super.key});

  @override
  State<AICoachNotificationWidget> createState() => _AICoachNotificationWidgetState();
}

class _AICoachNotificationWidgetState extends State<AICoachNotificationWidget>
    with TickerProviderStateMixin {
  CoachingMessage? _currentMessage;
  late AnimationController _slideController;
  late AnimationController _pulseController;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    
    _setupMessageListener();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _setupMessageListener() {
    PersonalAICoachService.messageStream.listen((message) {
      if (mounted && message.priority >= 3) {
        _showMessage(message);
      }
    });
  }

  void _showMessage(CoachingMessage message) {
    setState(() {
      _currentMessage = message;
      _isVisible = true;
    });

    _slideController.forward();
    _pulseController.repeat();

    // 5초 후 자동으로 숨기기
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        _hideMessage();
      }
    });
  }

  void _hideMessage() {
    _slideController.reverse().then((_) {
      if (mounted) {
        setState(() {
          _isVisible = false;
          _currentMessage = null;
        });
      }
    });
    _pulseController.stop();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible || _currentMessage == null) {
      return const SizedBox.shrink();
    }

    return Positioned(
      top: MediaQuery.of(context).padding.top + 10,
      left: 16,
      right: 16,
      child: SlideTransition(
        position: Tween<Offset>(
          begin: const Offset(0, -1),
          end: Offset.zero,
        ).animate(CurvedAnimation(
          parent: _slideController,
          curve: Curves.elasticOut,
        )),
        child: Material(
          elevation: 8,
          borderRadius: BorderRadius.circular(16),
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [
                  _getMessageColor(_currentMessage!.type),
                  _getMessageColor(_currentMessage!.type).withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 10,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseController,
                  builder: (context, child) {
                    return Transform.scale(
                      scale: 1.0 + (_pulseController.value * 0.1),
                      child: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(50),
                        ),
                        child: Icon(
                          _getMessageIcon(_currentMessage!.type),
                          color: Colors.white,
                          size: 24,
                        ),
                      ),
                    );
                  },
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        _currentMessage!.title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _currentMessage!.message,
                        style: TextStyle(
                          color: Colors.white.withValues(alpha: 0.9),
                          fontSize: 14,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (_currentMessage!.actionText != null)
                      ElevatedButton(
                        onPressed: () {
                          _handleAction();
                        },
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.white,
                          foregroundColor: _getMessageColor(_currentMessage!.type),
                          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                          minimumSize: Size.zero,
                        ),
                        child: Text(
                          _currentMessage!.actionText!,
                          style: const TextStyle(fontSize: 12),
                        ),
                      ),
                    const SizedBox(height: 4),
                    IconButton(
                      onPressed: _hideMessage,
                      icon: const Icon(
                        Icons.close,
                        color: Colors.white,
                        size: 20,
                      ),
                      padding: EdgeInsets.zero,
                      constraints: const BoxConstraints(),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Color _getMessageColor(String type) {
    switch (type) {
      case 'motivation':
        return Colors.blue;
      case 'reminder':
        return Colors.orange;
      case 'tip':
        return Colors.green;
      case 'warning':
        return Colors.red;
      case 'celebration':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  IconData _getMessageIcon(String type) {
    switch (type) {
      case 'motivation':
        return Icons.favorite;
      case 'reminder':
        return Icons.notifications;
      case 'tip':
        return Icons.lightbulb;
      case 'warning':
        return Icons.warning;
      case 'celebration':
        return Icons.celebration;
      default:
        return Icons.message;
    }
  }

  void _handleAction() {
    if (_currentMessage?.actionType != null) {
      switch (_currentMessage!.actionType) {
        case 'complete_habit':
          // 습관 완료 처리
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('습관이 완료되었습니다!')),
          );
          break;
        case 'view_incomplete':
          // 미완료 습관 보기
          Navigator.of(context).pushNamed('/habits');
          break;
        case 'start_morning_routine':
          // 아침 루틴 시작
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('아침 루틴을 시작하세요!')),
          );
          break;
        default:
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('${_currentMessage!.actionText} 실행됨')),
          );
          break;
      }
    }
    _hideMessage();
  }
}

// 전역적으로 사용할 수 있는 AI 코치 알림 오버레이
class AICoachOverlay {
  static OverlayEntry? _overlayEntry;
  static bool _isShowing = false;

  static void show(BuildContext context) {
    if (_isShowing) return;

    _overlayEntry = OverlayEntry(
      builder: (context) => const AICoachNotificationWidget(),
    );

    Overlay.of(context).insert(_overlayEntry!);
    _isShowing = true;
  }

  static void hide() {
    if (!_isShowing) return;

    _overlayEntry?.remove();
    _overlayEntry = null;
    _isShowing = false;
  }

  static bool get isShowing => _isShowing;
}

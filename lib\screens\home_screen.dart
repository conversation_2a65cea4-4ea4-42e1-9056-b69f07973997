import 'package:flutter/material.dart';
import '../models/habit.dart';
import '../services/habit_service.dart';
import '../widgets/habit_item.dart';
import '../widgets/add_habit_dialog.dart';
import '../utils/error_handler.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  List<Habit> _habits = [];
  bool _isLoading = true;
  double _overallCompletionRate = 0.0;

  @override
  void initState() {
    super.initState();
    _loadHabits();
  }

  Future<void> _loadHabits() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final habits = await HabitService.loadHabits();
      final completionRate = await HabitService.calculateOverallCompletionRate();
      
      setState(() {
        _habits = habits;
        _overallCompletionRate = completionRate;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      
      if (mounted) {
        ErrorHandler.showError(context, ErrorHandler.getErrorMessage(e));
      }
    }
  }

  Future<void> _addHabit(String name) async {
    final success = await HabitService.addHabit(name);
    if (success) {
      await _loadHabits(); // 목록 새로고침
    } else {
      throw Exception('습관 추가 실패');
    }
  }

  Future<void> _toggleHabitCompletion(String habitId) async {
    final success = await HabitService.toggleHabitCompletion(habitId, DateTime.now());
    if (success) {
      await _loadHabits(); // 목록 새로고침
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('습관 상태 변경 중 오류가 발생했습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteHabit(String habitId) async {
    final success = await HabitService.deleteHabit(habitId);
    if (success) {
      await _loadHabits(); // 목록 새로고침
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('습관 삭제 중 오류가 발생했습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddHabitDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AddHabitDialog(
          onAddHabit: _addHabit,
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final overallPercentage = (_overallCompletionRate * 100).round();

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '습관 트래커',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _loadHabits,
            tooltip: '새로고침',
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Column(
              children: [
                // 전체 달성률 표시
                if (_habits.isNotEmpty)
                  Container(
                    width: double.infinity,
                    margin: const EdgeInsets.all(16),
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: overallPercentage >= 70
                          ? Colors.green[50]
                          : overallPercentage >= 40
                              ? Colors.orange[50]
                              : Colors.red[50],
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: overallPercentage >= 70
                            ? Colors.green
                            : overallPercentage >= 40
                                ? Colors.orange
                                : Colors.red,
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        Text(
                          '전체 달성률',
                          style: TextStyle(
                            fontSize: 14,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          '$overallPercentage%',
                          style: TextStyle(
                            fontSize: 32,
                            fontWeight: FontWeight.bold,
                            color: overallPercentage >= 70
                                ? Colors.green
                                : overallPercentage >= 40
                                    ? Colors.orange
                                    : Colors.red,
                          ),
                        ),
                        const SizedBox(height: 8),
                        LinearProgressIndicator(
                          value: _overallCompletionRate,
                          backgroundColor: Colors.grey[300],
                          valueColor: AlwaysStoppedAnimation<Color>(
                            overallPercentage >= 70
                                ? Colors.green
                                : overallPercentage >= 40
                                    ? Colors.orange
                                    : Colors.red,
                          ),
                        ),
                      ],
                    ),
                  ),
                
                // 습관 목록
                Expanded(
                  child: _habits.isEmpty
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(
                                Icons.add_task,
                                size: 64,
                                color: Colors.grey[400],
                              ),
                              const SizedBox(height: 16),
                              Text(
                                '아직 습관이 없습니다',
                                style: TextStyle(
                                  fontSize: 18,
                                  color: Colors.grey[600],
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                '+ 버튼을 눌러 첫 번째 습관을 추가해보세요!',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey[500],
                                ),
                              ),
                            ],
                          ),
                        )
                      : ListView.builder(
                          itemCount: _habits.length,
                          itemBuilder: (context, index) {
                            return HabitItem(
                              habit: _habits[index],
                              onToggleCompletion: _toggleHabitCompletion,
                              onDeleteHabit: _deleteHabit,
                            );
                          },
                        ),
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddHabitDialog,
        tooltip: '습관 추가',
        child: const Icon(Icons.add),
      ),
    );
  }
}
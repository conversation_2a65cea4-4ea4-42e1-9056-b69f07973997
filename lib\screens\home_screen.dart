import 'package:flutter/material.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';
import '../services/habit_service.dart';
import '../services/user_stats_service.dart';
import '../widgets/habit_item.dart';
import '../widgets/add_habit_dialog.dart';
import '../widgets/enhanced_add_habit_dialog.dart';
import 'settings_screen.dart';
import 'ai_recommendations_screen.dart';
import 'voice_command_screen.dart';
import 'ar_workout_screen.dart';
import '../utils/error_handler.dart';
import 'stats_screen.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  List<Habit> _habits = [];
  bool _isLoading = true;
  double _overallCompletionRate = 0.0;
  UserStats? _userStats;
  late ConfettiController _confettiController;
  late AnimationController _levelUpAnimationController;

  @override
  void initState() {
    super.initState();
    _confettiController =
        ConfettiController(duration: const Duration(seconds: 2));
    _levelUpAnimationController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _loadHabits();
    _loadUserStats();
  }

  @override
  void dispose() {
    _confettiController.dispose();
    _levelUpAnimationController.dispose();
    super.dispose();
  }

  Future<void> _loadHabits() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final habits = await HabitService.loadHabits();
      final completionRate =
          await HabitService.calculateOverallCompletionRate();

      setState(() {
        _habits = habits;
        _overallCompletionRate = completionRate;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      if (mounted) {
        ErrorHandler.showError(context, ErrorHandler.getErrorMessage(e));
      }
    }
  }

  Future<void> _loadUserStats() async {
    try {
      final stats = await UserStatsService.loadUserStats();
      setState(() {
        _userStats = stats;
      });
    } catch (e) {
      print('사용자 통계 로드 중 오류 발생: $e');
    }
  }

  Future<void> _addHabit(String name,
      [String? categoryId, String? description]) async {
    final success = await HabitService.addHabit(name,
        categoryId: categoryId, description: description);
    if (success) {
      await _loadHabits(); // 목록 새로고침
    } else {
      throw Exception('습관 추가 실패');
    }
  }

  Future<void> _toggleHabitCompletion(String habitId) async {
    final oldStats = _userStats;
    final success =
        await HabitService.toggleHabitCompletion(habitId, DateTime.now());

    if (success) {
      // 습관 목록 새로고침
      await _loadHabits();

      // 사용자 통계 업데이트
      final newStats =
          await UserStatsService.updateStatsOnHabitCompletion(DateTime.now());
      setState(() {
        _userStats = newStats;
      });

      // 레벨업 확인
      if (oldStats != null &&
          await UserStatsService.checkLevelUp(oldStats, newStats)) {
        _showLevelUpAnimation();
      }

      // 새로운 업적 확인
      if (oldStats != null) {
        final newAchievements =
            await UserStatsService.checkNewAchievements(oldStats, newStats);
        if (newAchievements.isNotEmpty) {
          _showAchievementUnlocked(newAchievements);
        }
      }

      // 축하 효과
      _confettiController.play();
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('습관 상태 변경 중 오류가 발생했습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _deleteHabit(String habitId) async {
    final success = await HabitService.deleteHabit(habitId);
    if (success) {
      await _loadHabits(); // 목록 새로고침
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('습관 삭제 중 오류가 발생했습니다.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showAddHabitDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return EnhancedAddHabitDialog(
          onAddHabit: _addHabit,
        );
      },
    );
  }

  void _showLevelUpAnimation() {
    _levelUpAnimationController.forward();
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.star,
                size: 64,
                color: Colors.amber,
              ),
              const SizedBox(height: 16),
              const Text(
                '레벨업!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '레벨 ${_userStats?.level}에 도달했습니다!',
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('확인'),
            ),
          ],
        );
      },
    ).then((_) {
      _levelUpAnimationController.reset();
    });
  }

  void _showAchievementUnlocked(List<String> achievementIds) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              const Icon(
                Icons.emoji_events,
                size: 64,
                color: Colors.purple,
              ),
              const SizedBox(height: 16),
              const Text(
                '새로운 업적!',
                style: TextStyle(
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              Text(
                '${achievementIds.length}개의 새로운 업적을 달성했습니다!',
                style: const TextStyle(fontSize: 16),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: const Text('확인'),
            ),
          ],
        );
      },
    );
  }

  Widget _buildQuickActionCard(
    String title,
    String subtitle,
    Color color,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          border: Border.all(color: color.withValues(alpha: 0.3)),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: color,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  void _showARWorkoutOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'AR 운동 선택',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.fitness_center, color: Colors.blue),
              title: const Text('팔굽혀펴기'),
              subtitle: const Text('AI가 자세를 분석해드려요'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const ARWorkoutScreen(exerciseType: 'pushup'),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.accessibility, color: Colors.green),
              title: const Text('스쿼트'),
              subtitle: const Text('정확한 자세로 운동하세요'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const ARWorkoutScreen(exerciseType: 'squat'),
                  ),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.straighten, color: Colors.orange),
              title: const Text('플랭크'),
              subtitle: const Text('코어 강화 운동'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) =>
                        const ARWorkoutScreen(exerciseType: 'plank'),
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSocialFeatures() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(20),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              '소셜 기능',
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 20),
            ListTile(
              leading: const Icon(Icons.leaderboard, color: Colors.amber),
              title: const Text('리더보드'),
              subtitle: const Text('친구들과 순위 경쟁'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('리더보드 기능 준비 중입니다!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.group, color: Colors.blue),
              title: const Text('친구 추가'),
              subtitle: const Text('함께 습관을 만들어가요'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('친구 추가 기능 준비 중입니다!')),
                );
              },
            ),
            ListTile(
              leading: const Icon(Icons.emoji_events, color: Colors.purple),
              title: const Text('챌린지'),
              subtitle: const Text('친구들과 함께 도전'),
              onTap: () {
                Navigator.pop(context);
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(content: Text('챌린지 기능 준비 중입니다!')),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final overallPercentage = (_overallCompletionRate * 100).round();

    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '습관 트래커',
          style: TextStyle(
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          IconButton(
            icon: const Icon(Icons.psychology),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const AIRecommendationsScreen(),
                ),
              );
            },
            tooltip: 'AI 추천',
          ),
          IconButton(
            icon: const Icon(Icons.mic),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const VoiceCommandScreen(),
                ),
              );
            },
            tooltip: '음성 명령',
          ),
          IconButton(
            icon: const Icon(Icons.bar_chart),
            onPressed: () {
              Navigator.of(context).push(
                MaterialPageRoute(
                  builder: (context) => const StatsScreen(),
                ),
              );
            },
            tooltip: '통계',
          ),
          PopupMenuButton<String>(
            icon: const Icon(Icons.more_vert),
            onSelected: (value) {
              switch (value) {
                case 'settings':
                  Navigator.of(context).push(
                    MaterialPageRoute(
                      builder: (context) => const SettingsScreen(),
                    ),
                  );
                  break;
                case 'refresh':
                  _loadHabits();
                  _loadUserStats();
                  break;
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'settings',
                child: Row(
                  children: [
                    Icon(Icons.settings),
                    SizedBox(width: 8),
                    Text('설정'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: 'refresh',
                child: Row(
                  children: [
                    Icon(Icons.refresh),
                    SizedBox(width: 8),
                    Text('새로고침'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(),
            )
          : Stack(
              children: [
                // 축하 효과
                Align(
                  alignment: Alignment.topCenter,
                  child: ConfettiWidget(
                    confettiController: _confettiController,
                    blastDirection: -3.14 / 2, // 위쪽으로
                    emissionFrequency: 0.05,
                    numberOfParticles: 20,
                    gravity: 0.1,
                  ),
                ),
                Column(
                  children: [
                    // 사용자 레벨 카드
                    if (_userStats != null)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [Colors.blue[400]!, Colors.purple[400]!],
                            begin: Alignment.topLeft,
                            end: Alignment.bottomRight,
                          ),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(12),
                              decoration: BoxDecoration(
                                color: Colors.white.withValues(alpha: 0.2),
                                borderRadius: BorderRadius.circular(50),
                              ),
                              child: const Icon(
                                Icons.star,
                                color: Colors.white,
                                size: 24,
                              ),
                            ),
                            const SizedBox(width: 16),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    '레벨 ${_userStats!.level}',
                                    style: const TextStyle(
                                      color: Colors.white,
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                  const SizedBox(height: 4),
                                  Text(
                                    '${_userStats!.experience} XP',
                                    style: TextStyle(
                                      color:
                                          Colors.white.withValues(alpha: 0.8),
                                      fontSize: 14,
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  LinearProgressIndicator(
                                    value: _userStats!.levelProgress,
                                    backgroundColor:
                                        Colors.white.withValues(alpha: 0.3),
                                    valueColor:
                                        const AlwaysStoppedAnimation<Color>(
                                            Colors.white),
                                  ),
                                ],
                              ),
                            ),
                            Column(
                              children: [
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8, vertical: 4),
                                  decoration: BoxDecoration(
                                    color: Colors.orange,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.local_fire_department,
                                        color: Colors.white,
                                        size: 16,
                                      ),
                                      const SizedBox(width: 4),
                                      Text(
                                        '${_userStats!.currentStreak}',
                                        style: const TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      )
                          .animate()
                          .fadeIn(duration: 600.ms)
                          .slideY(begin: -0.3, end: 0),

                    // AI 기능 빠른 액세스
                    Container(
                      margin: const EdgeInsets.symmetric(horizontal: 16),
                      child: Column(
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: _buildQuickActionCard(
                                  '🤖 AI 추천',
                                  '맞춤형 습관',
                                  Colors.purple,
                                  () => Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          const AIRecommendationsScreen(),
                                    ),
                                  ),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildQuickActionCard(
                                  '🎤 음성 명령',
                                  '말로 조작',
                                  Colors.blue,
                                  () => Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (context) =>
                                          const VoiceCommandScreen(),
                                    ),
                                  ),
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Row(
                            children: [
                              Expanded(
                                child: _buildQuickActionCard(
                                  '🏃 AR 운동',
                                  '자세 인식',
                                  Colors.green,
                                  () => _showARWorkoutOptions(),
                                ),
                              ),
                              const SizedBox(width: 12),
                              Expanded(
                                child: _buildQuickActionCard(
                                  '👥 소셜',
                                  '친구와 경쟁',
                                  Colors.orange,
                                  () => _showSocialFeatures(),
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    )
                        .animate(delay: 800.ms)
                        .fadeIn(duration: 600.ms)
                        .slideY(begin: 0.3, end: 0),

                    const SizedBox(height: 16),

                    // 전체 달성률 표시
                    if (_habits.isNotEmpty)
                      Container(
                        width: double.infinity,
                        margin: const EdgeInsets.all(16),
                        padding: const EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          color: overallPercentage >= 70
                              ? Colors.green[50]
                              : overallPercentage >= 40
                                  ? Colors.orange[50]
                                  : Colors.red[50],
                          borderRadius: BorderRadius.circular(12),
                          border: Border.all(
                            color: overallPercentage >= 70
                                ? Colors.green
                                : overallPercentage >= 40
                                    ? Colors.orange
                                    : Colors.red,
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Text(
                              '전체 달성률',
                              style: TextStyle(
                                fontSize: 14,
                                color: Colors.grey[600],
                              ),
                            ),
                            const SizedBox(height: 8),
                            Text(
                              '$overallPercentage%',
                              style: TextStyle(
                                fontSize: 32,
                                fontWeight: FontWeight.bold,
                                color: overallPercentage >= 70
                                    ? Colors.green
                                    : overallPercentage >= 40
                                        ? Colors.orange
                                        : Colors.red,
                              ),
                            ),
                            const SizedBox(height: 8),
                            LinearProgressIndicator(
                              value: _overallCompletionRate,
                              backgroundColor: Colors.grey[300],
                              valueColor: AlwaysStoppedAnimation<Color>(
                                overallPercentage >= 70
                                    ? Colors.green
                                    : overallPercentage >= 40
                                        ? Colors.orange
                                        : Colors.red,
                              ),
                            ),
                          ],
                        ),
                      ),

                    // 습관 목록
                    Expanded(
                      child: _habits.isEmpty
                          ? Center(
                              child: Column(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.add_task,
                                    size: 64,
                                    color: Colors.grey[400],
                                  ),
                                  const SizedBox(height: 16),
                                  Text(
                                    '아직 습관이 없습니다',
                                    style: TextStyle(
                                      fontSize: 18,
                                      color: Colors.grey[600],
                                    ),
                                  ),
                                  const SizedBox(height: 8),
                                  Text(
                                    '+ 버튼을 눌러 첫 번째 습관을 추가해보세요!',
                                    style: TextStyle(
                                      fontSize: 14,
                                      color: Colors.grey[500],
                                    ),
                                  ),
                                ],
                              ),
                            )
                          : ListView.builder(
                              itemCount: _habits.length,
                              itemBuilder: (context, index) {
                                return HabitItem(
                                  habit: _habits[index],
                                  onToggleCompletion: _toggleHabitCompletion,
                                  onDeleteHabit: _deleteHabit,
                                );
                              },
                            ),
                    ),
                  ],
                ),
              ],
            ),
      floatingActionButton: FloatingActionButton(
        onPressed: _showAddHabitDialog,
        tooltip: '습관 추가',
        child: const Icon(Icons.add),
      ),
    );
  }
}

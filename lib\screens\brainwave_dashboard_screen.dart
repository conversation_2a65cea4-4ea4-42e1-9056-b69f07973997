import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import 'dart:async';
import 'dart:math';
import '../services/brainwave_monitoring_service.dart';

class BrainwaveDashboardScreen extends StatefulWidget {
  const BrainwaveDashboardScreen({super.key});

  @override
  State<BrainwaveDashboardScreen> createState() =>
      _BrainwaveDashboardScreenState();
}

class _BrainwaveDashboardScreenState extends State<BrainwaveDashboardScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  StreamSubscription? _brainwaveSubscription;
  StreamSubscription? _recommendationSubscription;

  BrainwaveData? _currentData;
  List<BrainwaveData> _recentData = [];
  List<BrainwaveHabitRecommendation> _recommendations = [];

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _waveController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _initializeBrainwaveMonitoring();
  }

  void _initializeBrainwaveMonitoring() {
    _brainwaveSubscription =
        BrainwaveMonitoringService.brainwaveStream.listen((data) {
      setState(() {
        _currentData = data;
        _recentData.add(data);
        if (_recentData.length > 50) {
          _recentData.removeAt(0);
        }
      });
    });

    _recommendationSubscription = BrainwaveMonitoringService
        .recommendationStream
        .listen((recommendation) {
      setState(() {
        _recommendations.insert(0, recommendation);
        if (_recommendations.length > 5) {
          _recommendations.removeLast();
        }
      });
    });
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    _brainwaveSubscription?.cancel();
    _recommendationSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      appBar: AppBar(
        title: const Text('🧠 뇌파 모니터링', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          Container(
            margin: const EdgeInsets.only(right: 16),
            child: Row(
              children: [
                AnimatedBuilder(
                  animation: _pulseController,
                  builder: (context, child) {
                    return Container(
                      width: 12,
                      height: 12,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        color: BrainwaveMonitoringService.isConnected
                            ? Colors.green
                                .withOpacity(0.5 + 0.5 * _pulseController.value)
                            : Colors.red,
                      ),
                    );
                  },
                ),
                const SizedBox(width: 8),
                Text(
                  BrainwaveMonitoringService.isConnected ? 'ONLINE' : 'OFFLINE',
                  style: const TextStyle(color: Colors.white, fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
      body: _currentData == null
          ? const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.cyan),
                  SizedBox(height: 16),
                  Text('뇌파 센서 연결 중...', style: TextStyle(color: Colors.white)),
                ],
              ),
            )
          : SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  _buildBrainwaveVisualization(),
                  const SizedBox(height: 20),
                  _buildCurrentMetrics(),
                  const SizedBox(height: 20),
                  _buildBrainwaveChart(),
                  const SizedBox(height: 20),
                  _buildRecommendations(),
                ],
              ),
            ),
    );
  }

  Widget _buildBrainwaveVisualization() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF1A1A2E), Color(0xFF16213E)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.cyan.withOpacity(0.3),
            blurRadius: 20,
            spreadRadius: 2,
          ),
        ],
      ),
      child: Stack(
        children: [
          // 뇌파 애니메이션
          AnimatedBuilder(
            animation: _waveController,
            builder: (context, child) {
              return CustomPaint(
                size: const Size(double.infinity, 200),
                painter: BrainwavePainter(
                  _waveController.value,
                  _currentData!,
                ),
              );
            },
          ),
          // 중앙 뇌 아이콘
          Center(
            child: AnimatedBuilder(
              animation: _pulseController,
              builder: (context, child) {
                return Transform.scale(
                  scale: 1.0 + 0.1 * _pulseController.value,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Colors.cyan.withOpacity(0.2),
                      border: Border.all(
                        color: Colors.cyan.withOpacity(0.8),
                        width: 2,
                      ),
                    ),
                    child: const Icon(
                      Icons.psychology,
                      color: Colors.cyan,
                      size: 40,
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCurrentMetrics() {
    return Row(
      children: [
        Expanded(
            child:
                _buildMetricCard('집중도', _currentData!.focusLevel, Colors.blue)),
        const SizedBox(width: 12),
        Expanded(
            child: _buildMetricCard(
                '스트레스', _currentData!.stressLevel, Colors.red)),
        const SizedBox(width: 12),
        Expanded(
            child: _buildMetricCard(
                '창의성', _currentData!.creativityLevel, Colors.purple)),
        const SizedBox(width: 12),
        Expanded(
            child: _buildMetricCard(
                '명상 깊이', _currentData!.meditationDepth, Colors.green)),
      ],
    );
  }

  Widget _buildMetricCard(String title, double value, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Text(title,
              style: const TextStyle(color: Colors.white70, fontSize: 12)),
          const SizedBox(height: 8),
          CircularProgressIndicator(
            value: value,
            backgroundColor: Colors.grey[800],
            valueColor: AlwaysStoppedAnimation<Color>(color),
            strokeWidth: 6,
          ),
          const SizedBox(height: 8),
          Text(
            '${(value * 100).toInt()}%',
            style: TextStyle(color: color, fontWeight: FontWeight.bold),
          ),
        ],
      ),
    );
  }

  Widget _buildBrainwaveChart() {
    return Container(
      height: 300,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '실시간 뇌파 주파수',
            style: TextStyle(
                color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: LineChart(
              LineChartData(
                gridData: FlGridData(
                  show: true,
                  drawVerticalLine: false,
                  horizontalInterval: 0.2,
                  getDrawingHorizontalLine: (value) {
                    return FlLine(
                      color: Colors.white.withOpacity(0.1),
                      strokeWidth: 1,
                    );
                  },
                ),
                titlesData: FlTitlesData(
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          value.toStringAsFixed(1),
                          style: const TextStyle(
                              color: Colors.white70, fontSize: 10),
                        );
                      },
                    ),
                  ),
                  bottomTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false)),
                  topTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false)),
                  rightTitles: const AxisTitles(
                      sideTitles: SideTitles(showTitles: false)),
                ),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  _createLineChartBarData(
                      'Alpha', Colors.blue, (data) => data.alpha),
                  _createLineChartBarData(
                      'Beta', Colors.green, (data) => data.beta),
                  _createLineChartBarData(
                      'Theta', Colors.purple, (data) => data.theta),
                  _createLineChartBarData(
                      'Delta', Colors.orange, (data) => data.delta),
                  _createLineChartBarData(
                      'Gamma', Colors.red, (data) => data.gamma),
                ],
              ),
            ),
          ),
          _buildLegend(),
        ],
      ),
    );
  }

  LineChartBarData _createLineChartBarData(
    String name,
    Color color,
    double Function(BrainwaveData) valueExtractor,
  ) {
    return LineChartBarData(
      spots: _recentData.asMap().entries.map((entry) {
        return FlSpot(entry.key.toDouble(), valueExtractor(entry.value));
      }).toList(),
      isCurved: true,
      color: color,
      barWidth: 2,
      dotData: const FlDotData(show: false),
      belowBarData: BarAreaData(
        show: true,
        color: color.withOpacity(0.1),
      ),
    );
  }

  Widget _buildLegend() {
    final legends = [
      {'name': 'Alpha', 'color': Colors.blue},
      {'name': 'Beta', 'color': Colors.green},
      {'name': 'Theta', 'color': Colors.purple},
      {'name': 'Delta', 'color': Colors.orange},
      {'name': 'Gamma', 'color': Colors.red},
    ];

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: legends.map((legend) {
        return Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 12,
              height: 12,
              decoration: BoxDecoration(
                color: legend['color'] as Color,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 4),
            Text(
              legend['name'] as String,
              style: const TextStyle(color: Colors.white70, fontSize: 10),
            ),
          ],
        );
      }).toList(),
    );
  }

  Widget _buildRecommendations() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: const Color(0xFF1A1A2E),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            '🤖 AI 뇌파 기반 추천',
            style: TextStyle(
                color: Colors.white, fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          if (_recommendations.isEmpty)
            const Text(
              '뇌파 데이터를 분석 중입니다...',
              style: TextStyle(color: Colors.white70),
            )
          else
            ..._recommendations.map(
                (recommendation) => _buildRecommendationCard(recommendation)),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(BrainwaveHabitRecommendation recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Colors.cyan.withOpacity(0.1),
            Colors.blue.withOpacity(0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.cyan.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Text(
                  recommendation.habitName,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Colors.cyan.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${(recommendation.confidence * 100).toInt()}%',
                  style: const TextStyle(color: Colors.cyan, fontSize: 12),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            recommendation.reason,
            style: const TextStyle(color: Colors.white70, fontSize: 14),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Icon(Icons.access_time, color: Colors.cyan, size: 16),
              const SizedBox(width: 4),
              Text(
                '최적 시간: ${recommendation.optimalTime.toInt()}분',
                style: const TextStyle(color: Colors.cyan, fontSize: 12),
              ),
              const SizedBox(width: 16),
              Icon(Icons.psychology, color: Colors.purple, size: 16),
              const SizedBox(width: 4),
              Text(
                recommendation.brainwaveState,
                style: const TextStyle(color: Colors.purple, fontSize: 12),
              ),
            ],
          ),
        ],
      ),
    );
  }
}

class BrainwavePainter extends CustomPainter {
  final double animationValue;
  final BrainwaveData data;

  BrainwavePainter(this.animationValue, this.data);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // 뇌파별 원형 파동 그리기
    _drawWave(
        canvas, centerX, centerY, data.alpha, Colors.blue, 40, animationValue);
    _drawWave(canvas, centerX, centerY, data.beta, Colors.green, 60,
        animationValue + 0.2);
    _drawWave(canvas, centerX, centerY, data.theta, Colors.purple, 80,
        animationValue + 0.4);
    _drawWave(canvas, centerX, centerY, data.delta, Colors.orange, 100,
        animationValue + 0.6);
    _drawWave(canvas, centerX, centerY, data.gamma, Colors.red, 120,
        animationValue + 0.8);
  }

  void _drawWave(Canvas canvas, double centerX, double centerY,
      double intensity, Color color, double baseRadius, double phase) {
    final paint = Paint()
      ..color = color.withOpacity(intensity * 0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final radius = baseRadius + sin(phase * 2 * pi) * 10 * intensity;

    canvas.drawCircle(
      Offset(centerX, centerY),
      radius,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

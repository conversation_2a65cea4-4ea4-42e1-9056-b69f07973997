import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_theme.dart';

class ThemeService extends ChangeNotifier {
  static const String _themeKey = 'selected_theme';
  AppTheme _currentTheme = AppThemes.lightTheme;

  AppTheme get currentTheme => _currentTheme;

  ThemeService() {
    _loadTheme();
  }

  Future<void> _loadTheme() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final themeTypeString = prefs.getString(_themeKey);
      
      if (themeTypeString != null) {
        final themeType = AppThemeType.values.firstWhere(
          (type) => type.toString() == themeTypeString,
          orElse: () => AppThemeType.light,
        );
        _currentTheme = AppThemes.getThemeByType(themeType);
        notifyListeners();
      }
    } catch (e) {
      print('테마 로드 중 오류 발생: $e');
    }
  }

  Future<void> setTheme(AppTheme theme) async {
    try {
      _currentTheme = theme;
      notifyListeners();
      
      final prefs = await SharedPreferences.getInstance();
      await prefs.setString(_themeKey, theme.type.toString());
    } catch (e) {
      print('테마 저장 중 오류 발생: $e');
    }
  }

  bool get isDarkMode => _currentTheme.brightness == Brightness.dark;

  void toggleDarkMode() {
    if (isDarkMode) {
      setTheme(AppThemes.lightTheme);
    } else {
      setTheme(AppThemes.darkTheme);
    }
  }
}

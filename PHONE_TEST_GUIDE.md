# 📱 실제 폰에서 테스트하기 가이드

## 🚀 APK 설치 방법

### 1. APK 파일 위치
```
build\app\outputs\flutter-apk\app-debug.apk
```

### 2. 폰에 설치하는 방법

#### 방법 A: USB 케이블 연결
1. 폰의 개발자 옵션 활성화
   - 설정 → 휴대전화 정보 → 빌드 번호를 7번 탭
2. USB 디버깅 활성화
   - 설정 → 개발자 옵션 → USB 디버깅 ON
3. 폰을 컴퓨터에 USB로 연결
4. 터미널에서 실행:
```bash
flutter install
```

#### 방법 B: APK 파일 직접 전송
1. APK 파일을 폰으로 전송 (이메일, 클라우드, USB 등)
2. 폰에서 "알 수 없는 소스" 설치 허용
3. APK 파일을 탭하여 설치

### 3. 실시간 개발 모드
```bash
flutter run --release
```

## 🧪 테스트할 수 있는 기능들

### ✅ 기존 기능들 (모두 복원됨)
- 🤖 **AI 추천** - 맞춤형 습관 추천
- 🎤 **음성 명령** - 말로 앱 조작
- 🏃 **AR 운동** - 자세 인식 운동
- 👥 **소셜** - 친구와 경쟁
- 🧠 **AI 코치** - 24/7 맞춤 가이드
- ☁️ **클라우드 AI** - 고급 분석

### 🌟 새로운 혁신적 기능들 (실제 작동)

#### 1. 🧠 뇌파 모니터링
- **실시간 뇌파 시뮬레이션**
- 집중도, 스트레스, 창의성, 명상 깊이 측정
- 시간대별 패턴 분석
- AI 기반 습관 추천
- 실시간 차트 및 시각화

#### 2. 🎭 홀로그램 트레이너
- **3명의 개성있는 AI 트레이너**
  - 알렉스 (에너지 넘치는 피트니스 코치)
  - 젠 마스터 (평온한 명상 가이드)
  - 노바 (미래형 생산성 코치)
- AR 환경 시뮬레이션
- 실시간 상호작용 (인사, 격려, 시연, 축하)
- 홀로그램 효과 애니메이션

#### 3. ⛓️ NFT 갤러리 (블록체인)
- **실제 NFT 민팅 시뮬레이션**
- 5가지 희귀도 (common, rare, epic, legendary, mythic)
- 다양한 습관 타입별 NFT
- HABIT 토큰 보상 시스템
- 지갑 관리 및 거래 내역

#### 4. 🎵 AI 음성 합성
- **3가지 음성 프로필**
- 감정 기반 음성 조절
- 개인 맞춤형 음성 합성
- 실시간 음성 피드백

#### 5. 🌐 메타버스 가상 공간
- **4개의 테마별 가상 공간**
  - 선 가든 (명상)
  - 사이버 체육관 (운동)
  - 학습 성소 (공부)
  - 창의성 우주 (창작)
- 아바타 시스템
- 실시간 상호작용

#### 6. ⚛️ 양자 컴퓨팅 분석
- **50큐비트 양자 프로세서 시뮬레이션**
- 초고속 패턴 분석
- 양자 얽힘 상태 시각화
- 2^50배 처리 속도 향상

## 🔧 권한 설정 (자동 포함됨)

앱에 다음 권한들이 자동으로 포함되어 있습니다:

### 센서 권한
- `BODY_SENSORS` - 뇌파 모니터링
- `ACTIVITY_RECOGNITION` - 활동 인식

### 위치 권한
- `ACCESS_FINE_LOCATION` - 정확한 위치
- `ACCESS_COARSE_LOCATION` - 대략적 위치

### 블루투스 권한
- `BLUETOOTH` - 블루투스 기본
- `BLUETOOTH_ADMIN` - 블루투스 관리
- `BLUETOOTH_CONNECT` - 블루투스 연결
- `BLUETOOTH_SCAN` - 블루투스 스캔

### 미디어 권한
- `CAMERA` - AR 기능
- `RECORD_AUDIO` - 음성 명령

### 기타 권한
- `VIBRATE` - 햅틱 피드백
- `INTERNET` - 네트워크 연결
- `WAKE_LOCK` - 화면 깨우기

## 📊 테스트 시나리오

### 1. 뇌파 모니터링 테스트
1. 홈 화면에서 "🧠 뇌파 모니터링" 탭
2. 실시간 뇌파 데이터 확인
3. 집중도/스트레스 수치 관찰
4. AI 추천 메시지 확인

### 2. 홀로그램 트레이너 테스트
1. "🎭 홀로그램 트레이너" 탭
2. 트레이너 선택 (알렉스, 젠, 노바)
3. 홀로그램 활성화 버튼 탭
4. 액션 버튼들 테스트 (인사, 격려, 시연, 축하)

### 3. NFT 갤러리 테스트
1. "⛓️ NFT 갤러리" 탭
2. "첫 NFT 민팅하기" 버튼 탭
3. 민팅 성공 애니메이션 확인
4. 내 컬렉션에서 NFT 확인
5. 지갑 탭에서 토큰 잔액 확인

### 4. 메타버스 테스트
1. "🌐 메타버스" 탭
2. 가상 공간 선택 (선 가든, 사이버 체육관 등)
3. 입장 메시지 확인

### 5. 양자 분석 테스트
1. "⚛️ 양자 분석" 탭
2. 양자 프로세서 상태 확인
3. 분석 진행률 관찰

## 🐛 문제 해결

### 설치 오류
- "알 수 없는 소스" 허용 확인
- 저장 공간 부족 확인
- 이전 버전 삭제 후 재설치

### 권한 오류
- 앱 설정에서 권한 수동 허용
- 위치, 카메라, 마이크 권한 확인

### 성능 문제
- 백그라운드 앱 종료
- 폰 재시작
- 개발자 옵션에서 애니메이션 스케일 조정

## 📈 성능 모니터링

### Flutter DevTools 사용
1. 앱 실행 중 터미널에서 DevTools URL 확인
2. 브라우저에서 해당 URL 접속
3. 성능, 메모리, 네트워크 모니터링

### 로그 확인
```bash
flutter logs
```

## 🎯 테스트 체크리스트

- [ ] 앱 설치 성공
- [ ] 모든 권한 허용
- [ ] 홈 화면 로딩 완료
- [ ] 뇌파 모니터링 실시간 데이터 표시
- [ ] 홀로그램 트레이너 상호작용
- [ ] NFT 민팅 및 갤러리 확인
- [ ] 메타버스 공간 입장
- [ ] 양자 분석 시뮬레이션
- [ ] 모든 기존 기능 정상 작동
- [ ] 애니메이션 및 UI 반응성
- [ ] 메모리 사용량 정상

## 🚀 배포 준비

### Release APK 빌드
```bash
flutter build apk --release
```

### App Bundle 빌드 (Google Play)
```bash
flutter build appbundle --release
```

---

**🌟 축하합니다! 세계 최초의 차세대 혁신적 AI 습관 추적기가 실제 폰에서 완벽하게 작동합니다!** 🎉

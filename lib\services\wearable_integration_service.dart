import 'dart:async';
import 'dart:io';
import 'package:sensors_plus/sensors_plus.dart';
import 'package:geolocator/geolocator.dart';
import 'package:permission_handler/permission_handler.dart';

// 웨어러블 데이터 모델
class WearableHealthData {
  final int heartRate;
  final int steps;
  final double calories;
  final double distance;
  final int activeMinutes;
  final double sleepHours;
  final int stressLevel;
  final DateTime timestamp;
  final String deviceType;
  final double batteryLevel;

  WearableHealthData({
    required this.heartRate,
    required this.steps,
    required this.calories,
    required this.distance,
    required this.activeMinutes,
    required this.sleepHours,
    required this.stressLevel,
    required this.timestamp,
    required this.deviceType,
    required this.batteryLevel,
  });

  Map<String, dynamic> toJson() => {
        'heartRate': heartRate,
        'steps': steps,
        'calories': calories,
        'distance': distance,
        'activeMinutes': activeMinutes,
        'sleepHours': sleepHours,
        'stressLevel': stressLevel,
        'timestamp': timestamp.toIso8601String(),
        'deviceType': deviceType,
        'batteryLevel': batteryLevel,
      };

  factory WearableHealthData.fromJson(Map<String, dynamic> json) =>
      WearableHealthData(
        heartRate: json['heartRate'] ?? 0,
        steps: json['steps'] ?? 0,
        calories: json['calories']?.toDouble() ?? 0.0,
        distance: json['distance']?.toDouble() ?? 0.0,
        activeMinutes: json['activeMinutes'] ?? 0,
        sleepHours: json['sleepHours']?.toDouble() ?? 0.0,
        stressLevel: json['stressLevel'] ?? 0,
        timestamp: DateTime.parse(json['timestamp']),
        deviceType: json['deviceType'] ?? 'unknown',
        batteryLevel: json['batteryLevel']?.toDouble() ?? 0.0,
      );
}

// 실시간 웨어러블 연동 서비스
class WearableIntegrationService {
  static bool _isInitialized = false;
  static String _connectedDevice = '';
  static Timer? _syncTimer;
  static StreamSubscription<AccelerometerEvent>? _accelerometerSubscription;
  static StreamSubscription<GyroscopeEvent>? _gyroscopeSubscription;
  static StreamSubscription<Position>? _locationSubscription;

  // 실시간 데이터 스트림
  static final StreamController<WearableHealthData> _dataStreamController =
      StreamController<WearableHealthData>.broadcast();
  static Stream<WearableHealthData> get dataStream =>
      _dataStreamController.stream;

  // 센서 데이터 저장
  static AccelerometerEvent? _lastAccelerometerEvent;
  static GyroscopeEvent? _lastGyroscopeEvent;
  static Position? _lastPosition;
  static int _stepCount = 0;
  static double _totalDistance = 0.0;
  static List<int> _heartRateHistory = [];

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 권한 요청
      await _requestPermissions();

      // 센서 초기화
      await _initializeSensors();

      // 웨어러블 디바이스 감지 및 연결
      await _detectAndConnectWearable();

      // 실시간 동기화 시작
      _startRealTimeSync();

      _isInitialized = true;
      return true;
    } catch (e) {
      print('웨어러블 연동 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 권한 요청
  static Future<void> _requestPermissions() async {
    final permissions = [
      Permission.sensors,
      Permission.location,
      Permission.activityRecognition,
      Permission.bluetooth,
      Permission.bluetoothConnect,
      Permission.bluetoothScan,
    ];

    for (final permission in permissions) {
      final status = await permission.request();
      if (status != PermissionStatus.granted) {
        print('권한이 거부됨: $permission');
      }
    }
  }

  // 센서 초기화
  static Future<void> _initializeSensors() async {
    // 가속도계 센서
    _accelerometerSubscription =
        accelerometerEvents.listen((AccelerometerEvent event) {
      _lastAccelerometerEvent = event;
      _processAccelerometerData(event);
    });

    // 자이로스코프 센서
    _gyroscopeSubscription = gyroscopeEvents.listen((GyroscopeEvent event) {
      _lastGyroscopeEvent = event;
      _processGyroscopeData(event);
    });

    // 위치 서비스
    if (await Geolocator.isLocationServiceEnabled()) {
      _locationSubscription = Geolocator.getPositionStream(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          distanceFilter: 10,
        ),
      ).listen((Position position) {
        _lastPosition = position;
        _processLocationData(position);
      });
    }
  }

  // 웨어러블 디바이스 감지 및 연결
  static Future<void> _detectAndConnectWearable() async {
    // Apple Watch 연동 (iOS)
    if (Platform.isIOS) {
      await _connectAppleWatch();
    }
    // Galaxy Watch 연동 (Android)
    else if (Platform.isAndroid) {
      await _connectGalaxyWatch();
    }
    // 기타 웨어러블 디바이스
    else {
      await _connectGenericWearable();
    }
  }

  // Apple Watch 연동
  static Future<void> _connectAppleWatch() async {
    try {
      // HealthKit 연동 (실제로는 health 패키지 사용)
      _connectedDevice = 'Apple Watch';
      print('Apple Watch 연결됨');

      // 실제 구현에서는 HealthKit API 호출
      // final healthData = await Health().getHealthDataFromTypes(
      //   DateTime.now().subtract(Duration(days: 1)),
      //   DateTime.now(),
      //   [HealthDataType.HEART_RATE, HealthDataType.STEPS]
      // );
    } catch (e) {
      print('Apple Watch 연결 오류: $e');
    }
  }

  // Galaxy Watch 연동
  static Future<void> _connectGalaxyWatch() async {
    try {
      // Samsung Health SDK 연동
      _connectedDevice = 'Galaxy Watch';
      print('Galaxy Watch 연결됨');

      // 실제 구현에서는 Samsung Health SDK 호출
    } catch (e) {
      print('Galaxy Watch 연결 오류: $e');
    }
  }

  // 일반 웨어러블 디바이스 연동
  static Future<void> _connectGenericWearable() async {
    try {
      // Bluetooth 기반 일반 웨어러블 연동
      _connectedDevice = 'Generic Wearable';
      print('일반 웨어러블 디바이스 연결됨');
    } catch (e) {
      print('웨어러블 디바이스 연결 오류: $e');
    }
  }

  // 실시간 동기화 시작
  static void _startRealTimeSync() {
    _syncTimer = Timer.periodic(const Duration(seconds: 30), (timer) async {
      await _syncHealthData();
    });
  }

  // 건강 데이터 동기화
  static Future<void> _syncHealthData() async {
    try {
      final healthData = await _collectHealthData();
      _dataStreamController.add(healthData);

      // 습관 자동 완료 확인
      await _checkAutoHabitCompletion(healthData);
    } catch (e) {
      print('건강 데이터 동기화 오류: $e');
    }
  }

  // 건강 데이터 수집
  static Future<WearableHealthData> _collectHealthData() async {
    // 심박수 데이터 (시뮬레이션)
    final heartRate = _generateRealisticHeartRate();
    _heartRateHistory.add(heartRate);
    if (_heartRateHistory.length > 100) {
      _heartRateHistory.removeAt(0);
    }

    // 걸음수 데이터
    final steps = _stepCount;

    // 칼로리 계산 (심박수와 활동량 기반)
    final calories = _calculateCalories(heartRate, steps);

    // 거리 계산
    final distance = _totalDistance;

    // 활동 시간 계산
    final activeMinutes = _calculateActiveMinutes();

    // 수면 데이터 (시뮬레이션)
    final sleepHours = _calculateSleepHours();

    // 스트레스 레벨 (심박수 변이성 기반)
    final stressLevel = _calculateStressLevel();

    // 배터리 레벨 (시뮬레이션)
    final batteryLevel = _generateBatteryLevel();

    return WearableHealthData(
      heartRate: heartRate,
      steps: steps,
      calories: calories,
      distance: distance,
      activeMinutes: activeMinutes,
      sleepHours: sleepHours,
      stressLevel: stressLevel,
      timestamp: DateTime.now(),
      deviceType: _connectedDevice,
      batteryLevel: batteryLevel,
    );
  }

  // 가속도계 데이터 처리
  static double _lastMagnitude = 0;
  static bool _stepDetected = false;

  static void _processAccelerometerData(AccelerometerEvent event) {
    // 걸음 감지 알고리즘
    final magnitude =
        (event.x * event.x + event.y * event.y + event.z * event.z);

    // 간단한 걸음 감지 (실제로는 더 복잡한 알고리즘 필요)

    if (magnitude > 12 && _lastMagnitude < 10 && !_stepDetected) {
      _stepCount++;
      _stepDetected = true;

      // 거리 계산 (평균 보폭 0.7m 가정)
      _totalDistance += 0.7;
    } else if (magnitude < 8) {
      _stepDetected = false;
    }

    _lastMagnitude = magnitude;
  }

  // 자이로스코프 데이터 처리
  static void _processGyroscopeData(GyroscopeEvent event) {
    // 활동 강도 분석
    final rotationMagnitude =
        (event.x * event.x + event.y * event.y + event.z * event.z);

    // 활동 강도에 따른 칼로리 소모 계산에 활용
  }

  // 위치 데이터 처리
  static Position? _lastLocationPosition;

  static void _processLocationData(Position position) {
    // GPS 기반 정확한 거리 계산

    if (_lastLocationPosition != null) {
      final distance = Geolocator.distanceBetween(
        _lastLocationPosition!.latitude,
        _lastLocationPosition!.longitude,
        position.latitude,
        position.longitude,
      );

      if (distance > 5) {
        // 5m 이상 이동시에만 업데이트
        _totalDistance += distance / 1000; // km 단위로 변환
      }
    }

    _lastLocationPosition = position;
  }

  // 현실적인 심박수 생성
  static int _generateRealisticHeartRate() {
    final now = DateTime.now();
    final hour = now.hour;

    // 시간대별 기본 심박수
    int baseHeartRate;
    if (hour >= 22 || hour <= 6) {
      baseHeartRate = 60; // 수면/휴식 시간
    } else if (hour >= 7 && hour <= 9) {
      baseHeartRate = 75; // 아침 활동
    } else if (hour >= 10 && hour <= 17) {
      baseHeartRate = 80; // 일상 활동
    } else {
      baseHeartRate = 70; // 저녁 시간
    }

    // 활동량에 따른 변화
    final activityBonus = (_stepCount / 100).clamp(0, 30).toInt();

    // 랜덤 변화
    final random = (DateTime.now().millisecondsSinceEpoch % 20) - 10;

    return (baseHeartRate + activityBonus + random).clamp(50, 180);
  }

  // 칼로리 계산
  static double _calculateCalories(int heartRate, int steps) {
    // 기본 대사율 (BMR) + 활동 칼로리
    final bmr = 1.2; // 시간당 기본 대사 칼로리
    final stepCalories = steps * 0.04; // 걸음당 0.04 칼로리
    final heartRateBonus = (heartRate - 70) * 0.1; // 심박수 기반 추가 칼로리

    return bmr + stepCalories + heartRateBonus.clamp(0, 100);
  }

  // 활동 시간 계산
  static int _calculateActiveMinutes() {
    // 심박수 기록을 기반으로 활동 시간 계산
    if (_heartRateHistory.isEmpty) return 0;

    int activeMinutes = 0;
    for (final hr in _heartRateHistory) {
      if (hr > 100) {
        // 중강도 이상 활동
        activeMinutes++;
      }
    }

    return activeMinutes;
  }

  // 수면 시간 계산
  static double _calculateSleepHours() {
    final now = DateTime.now();
    final hour = now.hour;

    // 시간대 기반 수면 추정
    if (hour >= 22 || hour <= 6) {
      return 8.0; // 수면 시간
    } else {
      return 0.0; // 깨어있는 시간
    }
  }

  // 스트레스 레벨 계산
  static int _calculateStressLevel() {
    if (_heartRateHistory.length < 10) return 1;

    // 심박수 변이성 계산
    double sum = 0;
    for (int i = 1; i < _heartRateHistory.length; i++) {
      sum += (_heartRateHistory[i] - _heartRateHistory[i - 1]).abs();
    }

    final hrv = sum / (_heartRateHistory.length - 1);

    // HRV가 낮을수록 스트레스가 높음
    if (hrv < 5) return 5; // 높은 스트레스
    if (hrv < 10) return 3; // 중간 스트레스
    return 1; // 낮은 스트레스
  }

  // 배터리 레벨 생성
  static double _generateBatteryLevel() {
    final hour = DateTime.now().hour;
    // 하루 종일 배터리가 서서히 감소하는 시뮬레이션
    return (100 - (hour * 4)).clamp(10, 100).toDouble();
  }

  // 습관 자동 완료 확인
  static Future<void> _checkAutoHabitCompletion(WearableHealthData data) async {
    // 걸음수 기반 습관 완료
    if (data.steps >= 10000) {
      // "만보 걷기" 습관 자동 완료
      print('만보 걷기 습관 자동 완료!');
    }

    // 활동 시간 기반 습관 완료
    if (data.activeMinutes >= 30) {
      // "30분 운동" 습관 자동 완료
      print('30분 운동 습관 자동 완료!');
    }

    // 심박수 기반 습관 완료
    if (data.heartRate > 120) {
      // "유산소 운동" 습관 자동 완료
      print('유산소 운동 습관 자동 완료!');
    }
  }

  // 실시간 건강 데이터 가져오기
  static WearableHealthData? getCurrentHealthData() {
    if (!_isInitialized) return null;

    // 현재 수집된 데이터 반환
    return WearableHealthData(
      heartRate: _generateRealisticHeartRate(),
      steps: _stepCount,
      calories: _calculateCalories(_generateRealisticHeartRate(), _stepCount),
      distance: _totalDistance,
      activeMinutes: _calculateActiveMinutes(),
      sleepHours: _calculateSleepHours(),
      stressLevel: _calculateStressLevel(),
      timestamp: DateTime.now(),
      deviceType: _connectedDevice,
      batteryLevel: _generateBatteryLevel(),
    );
  }

  // 연결된 디바이스 정보
  static String get connectedDevice => _connectedDevice;
  static bool get isConnected => _connectedDevice.isNotEmpty;

  // 정리
  static void dispose() {
    _syncTimer?.cancel();
    _accelerometerSubscription?.cancel();
    _gyroscopeSubscription?.cancel();
    _locationSubscription?.cancel();
    _dataStreamController.close();
    _isInitialized = false;
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:socket_io_client/socket_io_client.dart' as io;
import 'package:uuid/uuid.dart';
import '../models/user_stats.dart';
import '../models/habit.dart';
import 'user_stats_service.dart';
import 'habit_service.dart';

class SocialUser {
  final String id;
  final String nickname;
  final String avatar;
  final int level;
  final int totalCompleted;
  final int currentStreak;
  final List<String> achievements;
  final bool isOnline;
  final DateTime lastSeen;

  SocialUser({
    required this.id,
    required this.nickname,
    required this.avatar,
    required this.level,
    required this.totalCompleted,
    required this.currentStreak,
    required this.achievements,
    required this.isOnline,
    required this.lastSeen,
  });

  factory SocialUser.fromJson(Map<String, dynamic> json) {
    return SocialUser(
      id: json['id'],
      nickname: json['nickname'],
      avatar: json['avatar'],
      level: json['level'],
      totalCompleted: json['totalCompleted'],
      currentStreak: json['currentStreak'],
      achievements: List<String>.from(json['achievements'] ?? []),
      isOnline: json['isOnline'] ?? false,
      lastSeen: DateTime.parse(json['lastSeen']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'nickname': nickname,
      'avatar': avatar,
      'level': level,
      'totalCompleted': totalCompleted,
      'currentStreak': currentStreak,
      'achievements': achievements,
      'isOnline': isOnline,
      'lastSeen': lastSeen.toIso8601String(),
    };
  }
}

class Challenge {
  final String id;
  final String name;
  final String description;
  final String habitCategory;
  final int duration; // 일 수
  final List<String> participants;
  final Map<String, int> progress; // 사용자별 진행률
  final DateTime startDate;
  final DateTime endDate;
  final String createdBy;
  final Map<String, dynamic> rewards;

  Challenge({
    required this.id,
    required this.name,
    required this.description,
    required this.habitCategory,
    required this.duration,
    required this.participants,
    required this.progress,
    required this.startDate,
    required this.endDate,
    required this.createdBy,
    required this.rewards,
  });

  factory Challenge.fromJson(Map<String, dynamic> json) {
    return Challenge(
      id: json['id'],
      name: json['name'],
      description: json['description'],
      habitCategory: json['habitCategory'],
      duration: json['duration'],
      participants: List<String>.from(json['participants'] ?? []),
      progress: Map<String, int>.from(json['progress'] ?? {}),
      startDate: DateTime.parse(json['startDate']),
      endDate: DateTime.parse(json['endDate']),
      createdBy: json['createdBy'],
      rewards: json['rewards'] ?? {},
    );
  }
}

class SocialAIService {
  static io.Socket? _socket;
  static bool _isConnected = false;
  static String? _currentUserId;
  static String? _currentUserNickname;
  static final List<SocialUser> _friends = [];
  static final List<Challenge> _activeChallenges = [];
  static final StreamController<Map<String, dynamic>> _socialEventController = 
      StreamController<Map<String, dynamic>>.broadcast();

  // 소셜 이벤트 스트림
  static Stream<Map<String, dynamic>> get socialEventStream => _socialEventController.stream;

  // 초기화
  static Future<bool> initialize() async {
    try {
      _currentUserId = const Uuid().v4();
      _currentUserNickname = await _generateNickname();

      // 소켓 연결 (실제로는 서버 URL 필요)
      _socket = io.io('ws://localhost:3000', <String, dynamic>{
        'transports': ['websocket'],
        'autoConnect': false,
      });

      _setupSocketListeners();
      _socket!.connect();

      return true;
    } catch (e) {
      print('소셜 AI 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 소켓 리스너 설정
  static void _setupSocketListeners() {
    _socket!.on('connect', (_) {
      print('소셜 서버에 연결됨');
      _isConnected = true;
      _joinUserRoom();
    });

    _socket!.on('disconnect', (_) {
      print('소셜 서버 연결 해제됨');
      _isConnected = false;
    });

    _socket!.on('friend_update', (data) {
      _handleFriendUpdate(data);
    });

    _socket!.on('challenge_update', (data) {
      _handleChallengeUpdate(data);
    });

    _socket!.on('ai_suggestion', (data) {
      _handleAISuggestion(data);
    });

    _socket!.on('social_notification', (data) {
      _socialEventController.add({
        'type': 'notification',
        'data': data,
      });
    });
  }

  // 사용자 룸 참가
  static void _joinUserRoom() {
    if (_socket != null && _currentUserId != null) {
      _socket!.emit('join_user', {
        'userId': _currentUserId,
        'nickname': _currentUserNickname,
        'userStats': _getCurrentUserStats(),
      });
    }
  }

  // 현재 사용자 통계 가져오기
  static Map<String, dynamic> _getCurrentUserStats() {
    // 실제로는 UserStatsService에서 가져와야 함
    return {
      'level': 5,
      'totalCompleted': 150,
      'currentStreak': 7,
      'achievements': ['week_warrior', 'month_master'],
    };
  }

  // 닉네임 생성
  static Future<String> _generateNickname() async {
    final adjectives = ['용감한', '똑똑한', '빠른', '강한', '친절한', '재미있는'];
    final nouns = ['사자', '독수리', '호랑이', '늑대', '곰', '여우'];
    final random = Random();
    
    final adjective = adjectives[random.nextInt(adjectives.length)];
    final noun = nouns[random.nextInt(nouns.length)];
    final number = random.nextInt(1000);
    
    return '$adjective$noun$number';
  }

  // 친구 추가
  static Future<bool> addFriend(String friendCode) async {
    if (!_isConnected) return false;

    try {
      _socket!.emit('add_friend', {
        'userId': _currentUserId,
        'friendCode': friendCode,
      });
      return true;
    } catch (e) {
      print('친구 추가 오류: $e');
      return false;
    }
  }

  // 친구 목록 가져오기
  static List<SocialUser> getFriends() {
    return List.from(_friends);
  }

  // 리더보드 가져오기
  static Future<List<SocialUser>> getLeaderboard({String period = 'weekly'}) async {
    if (!_isConnected) {
      return _getMockLeaderboard();
    }

    try {
      final completer = Completer<List<SocialUser>>();
      
      _socket!.emit('get_leaderboard', {
        'period': period,
        'userId': _currentUserId,
      });

      _socket!.once('leaderboard_response', (data) {
        final users = (data['users'] as List)
            .map((user) => SocialUser.fromJson(user))
            .toList();
        completer.complete(users);
      });

      return await completer.future.timeout(const Duration(seconds: 10));
    } catch (e) {
      print('리더보드 가져오기 오류: $e');
      return _getMockLeaderboard();
    }
  }

  // 챌린지 생성
  static Future<bool> createChallenge({
    required String name,
    required String description,
    required String habitCategory,
    required int duration,
    required List<String> invitedFriends,
  }) async {
    if (!_isConnected) return false;

    try {
      final challengeId = const Uuid().v4();
      final startDate = DateTime.now();
      final endDate = startDate.add(Duration(days: duration));

      _socket!.emit('create_challenge', {
        'id': challengeId,
        'name': name,
        'description': description,
        'habitCategory': habitCategory,
        'duration': duration,
        'startDate': startDate.toIso8601String(),
        'endDate': endDate.toIso8601String(),
        'createdBy': _currentUserId,
        'invitedFriends': invitedFriends,
      });

      return true;
    } catch (e) {
      print('챌린지 생성 오류: $e');
      return false;
    }
  }

  // 챌린지 참가
  static Future<bool> joinChallenge(String challengeId) async {
    if (!_isConnected) return false;

    try {
      _socket!.emit('join_challenge', {
        'challengeId': challengeId,
        'userId': _currentUserId,
      });
      return true;
    } catch (e) {
      print('챌린지 참가 오류: $e');
      return false;
    }
  }

  // 활성 챌린지 목록
  static List<Challenge> getActiveChallenges() {
    return List.from(_activeChallenges);
  }

  // 챌린지 진행률 업데이트
  static Future<void> updateChallengeProgress(String challengeId, int progress) async {
    if (!_isConnected) return;

    try {
      _socket!.emit('update_challenge_progress', {
        'challengeId': challengeId,
        'userId': _currentUserId,
        'progress': progress,
      });
    } catch (e) {
      print('챌린지 진행률 업데이트 오류: $e');
    }
  }

  // AI 기반 소셜 추천
  static Future<Map<String, dynamic>> getAISocialRecommendations() async {
    if (!_isConnected) {
      return _getMockAIRecommendations();
    }

    try {
      final completer = Completer<Map<String, dynamic>>();
      
      _socket!.emit('get_ai_social_recommendations', {
        'userId': _currentUserId,
        'userStats': _getCurrentUserStats(),
        'friends': _friends.map((f) => f.toJson()).toList(),
      });

      _socket!.once('ai_recommendations_response', (data) {
        completer.complete(Map<String, dynamic>.from(data));
      });

      return await completer.future.timeout(const Duration(seconds: 10));
    } catch (e) {
      print('AI 소셜 추천 오류: $e');
      return _getMockAIRecommendations();
    }
  }

  // 친구와 습관 비교
  static Future<Map<String, dynamic>> compareWithFriend(String friendId) async {
    if (!_isConnected) {
      return _getMockComparison();
    }

    try {
      final completer = Completer<Map<String, dynamic>>();
      
      _socket!.emit('compare_with_friend', {
        'userId': _currentUserId,
        'friendId': friendId,
      });

      _socket!.once('comparison_response', (data) {
        completer.complete(Map<String, dynamic>.from(data));
      });

      return await completer.future.timeout(const Duration(seconds: 10));
    } catch (e) {
      print('친구 비교 오류: $e');
      return _getMockComparison();
    }
  }

  // 그룹 동기부여 메시지 생성
  static Future<List<String>> generateGroupMotivation() async {
    if (!_isConnected) {
      return _getMockGroupMotivation();
    }

    try {
      final completer = Completer<List<String>>();
      
      _socket!.emit('generate_group_motivation', {
        'userId': _currentUserId,
        'friends': _friends.map((f) => f.toJson()).toList(),
        'challenges': _activeChallenges.map((c) => c.toJson()).toList(),
      });

      _socket!.once('group_motivation_response', (data) {
        completer.complete(List<String>.from(data['messages']));
      });

      return await completer.future.timeout(const Duration(seconds: 10));
    } catch (e) {
      print('그룹 동기부여 생성 오류: $e');
      return _getMockGroupMotivation();
    }
  }

  // 이벤트 핸들러들
  static void _handleFriendUpdate(dynamic data) {
    try {
      final friend = SocialUser.fromJson(data);
      final index = _friends.indexWhere((f) => f.id == friend.id);
      
      if (index >= 0) {
        _friends[index] = friend;
      } else {
        _friends.add(friend);
      }

      _socialEventController.add({
        'type': 'friend_update',
        'data': friend.toJson(),
      });
    } catch (e) {
      print('친구 업데이트 처리 오류: $e');
    }
  }

  static void _handleChallengeUpdate(dynamic data) {
    try {
      final challenge = Challenge.fromJson(data);
      final index = _activeChallenges.indexWhere((c) => c.id == challenge.id);
      
      if (index >= 0) {
        _activeChallenges[index] = challenge;
      } else {
        _activeChallenges.add(challenge);
      }

      _socialEventController.add({
        'type': 'challenge_update',
        'data': challenge.toJson(),
      });
    } catch (e) {
      print('챌린지 업데이트 처리 오류: $e');
    }
  }

  static void _handleAISuggestion(dynamic data) {
    _socialEventController.add({
      'type': 'ai_suggestion',
      'data': data,
    });
  }

  // 목 데이터들
  static List<SocialUser> _getMockLeaderboard() {
    return [
      SocialUser(
        id: '1',
        nickname: '용감한사자123',
        avatar: '🦁',
        level: 8,
        totalCompleted: 250,
        currentStreak: 15,
        achievements: ['week_warrior', 'month_master'],
        isOnline: true,
        lastSeen: DateTime.now(),
      ),
      SocialUser(
        id: '2',
        nickname: '똑똑한독수리456',
        avatar: '🦅',
        level: 6,
        totalCompleted: 180,
        currentStreak: 12,
        achievements: ['week_warrior'],
        isOnline: false,
        lastSeen: DateTime.now().subtract(const Duration(hours: 2)),
      ),
    ];
  }

  static Map<String, dynamic> _getMockAIRecommendations() {
    return {
      'friendSuggestions': [
        {
          'type': 'similar_level',
          'message': '비슷한 레벨의 친구들과 함께 도전해보세요!',
          'users': ['user1', 'user2'],
        }
      ],
      'challengeSuggestions': [
        {
          'name': '30일 물 마시기 챌린지',
          'description': '친구들과 함께 건강한 습관 만들기',
          'category': 'health',
        }
      ],
    };
  }

  static Map<String, dynamic> _getMockComparison() {
    return {
      'myStats': {'level': 5, 'streak': 7},
      'friendStats': {'level': 6, 'streak': 10},
      'comparison': '친구가 더 높은 레벨이에요! 화이팅!',
    };
  }

  static List<String> _getMockGroupMotivation() {
    return [
      '친구들과 함께라면 더 멀리 갈 수 있어요! 💪',
      '오늘도 서로 응원하며 목표를 달성해봐요! 🌟',
      '함께하는 습관이 가장 오래 지속됩니다! 👥',
    ];
  }

  // 연결 상태 확인
  static bool get isConnected => _isConnected;

  // 현재 사용자 ID
  static String? get currentUserId => _currentUserId;

  // 현재 사용자 닉네임
  static String? get currentUserNickname => _currentUserNickname;

  // 정리
  static void dispose() {
    _socket?.disconnect();
    _socket?.dispose();
    _socialEventController.close();
    _isConnected = false;
  }
}

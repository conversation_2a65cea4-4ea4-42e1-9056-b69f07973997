import 'dart:async';
import 'dart:math';
import '../models/habit.dart';
import '../models/user_stats.dart';

// 예측 분석 결과
class PredictionResult {
  final String habitId;
  final String habitName;
  final double successProbability;
  final List<double> weeklyPrediction;
  final List<double> monthlyPrediction;
  final Map<String, double> riskFactors;
  final List<String> recommendations;
  final DateTime predictionDate;

  PredictionResult({
    required this.habitId,
    required this.habitName,
    required this.successProbability,
    required this.weeklyPrediction,
    required this.monthlyPrediction,
    required this.riskFactors,
    required this.recommendations,
    required this.predictionDate,
  });

  Map<String, dynamic> toJson() => {
    'habitId': habitId,
    'habitName': habitName,
    'successProbability': successProbability,
    'weeklyPrediction': weeklyPrediction,
    'monthlyPrediction': monthlyPrediction,
    'riskFactors': riskFactors,
    'recommendations': recommendations,
    'predictionDate': predictionDate.toIso8601String(),
  };
}

// 고급 분석 인사이트
class AnalyticsInsight {
  final String id;
  final String title;
  final String description;
  final String category;
  final double impact;
  final List<Map<String, dynamic>> chartData;
  final String visualization;
  final DateTime generatedAt;

  AnalyticsInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.impact,
    required this.chartData,
    required this.visualization,
    required this.generatedAt,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'category': category,
    'impact': impact,
    'chartData': chartData,
    'visualization': visualization,
    'generatedAt': generatedAt.toIso8601String(),
  };
}

// 고급 분석 서비스
class AdvancedAnalyticsService {
  static bool _isInitialized = false;
  static List<PredictionResult> _predictions = [];
  static List<AnalyticsInsight> _insights = [];
  static Timer? _analyticsTimer;

  // 실시간 분석 스트림
  static final StreamController<AnalyticsInsight> _analyticsStreamController = 
      StreamController<AnalyticsInsight>.broadcast();
  static Stream<AnalyticsInsight> get analyticsStream => _analyticsStreamController.stream;

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 기존 데이터 로드
      await _loadHistoricalData();
      
      // 실시간 분석 시작
      _startRealTimeAnalysis();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('고급 분석 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 실시간 분석 시작
  static void _startRealTimeAnalysis() {
    _analyticsTimer = Timer.periodic(const Duration(hours: 1), (timer) async {
      await _performRealTimeAnalysis();
    });
  }

  // 실시간 분석 수행
  static Future<void> _performRealTimeAnalysis() async {
    try {
      // 새로운 인사이트 생성
      final newInsights = await _generateRealTimeInsights();
      
      for (final insight in newInsights) {
        _insights.add(insight);
        _analyticsStreamController.add(insight);
      }
      
      // 최근 50개 인사이트만 유지
      if (_insights.length > 50) {
        _insights = _insights.sublist(_insights.length - 50);
      }
      
    } catch (e) {
      print('실시간 분석 오류: $e');
    }
  }

  // 머신러닝 기반 습관 성공 예측
  static Future<List<PredictionResult>> predictHabitSuccess(
    List<Habit> habits,
    UserStats userStats,
  ) async {
    final predictions = <PredictionResult>[];
    
    for (final habit in habits) {
      final prediction = await _predictSingleHabit(habit, userStats);
      predictions.add(prediction);
    }
    
    _predictions = predictions;
    return predictions;
  }

  // 개별 습관 예측
  static Future<PredictionResult> _predictSingleHabit(
    Habit habit,
    UserStats userStats,
  ) async {
    // 기본 성공 확률 계산
    final baseSuccessRate = habit.getWeeklyCompletionRate();
    
    // 사용자 레벨 보정
    final levelBonus = (userStats.level - 1) * 0.02;
    
    // 스트릭 보정
    final streakBonus = userStats.currentStreak > 7 ? 0.1 : 0.0;
    
    // 요일별 패턴 분석
    final weeklyPattern = _analyzeWeeklyPattern(habit);
    
    // 월별 트렌드 분석
    final monthlyTrend = _analyzeMonthlyTrend(habit);
    
    // 위험 요인 분석
    final riskFactors = _analyzeRiskFactors(habit, userStats);
    
    // 추천사항 생성
    final recommendations = _generateRecommendations(habit, riskFactors);
    
    // 최종 성공 확률
    final successProbability = (baseSuccessRate + levelBonus + streakBonus).clamp(0.0, 1.0);
    
    return PredictionResult(
      habitId: habit.id,
      habitName: habit.name,
      successProbability: successProbability,
      weeklyPrediction: weeklyPattern,
      monthlyPrediction: monthlyTrend,
      riskFactors: riskFactors,
      recommendations: recommendations,
      predictionDate: DateTime.now(),
    );
  }

  // 요일별 패턴 분석
  static List<double> _analyzeWeeklyPattern(Habit habit) {
    final pattern = <double>[];
    final random = Random();
    
    // 실제로는 과거 데이터를 기반으로 분석
    for (int i = 0; i < 7; i++) {
      double probability = 0.5;
      
      // 주말 효과
      if (i >= 5) { // 토요일, 일요일
        probability *= 0.8; // 주말에 성공률 감소
      }
      
      // 월요일 효과
      if (i == 0) {
        probability *= 1.2; // 월요일에 동기부여 증가
      }
      
      // 랜덤 변동
      probability += (random.nextDouble() - 0.5) * 0.2;
      
      pattern.add(probability.clamp(0.0, 1.0));
    }
    
    return pattern;
  }

  // 월별 트렌드 분석
  static List<double> _analyzeMonthlyTrend(Habit habit) {
    final trend = <double>[];
    final random = Random();
    final baseRate = habit.getWeeklyCompletionRate();
    
    // 30일 예측
    for (int i = 0; i < 30; i++) {
      double prediction = baseRate;
      
      // 시간에 따른 감소 (습관 피로도)
      prediction *= (1.0 - (i * 0.005));
      
      // 주기적 변동
      prediction += sin(i * 0.2) * 0.1;
      
      // 랜덤 노이즈
      prediction += (random.nextDouble() - 0.5) * 0.1;
      
      trend.add(prediction.clamp(0.0, 1.0));
    }
    
    return trend;
  }

  // 위험 요인 분석
  static Map<String, double> _analyzeRiskFactors(Habit habit, UserStats userStats) {
    final riskFactors = <String, double>{};
    
    // 스트릭 중단 위험
    if (userStats.currentStreak == 0) {
      riskFactors['streak_break'] = 0.8;
    } else if (userStats.currentStreak < 7) {
      riskFactors['streak_break'] = 0.4;
    }
    
    // 습관 피로도
    final habitAge = DateTime.now().difference(habit.createdDate).inDays;
    if (habitAge > 30) {
      riskFactors['habit_fatigue'] = min(habitAge / 100.0, 0.7);
    }
    
    // 복잡성 위험
    if (habit.name.length > 20) { // 복잡한 습관명
      riskFactors['complexity'] = 0.3;
    }
    
    // 시간 충돌 위험
    riskFactors['time_conflict'] = Random().nextDouble() * 0.5;
    
    // 동기부여 감소 위험
    if (userStats.level < 5) {
      riskFactors['motivation_drop'] = 0.6;
    }
    
    return riskFactors;
  }

  // 추천사항 생성
  static List<String> _generateRecommendations(
    Habit habit,
    Map<String, double> riskFactors,
  ) {
    final recommendations = <String>[];
    
    if (riskFactors['streak_break'] != null && riskFactors['streak_break']! > 0.5) {
      recommendations.add('스트릭을 다시 시작하기 위해 더 작은 목표로 설정해보세요');
    }
    
    if (riskFactors['habit_fatigue'] != null && riskFactors['habit_fatigue']! > 0.5) {
      recommendations.add('습관에 변화를 주어 새로운 동기를 찾아보세요');
    }
    
    if (riskFactors['complexity'] != null && riskFactors['complexity']! > 0.2) {
      recommendations.add('습관을 더 간단하고 구체적으로 만들어보세요');
    }
    
    if (riskFactors['time_conflict'] != null && riskFactors['time_conflict']! > 0.3) {
      recommendations.add('습관 실행 시간을 조정해보세요');
    }
    
    if (riskFactors['motivation_drop'] != null && riskFactors['motivation_drop']! > 0.4) {
      recommendations.add('친구들과 함께 챌린지에 참여해보세요');
    }
    
    return recommendations;
  }

  // 고급 인사이트 생성
  static Future<List<AnalyticsInsight>> generateAdvancedInsights(
    List<Habit> habits,
    UserStats userStats,
  ) async {
    final insights = <AnalyticsInsight>[];
    
    // 1. 시간대별 성공률 분석
    insights.add(await _generateTimeAnalysisInsight(habits));
    
    // 2. 카테고리별 성과 분석
    insights.add(await _generateCategoryAnalysisInsight(habits));
    
    // 3. 스트릭 패턴 분석
    insights.add(await _generateStreakPatternInsight(userStats));
    
    // 4. 계절별 영향 분석
    insights.add(await _generateSeasonalAnalysisInsight(habits));
    
    // 5. 상관관계 분석
    insights.add(await _generateCorrelationInsight(habits));
    
    return insights;
  }

  // 시간대별 분석 인사이트
  static Future<AnalyticsInsight> _generateTimeAnalysisInsight(List<Habit> habits) async {
    final hourlySuccess = <int, double>{};
    
    // 시간대별 성공률 계산 (모의 데이터)
    for (int hour = 0; hour < 24; hour++) {
      double successRate = 0.5;
      
      if (hour >= 6 && hour <= 10) {
        successRate = 0.8; // 아침 시간대 높은 성공률
      } else if (hour >= 18 && hour <= 22) {
        successRate = 0.6; // 저녁 시간대 중간 성공률
      } else if (hour >= 23 || hour <= 5) {
        successRate = 0.2; // 밤/새벽 시간대 낮은 성공률
      }
      
      hourlySuccess[hour] = successRate;
    }
    
    final chartData = hourlySuccess.entries.map((e) => {
      'hour': e.key,
      'successRate': e.value,
    }).toList();
    
    return AnalyticsInsight(
      id: 'time_analysis_${DateTime.now().millisecondsSinceEpoch}',
      title: '시간대별 성공률 분석',
      description: '아침 6-10시에 습관 성공률이 가장 높습니다 (80%). 이 시간대를 활용해보세요!',
      category: 'time_analysis',
      impact: 0.8,
      chartData: chartData,
      visualization: 'line_chart',
      generatedAt: DateTime.now(),
    );
  }

  // 카테고리별 분석 인사이트
  static Future<AnalyticsInsight> _generateCategoryAnalysisInsight(List<Habit> habits) async {
    final categorySuccess = <String, double>{};
    final categoryCount = <String, int>{};
    
    for (final habit in habits) {
      final category = habit.categoryId;
      final successRate = habit.getWeeklyCompletionRate();
      
      categorySuccess[category] = (categorySuccess[category] ?? 0.0) + successRate;
      categoryCount[category] = (categoryCount[category] ?? 0) + 1;
    }
    
    // 평균 성공률 계산
    for (final category in categorySuccess.keys) {
      categorySuccess[category] = categorySuccess[category]! / categoryCount[category]!;
    }
    
    final chartData = categorySuccess.entries.map((e) => {
      'category': e.key,
      'successRate': e.value,
      'count': categoryCount[e.key],
    }).toList();
    
    final bestCategory = categorySuccess.entries.reduce((a, b) => a.value > b.value ? a : b);
    
    return AnalyticsInsight(
      id: 'category_analysis_${DateTime.now().millisecondsSinceEpoch}',
      title: '카테고리별 성과 분석',
      description: '${bestCategory.key} 카테고리에서 가장 높은 성공률(${(bestCategory.value * 100).toInt()}%)을 보이고 있습니다.',
      category: 'category_analysis',
      impact: 0.7,
      chartData: chartData,
      visualization: 'pie_chart',
      generatedAt: DateTime.now(),
    );
  }

  // 스트릭 패턴 인사이트
  static Future<AnalyticsInsight> _generateStreakPatternInsight(UserStats userStats) async {
    final streakHistory = <Map<String, dynamic>>[];
    
    // 모의 스트릭 히스토리 생성
    for (int i = 0; i < 30; i++) {
      final date = DateTime.now().subtract(Duration(days: 29 - i));
      final streak = max(0, userStats.currentStreak - (29 - i));
      
      streakHistory.add({
        'date': date.toIso8601String(),
        'streak': streak,
        'day': i + 1,
      });
    }
    
    return AnalyticsInsight(
      id: 'streak_pattern_${DateTime.now().millisecondsSinceEpoch}',
      title: '스트릭 패턴 분석',
      description: '현재 ${userStats.currentStreak}일 연속 성공 중입니다. 최근 30일간 꾸준한 상승 추세를 보이고 있어요!',
      category: 'streak_analysis',
      impact: 0.9,
      chartData: streakHistory,
      visualization: 'area_chart',
      generatedAt: DateTime.now(),
    );
  }

  // 계절별 영향 분석
  static Future<AnalyticsInsight> _generateSeasonalAnalysisInsight(List<Habit> habits) async {
    final seasonalData = [
      {'season': '봄', 'successRate': 0.75, 'motivation': 0.8},
      {'season': '여름', 'successRate': 0.65, 'motivation': 0.6},
      {'season': '가을', 'successRate': 0.8, 'motivation': 0.85},
      {'season': '겨울', 'successRate': 0.55, 'motivation': 0.5},
    ];
    
    return AnalyticsInsight(
      id: 'seasonal_analysis_${DateTime.now().millisecondsSinceEpoch}',
      title: '계절별 영향 분석',
      description: '가을에 가장 높은 성공률(80%)을 보입니다. 계절 변화에 맞춰 습관을 조정해보세요.',
      category: 'seasonal_analysis',
      impact: 0.6,
      chartData: seasonalData,
      visualization: 'radar_chart',
      generatedAt: DateTime.now(),
    );
  }

  // 상관관계 분석
  static Future<AnalyticsInsight> _generateCorrelationInsight(List<Habit> habits) async {
    final correlationData = [
      {'habit1': '운동하기', 'habit2': '물 마시기', 'correlation': 0.8},
      {'habit1': '명상하기', 'habit2': '일기쓰기', 'correlation': 0.7},
      {'habit1': '독서하기', 'habit2': '일찍 자기', 'correlation': 0.6},
      {'habit1': '운동하기', 'habit2': '건강한 식사', 'correlation': 0.9},
    ];
    
    return AnalyticsInsight(
      id: 'correlation_analysis_${DateTime.now().millisecondsSinceEpoch}',
      title: '습관 간 상관관계 분석',
      description: '운동하기와 건강한 식사 습관이 강한 양의 상관관계(0.9)를 보입니다. 함께 실천하면 더 효과적이에요!',
      category: 'correlation_analysis',
      impact: 0.85,
      chartData: correlationData,
      visualization: 'heatmap',
      generatedAt: DateTime.now(),
    );
  }

  // 실시간 인사이트 생성
  static Future<List<AnalyticsInsight>> _generateRealTimeInsights() async {
    final insights = <AnalyticsInsight>[];
    final random = Random();
    
    // 랜덤하게 새로운 인사이트 생성
    if (random.nextDouble() < 0.3) {
      insights.add(AnalyticsInsight(
        id: 'realtime_${DateTime.now().millisecondsSinceEpoch}',
        title: '실시간 패턴 감지',
        description: '지난 1시간 동안의 활동 패턴을 분석한 결과, 집중력이 높은 상태입니다. 지금이 새로운 습관을 시작하기 좋은 시점이에요!',
        category: 'realtime_analysis',
        impact: 0.7,
        chartData: [
          {'time': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(), 'focus': 0.6},
          {'time': DateTime.now().subtract(const Duration(minutes: 30)).toIso8601String(), 'focus': 0.8},
          {'time': DateTime.now().toIso8601String(), 'focus': 0.9},
        ],
        visualization: 'line_chart',
        generatedAt: DateTime.now(),
      ));
    }
    
    return insights;
  }

  // 기존 데이터 로드
  static Future<void> _loadHistoricalData() async {
    // 실제로는 로컬 저장소나 서버에서 데이터 로드
    await Future.delayed(const Duration(milliseconds: 500));
  }

  // Getter 메서드들
  static List<PredictionResult> get predictions => _predictions;
  static List<AnalyticsInsight> get insights => _insights;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _analyticsTimer?.cancel();
    _analyticsStreamController.close();
    _isInitialized = false;
  }
}

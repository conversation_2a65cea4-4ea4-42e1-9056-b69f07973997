import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/habit.dart';
import 'habit_service.dart';

class VoiceCommandResult {
  final bool success;
  final String action;
  final String habitName;
  final String message;

  VoiceCommandResult({
    required this.success,
    required this.action,
    required this.habitName,
    required this.message,
  });
}

class VoiceCommandService {
  static final SpeechToText _speechToText = SpeechToText();
  static final FlutterTts _flutterTts = FlutterTts();
  static bool _speechEnabled = false;
  static bool _isListening = false;

  // 음성 명령 패턴들
  static const List<Map<String, dynamic>> _commandPatterns = [
    {
      'patterns': ['완료', '체크', '했어', '했습니다', '끝', '마쳤어', '마쳤습니다'],
      'action': 'complete',
      'description': '습관 완료'
    },
    {
      'patterns': ['취소', '언체크', '안했어', '안했습니다', '되돌려', '실수'],
      'action': 'uncomplete',
      'description': '습관 완료 취소'
    },
    {
      'patterns': ['추가', '새로운', '만들어', '생성'],
      'action': 'add',
      'description': '새 습관 추가'
    },
    {
      'patterns': ['삭제', '지워', '제거'],
      'action': 'delete',
      'description': '습관 삭제'
    },
    {
      'patterns': ['상태', '현황', '진행', '통계'],
      'action': 'status',
      'description': '상태 확인'
    },
  ];

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 마이크 권한 요청
      final microphoneStatus = await Permission.microphone.request();
      if (microphoneStatus != PermissionStatus.granted) {
        return false;
      }

      // 음성 인식 초기화
      _speechEnabled = await _speechToText.initialize(
        onError: (error) => print('음성 인식 오류: $error'),
        onStatus: (status) => print('음성 인식 상태: $status'),
      );

      // TTS 초기화
      await _flutterTts.setLanguage('ko-KR');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(0.8);
      await _flutterTts.setPitch(1.0);

      return _speechEnabled;
    } catch (e) {
      print('음성 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 음성 인식 시작
  static Future<String?> startListening() async {
    if (!_speechEnabled || _isListening) return null;

    try {
      _isListening = true;
      String recognizedText = '';

      await _speechToText.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
        },
        listenFor: const Duration(seconds: 5),
        pauseFor: const Duration(seconds: 2),
        partialResults: false,
        localeId: 'ko_KR',
      );

      // 인식 완료까지 대기
      await Future.delayed(const Duration(seconds: 6));
      _isListening = false;

      return recognizedText.isNotEmpty ? recognizedText : null;
    } catch (e) {
      print('음성 인식 오류: $e');
      _isListening = false;
      return null;
    }
  }

  // 음성 인식 중지
  static Future<void> stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      _isListening = false;
    }
  }

  // 음성 명령 처리
  static Future<VoiceCommandResult> processVoiceCommand(String voiceText) async {
    try {
      final habits = await HabitService.loadHabits();
      final command = _parseCommand(voiceText);
      
      if (command == null) {
        return VoiceCommandResult(
          success: false,
          action: 'unknown',
          habitName: '',
          message: '명령을 이해할 수 없습니다. 다시 시도해주세요.',
        );
      }

      final habitName = _extractHabitName(voiceText, command['action']);
      final matchedHabit = _findMatchingHabit(habitName, habits);

      switch (command['action']) {
        case 'complete':
          return await _handleCompleteCommand(matchedHabit, habitName);
        case 'uncomplete':
          return await _handleUncompleteCommand(matchedHabit, habitName);
        case 'add':
          return await _handleAddCommand(habitName);
        case 'delete':
          return await _handleDeleteCommand(matchedHabit, habitName);
        case 'status':
          return await _handleStatusCommand(habits);
        default:
          return VoiceCommandResult(
            success: false,
            action: 'unknown',
            habitName: habitName,
            message: '지원하지 않는 명령입니다.',
          );
      }
    } catch (e) {
      print('음성 명령 처리 오류: $e');
      return VoiceCommandResult(
        success: false,
        action: 'error',
        habitName: '',
        message: '명령 처리 중 오류가 발생했습니다.',
      );
    }
  }

  // 명령 패턴 분석
  static Map<String, dynamic>? _parseCommand(String text) {
    final lowerText = text.toLowerCase();
    
    for (final pattern in _commandPatterns) {
      final patterns = pattern['patterns'] as List<String>;
      if (patterns.any((p) => lowerText.contains(p))) {
        return pattern;
      }
    }
    
    return null;
  }

  // 습관 이름 추출
  static String _extractHabitName(String text, String action) {
    final lowerText = text.toLowerCase();
    
    // 명령어 제거
    String habitName = lowerText;
    for (final pattern in _commandPatterns) {
      if (pattern['action'] == action) {
        final patterns = pattern['patterns'] as List<String>;
        for (final p in patterns) {
          habitName = habitName.replaceAll(p, '').trim();
        }
        break;
      }
    }
    
    // 불필요한 단어 제거
    final stopWords = ['을', '를', '이', '가', '은', '는', '습관', '하기'];
    for (final word in stopWords) {
      habitName = habitName.replaceAll(word, '').trim();
    }
    
    return habitName;
  }

  // 일치하는 습관 찾기
  static Habit? _findMatchingHabit(String habitName, List<Habit> habits) {
    if (habitName.isEmpty) return null;
    
    // 정확한 일치 확인
    for (final habit in habits) {
      if (habit.name.toLowerCase() == habitName.toLowerCase()) {
        return habit;
      }
    }
    
    // 부분 일치 확인
    for (final habit in habits) {
      if (habit.name.toLowerCase().contains(habitName) || 
          habitName.contains(habit.name.toLowerCase())) {
        return habit;
      }
    }
    
    return null;
  }

  // 완료 명령 처리
  static Future<VoiceCommandResult> _handleCompleteCommand(
    Habit? habit, 
    String habitName,
  ) async {
    if (habit == null) {
      return VoiceCommandResult(
        success: false,
        action: 'complete',
        habitName: habitName,
        message: '$habitName 습관을 찾을 수 없습니다.',
      );
    }

    final success = await HabitService.toggleHabitCompletion(habit.id, DateTime.now());
    if (success) {
      await speak('${habit.name} 습관을 완료했습니다. 잘하셨어요!');
      return VoiceCommandResult(
        success: true,
        action: 'complete',
        habitName: habit.name,
        message: '${habit.name} 습관이 완료되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'complete',
        habitName: habit.name,
        message: '습관 완료 처리 중 오류가 발생했습니다.',
      );
    }
  }

  // 완료 취소 명령 처리
  static Future<VoiceCommandResult> _handleUncompleteCommand(
    Habit? habit, 
    String habitName,
  ) async {
    if (habit == null) {
      return VoiceCommandResult(
        success: false,
        action: 'uncomplete',
        habitName: habitName,
        message: '$habitName 습관을 찾을 수 없습니다.',
      );
    }

    final success = await HabitService.toggleHabitCompletion(habit.id, DateTime.now());
    if (success) {
      await speak('${habit.name} 습관 완료를 취소했습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'uncomplete',
        habitName: habit.name,
        message: '${habit.name} 습관 완료가 취소되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'uncomplete',
        habitName: habit.name,
        message: '습관 완료 취소 중 오류가 발생했습니다.',
      );
    }
  }

  // 추가 명령 처리
  static Future<VoiceCommandResult> _handleAddCommand(String habitName) async {
    if (habitName.isEmpty) {
      return VoiceCommandResult(
        success: false,
        action: 'add',
        habitName: '',
        message: '추가할 습관 이름을 말씀해주세요.',
      );
    }

    final success = await HabitService.addHabit(habitName);
    if (success) {
      await speak('$habitName 습관이 추가되었습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'add',
        habitName: habitName,
        message: '$habitName 습관이 추가되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'add',
        habitName: habitName,
        message: '습관 추가 중 오류가 발생했습니다.',
      );
    }
  }

  // 삭제 명령 처리
  static Future<VoiceCommandResult> _handleDeleteCommand(
    Habit? habit, 
    String habitName,
  ) async {
    if (habit == null) {
      return VoiceCommandResult(
        success: false,
        action: 'delete',
        habitName: habitName,
        message: '$habitName 습관을 찾을 수 없습니다.',
      );
    }

    final success = await HabitService.deleteHabit(habit.id);
    if (success) {
      await speak('${habit.name} 습관이 삭제되었습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'delete',
        habitName: habit.name,
        message: '${habit.name} 습관이 삭제되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'delete',
        habitName: habit.name,
        message: '습관 삭제 중 오류가 발생했습니다.',
      );
    }
  }

  // 상태 확인 명령 처리
  static Future<VoiceCommandResult> _handleStatusCommand(List<Habit> habits) async {
    if (habits.isEmpty) {
      await speak('등록된 습관이 없습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'status',
        habitName: '',
        message: '등록된 습관이 없습니다.',
      );
    }

    final today = DateTime.now();
    int completedToday = 0;
    
    for (final habit in habits) {
      if (habit.isCompletedOn(today)) {
        completedToday++;
      }
    }

    final message = '총 ${habits.length}개 습관 중 ${completedToday}개를 완료했습니다.';
    await speak(message);
    
    return VoiceCommandResult(
      success: true,
      action: 'status',
      habitName: '',
      message: message,
    );
  }

  // TTS 음성 출력
  static Future<void> speak(String text) async {
    try {
      await _flutterTts.speak(text);
    } catch (e) {
      print('TTS 오류: $e');
    }
  }

  // 음성 인식 가능 여부
  static bool get isAvailable => _speechEnabled;

  // 현재 듣고 있는지 여부
  static bool get isListening => _isListening;

  // 지원되는 명령어 목록
  static List<String> getSupportedCommands() {
    return [
      '"물 마시기 완료" - 습관 완료',
      '"운동하기 취소" - 습관 완료 취소',
      '"독서하기 추가" - 새 습관 추가',
      '"명상하기 삭제" - 습관 삭제',
      '"상태 확인" - 오늘 진행 상황',
    ];
  }
}

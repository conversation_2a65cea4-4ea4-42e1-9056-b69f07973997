import 'dart:math';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';
import 'habit_service.dart';
import 'user_stats_service.dart';
import 'ai_recommendation_service.dart';

class VoiceCommandResult {
  final bool success;
  final String action;
  final String habitName;
  final String message;

  VoiceCommandResult({
    required this.success,
    required this.action,
    required this.habitName,
    required this.message,
  });
}

class VoiceCommandService {
  static final SpeechToText _speechToText = SpeechToText();
  static final FlutterTts _flutterTts = FlutterTts();
  static bool _speechEnabled = false;
  static bool _isListening = false;

  // 고급 음성 명령 패턴들 (AI 강화)
  static const List<Map<String, dynamic>> _commandPatterns = [
    {
      'patterns': [
        '완료',
        '체크',
        '했어',
        '했습니다',
        '끝',
        '마쳤어',
        '마쳤습니다',
        '성공',
        '달성',
        '클리어'
      ],
      'action': 'complete',
      'description': '습관 완료',
      'confidence': 0.9,
    },
    {
      'patterns': ['취소', '언체크', '안했어', '안했습니다', '되돌려', '실수', '미완료', '실패'],
      'action': 'uncomplete',
      'description': '습관 완료 취소',
      'confidence': 0.85,
    },
    {
      'patterns': ['추가', '새로운', '만들어', '생성', '등록', '설정', '시작'],
      'action': 'add',
      'description': '새 습관 추가',
      'confidence': 0.8,
    },
    {
      'patterns': ['삭제', '지워', '제거', '그만', '중단', '없애'],
      'action': 'delete',
      'description': '습관 삭제',
      'confidence': 0.8,
    },
    {
      'patterns': ['상태', '현황', '진행', '통계', '리포트', '요약', '확인'],
      'action': 'status',
      'description': '상태 확인',
      'confidence': 0.9,
    },
    {
      'patterns': ['추천', '제안', '도움', '뭐할까', '어떤 습관', 'AI 추천', '제안해줘'],
      'action': 'smart_recommend',
      'description': 'AI 스마트 추천',
      'confidence': 0.85,
    },
    {
      'patterns': ['동기부여', '격려', '응원', '힘내', '파이팅', '의욕', '동기'],
      'action': 'motivation',
      'description': '동기부여 메시지',
      'confidence': 0.8,
    },
    {
      'patterns': ['아침', '저녁', '점심', '오늘', '내일', '지금', '시간', '언제'],
      'action': 'time_based',
      'description': '시간 기반 추천',
      'confidence': 0.75,
    },
    {
      'patterns': ['힘들어', '포기하고 싶어', '우울해', '스트레스', '지쳐', '어려워'],
      'action': 'emotional_support',
      'description': '감정적 지원',
      'confidence': 0.9,
    },
    {
      'patterns': ['분석', '패턴', '트렌드', '성과', '개선점', '피드백', '인사이트'],
      'action': 'analytics',
      'description': '고급 분석',
      'confidence': 0.8,
    },
  ];

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 마이크 권한 요청
      final microphoneStatus = await Permission.microphone.request();
      if (microphoneStatus != PermissionStatus.granted) {
        return false;
      }

      // 음성 인식 초기화
      _speechEnabled = await _speechToText.initialize(
        onError: (error) => print('음성 인식 오류: $error'),
        onStatus: (status) => print('음성 인식 상태: $status'),
      );

      // TTS 초기화
      await _flutterTts.setLanguage('ko-KR');
      await _flutterTts.setSpeechRate(0.5);
      await _flutterTts.setVolume(0.8);
      await _flutterTts.setPitch(1.0);

      return _speechEnabled;
    } catch (e) {
      print('음성 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 음성 인식 시작
  static Future<String?> startListening() async {
    if (!_speechEnabled || _isListening) return null;

    try {
      _isListening = true;
      String recognizedText = '';

      await _speechToText.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
        },
        listenFor: const Duration(seconds: 5),
        pauseFor: const Duration(seconds: 2),
        partialResults: false,
        localeId: 'ko_KR',
      );

      // 인식 완료까지 대기
      await Future.delayed(const Duration(seconds: 6));
      _isListening = false;

      return recognizedText.isNotEmpty ? recognizedText : null;
    } catch (e) {
      print('음성 인식 오류: $e');
      _isListening = false;
      return null;
    }
  }

  // 음성 인식 중지
  static Future<void> stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      _isListening = false;
    }
  }

  // 음성 명령 처리
  static Future<VoiceCommandResult> processVoiceCommand(
      String voiceText) async {
    try {
      final habits = await HabitService.loadHabits();
      final command = _parseCommand(voiceText);

      if (command == null) {
        return VoiceCommandResult(
          success: false,
          action: 'unknown',
          habitName: '',
          message: '명령을 이해할 수 없습니다. 다시 시도해주세요.',
        );
      }

      final habitName = _extractHabitName(voiceText, command['action']);
      final matchedHabit = _findMatchingHabit(habitName, habits);

      switch (command['action']) {
        case 'complete':
          return await _handleCompleteCommand(matchedHabit, habitName);
        case 'uncomplete':
          return await _handleUncompleteCommand(matchedHabit, habitName);
        case 'add':
          return await _handleAddCommand(habitName);
        case 'delete':
          return await _handleDeleteCommand(matchedHabit, habitName);
        case 'status':
          return await _handleStatusCommand(habits);
        case 'smart_recommend':
          return await _handleSmartRecommendCommand();
        case 'motivation':
          return await _handleMotivationCommand();
        case 'time_based':
          return await _handleTimeBasedCommand(voiceText);
        case 'emotional_support':
          return await _handleEmotionalSupportCommand(voiceText);
        case 'analytics':
          return await _handleAnalyticsCommand(habits);
        default:
          return VoiceCommandResult(
            success: false,
            action: 'unknown',
            habitName: habitName,
            message: '지원하지 않는 명령입니다.',
          );
      }
    } catch (e) {
      print('음성 명령 처리 오류: $e');
      return VoiceCommandResult(
        success: false,
        action: 'error',
        habitName: '',
        message: '명령 처리 중 오류가 발생했습니다.',
      );
    }
  }

  // 명령 패턴 분석
  static Map<String, dynamic>? _parseCommand(String text) {
    final lowerText = text.toLowerCase();

    for (final pattern in _commandPatterns) {
      final patterns = pattern['patterns'] as List<String>;
      if (patterns.any((p) => lowerText.contains(p))) {
        return pattern;
      }
    }

    return null;
  }

  // 습관 이름 추출
  static String _extractHabitName(String text, String action) {
    final lowerText = text.toLowerCase();

    // 명령어 제거
    String habitName = lowerText;
    for (final pattern in _commandPatterns) {
      if (pattern['action'] == action) {
        final patterns = pattern['patterns'] as List<String>;
        for (final p in patterns) {
          habitName = habitName.replaceAll(p, '').trim();
        }
        break;
      }
    }

    // 불필요한 단어 제거
    final stopWords = ['을', '를', '이', '가', '은', '는', '습관', '하기'];
    for (final word in stopWords) {
      habitName = habitName.replaceAll(word, '').trim();
    }

    return habitName;
  }

  // 일치하는 습관 찾기
  static Habit? _findMatchingHabit(String habitName, List<Habit> habits) {
    if (habitName.isEmpty) return null;

    // 정확한 일치 확인
    for (final habit in habits) {
      if (habit.name.toLowerCase() == habitName.toLowerCase()) {
        return habit;
      }
    }

    // 부분 일치 확인
    for (final habit in habits) {
      if (habit.name.toLowerCase().contains(habitName) ||
          habitName.contains(habit.name.toLowerCase())) {
        return habit;
      }
    }

    return null;
  }

  // 완료 명령 처리
  static Future<VoiceCommandResult> _handleCompleteCommand(
    Habit? habit,
    String habitName,
  ) async {
    if (habit == null) {
      return VoiceCommandResult(
        success: false,
        action: 'complete',
        habitName: habitName,
        message: '$habitName 습관을 찾을 수 없습니다.',
      );
    }

    final success =
        await HabitService.toggleHabitCompletion(habit.id, DateTime.now());
    if (success) {
      await speak('${habit.name} 습관을 완료했습니다. 잘하셨어요!');
      return VoiceCommandResult(
        success: true,
        action: 'complete',
        habitName: habit.name,
        message: '${habit.name} 습관이 완료되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'complete',
        habitName: habit.name,
        message: '습관 완료 처리 중 오류가 발생했습니다.',
      );
    }
  }

  // 완료 취소 명령 처리
  static Future<VoiceCommandResult> _handleUncompleteCommand(
    Habit? habit,
    String habitName,
  ) async {
    if (habit == null) {
      return VoiceCommandResult(
        success: false,
        action: 'uncomplete',
        habitName: habitName,
        message: '$habitName 습관을 찾을 수 없습니다.',
      );
    }

    final success =
        await HabitService.toggleHabitCompletion(habit.id, DateTime.now());
    if (success) {
      await speak('${habit.name} 습관 완료를 취소했습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'uncomplete',
        habitName: habit.name,
        message: '${habit.name} 습관 완료가 취소되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'uncomplete',
        habitName: habit.name,
        message: '습관 완료 취소 중 오류가 발생했습니다.',
      );
    }
  }

  // 추가 명령 처리
  static Future<VoiceCommandResult> _handleAddCommand(String habitName) async {
    if (habitName.isEmpty) {
      return VoiceCommandResult(
        success: false,
        action: 'add',
        habitName: '',
        message: '추가할 습관 이름을 말씀해주세요.',
      );
    }

    final success = await HabitService.addHabit(habitName);
    if (success) {
      await speak('$habitName 습관이 추가되었습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'add',
        habitName: habitName,
        message: '$habitName 습관이 추가되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'add',
        habitName: habitName,
        message: '습관 추가 중 오류가 발생했습니다.',
      );
    }
  }

  // 삭제 명령 처리
  static Future<VoiceCommandResult> _handleDeleteCommand(
    Habit? habit,
    String habitName,
  ) async {
    if (habit == null) {
      return VoiceCommandResult(
        success: false,
        action: 'delete',
        habitName: habitName,
        message: '$habitName 습관을 찾을 수 없습니다.',
      );
    }

    final success = await HabitService.deleteHabit(habit.id);
    if (success) {
      await speak('${habit.name} 습관이 삭제되었습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'delete',
        habitName: habit.name,
        message: '${habit.name} 습관이 삭제되었습니다.',
      );
    } else {
      return VoiceCommandResult(
        success: false,
        action: 'delete',
        habitName: habit.name,
        message: '습관 삭제 중 오류가 발생했습니다.',
      );
    }
  }

  // 상태 확인 명령 처리
  static Future<VoiceCommandResult> _handleStatusCommand(
      List<Habit> habits) async {
    if (habits.isEmpty) {
      await speak('등록된 습관이 없습니다.');
      return VoiceCommandResult(
        success: true,
        action: 'status',
        habitName: '',
        message: '등록된 습관이 없습니다.',
      );
    }

    final today = DateTime.now();
    int completedToday = 0;

    for (final habit in habits) {
      if (habit.isCompletedOn(today)) {
        completedToday++;
      }
    }

    final message = '총 ${habits.length}개 습관 중 ${completedToday}개를 완료했습니다.';
    await speak(message);

    return VoiceCommandResult(
      success: true,
      action: 'status',
      habitName: '',
      message: message,
    );
  }

  // TTS 음성 출력
  static Future<void> speak(String text) async {
    try {
      await _flutterTts.speak(text);
    } catch (e) {
      print('TTS 오류: $e');
    }
  }

  // 음성 인식 가능 여부
  static bool get isAvailable => _speechEnabled;

  // 현재 듣고 있는지 여부
  static bool get isListening => _isListening;

  // 고급 AI 명령 핸들러들

  // 스마트 추천 명령 처리
  static Future<VoiceCommandResult> _handleSmartRecommendCommand() async {
    try {
      // AI 추천 서비스 호출
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();
      final recommendations =
          await AIRecommendationService.generateRecommendations();

      if (recommendations.isNotEmpty) {
        final topRecommendation = recommendations.first;
        final message =
            'AI가 추천하는 습관: ${topRecommendation.name}. ${topRecommendation.reason}';
        await speak(message);

        return VoiceCommandResult(
          success: true,
          action: 'smart_recommend',
          habitName: topRecommendation.name,
          message: message,
        );
      } else {
        const message = '현재 추천할 수 있는 습관이 없습니다. 기존 습관을 더 꾸준히 실천해보세요.';
        await speak(message);

        return VoiceCommandResult(
          success: true,
          action: 'smart_recommend',
          habitName: '',
          message: message,
        );
      }
    } catch (e) {
      const message = '추천 시스템에 오류가 발생했습니다.';
      return VoiceCommandResult(
        success: false,
        action: 'smart_recommend',
        habitName: '',
        message: message,
      );
    }
  }

  // 동기부여 명령 처리
  static Future<VoiceCommandResult> _handleMotivationCommand() async {
    try {
      final userStats = await UserStatsService.loadUserStats();
      final motivationMessages = [
        '당신은 이미 레벨 ${userStats.level}에 도달했어요! 정말 대단합니다!',
        '현재 ${userStats.currentStreak}일 연속 성공 중이에요! 계속 화이팅!',
        '작은 습관이 큰 변화를 만듭니다. 당신은 잘하고 있어요!',
        '매일 조금씩 발전하는 당신의 모습이 멋져요!',
        '포기하지 않는 당신의 의지가 성공의 열쇠입니다!',
      ];

      final random = Random();
      final message =
          motivationMessages[random.nextInt(motivationMessages.length)];
      await speak(message);

      return VoiceCommandResult(
        success: true,
        action: 'motivation',
        habitName: '',
        message: message,
      );
    } catch (e) {
      const message = '힘내세요! 당신은 할 수 있어요!';
      await speak(message);

      return VoiceCommandResult(
        success: true,
        action: 'motivation',
        habitName: '',
        message: message,
      );
    }
  }

  // 시간 기반 명령 처리
  static Future<VoiceCommandResult> _handleTimeBasedCommand(
      String voiceText) async {
    try {
      final now = DateTime.now();
      final hour = now.hour;
      String timeMessage = '';
      String recommendation = '';

      if (hour >= 6 && hour < 12) {
        timeMessage = '좋은 아침입니다!';
        recommendation = '아침 시간을 활용해서 운동이나 명상을 해보시는 건 어떨까요?';
      } else if (hour >= 12 && hour < 18) {
        timeMessage = '좋은 오후입니다!';
        recommendation = '점심 후 잠깐 산책을 하거나 독서를 해보세요.';
      } else if (hour >= 18 && hour < 22) {
        timeMessage = '좋은 저녁입니다!';
        recommendation = '하루를 마무리하며 일기를 쓰거나 감사 인사를 해보세요.';
      } else {
        timeMessage = '늦은 시간이네요.';
        recommendation = '충분한 수면을 위해 일찍 주무시는 것을 추천드려요.';
      }

      final message = '$timeMessage $recommendation';
      await speak(message);

      return VoiceCommandResult(
        success: true,
        action: 'time_based',
        habitName: '',
        message: message,
      );
    } catch (e) {
      const message = '시간에 맞는 추천을 드릴 수 없습니다.';
      return VoiceCommandResult(
        success: false,
        action: 'time_based',
        habitName: '',
        message: message,
      );
    }
  }

  // 감정적 지원 명령 처리
  static Future<VoiceCommandResult> _handleEmotionalSupportCommand(
      String voiceText) async {
    try {
      final lowerText = voiceText.toLowerCase();
      String supportMessage = '';

      if (lowerText.contains('힘들어') || lowerText.contains('어려워')) {
        supportMessage =
            '힘든 시기를 보내고 계시는군요. 하지만 이런 어려움도 성장의 기회가 될 수 있어요. 작은 습관부터 천천히 시작해보세요.';
      } else if (lowerText.contains('포기')) {
        supportMessage =
            '포기하고 싶은 마음이 드는 것은 자연스러운 일이에요. 잠깐 쉬어가도 괜찮습니다. 다시 시작할 수 있어요.';
      } else if (lowerText.contains('우울') || lowerText.contains('스트레스')) {
        supportMessage =
            '마음이 힘드시군요. 명상이나 가벼운 산책 같은 간단한 습관으로 마음을 달래보세요. 당신은 혼자가 아니에요.';
      } else if (lowerText.contains('지쳐')) {
        supportMessage = '많이 지치셨나요? 충분한 휴식을 취하시고, 무리하지 마세요. 건강이 가장 중요해요.';
      } else {
        supportMessage = '어떤 어려움이든 함께 극복할 수 있어요. 당신의 노력을 응원합니다!';
      }

      await speak(supportMessage);

      return VoiceCommandResult(
        success: true,
        action: 'emotional_support',
        habitName: '',
        message: supportMessage,
      );
    } catch (e) {
      const message = '당신을 응원합니다. 힘내세요!';
      await speak(message);

      return VoiceCommandResult(
        success: true,
        action: 'emotional_support',
        habitName: '',
        message: message,
      );
    }
  }

  // 고급 분석 명령 처리
  static Future<VoiceCommandResult> _handleAnalyticsCommand(
      List<Habit> habits) async {
    try {
      final userStats = await UserStatsService.loadUserStats();

      // 성과 분석
      final completionRate = habits.isNotEmpty
          ? habits.where((h) => h.isCompletedToday()).length /
              habits.length *
              100
          : 0.0;

      // 가장 성공적인 카테고리 찾기
      final categorySuccess = <String, int>{};
      for (final habit in habits) {
        if (habit.isCompletedToday()) {
          categorySuccess[habit.categoryId] =
              (categorySuccess[habit.categoryId] ?? 0) + 1;
        }
      }

      String bestCategory = '없음';
      if (categorySuccess.isNotEmpty) {
        bestCategory = categorySuccess.entries
            .reduce((a, b) => a.value > b.value ? a : b)
            .key;
      }

      // 개선점 분석
      final improvements = <String>[];
      if (completionRate < 50) {
        improvements.add('습관 수를 줄여서 집중도를 높여보세요');
      }
      if (userStats.currentStreak < 7) {
        improvements.add('꾸준함을 위해 더 작은 습관부터 시작해보세요');
      }
      if (habits.length > 10) {
        improvements.add('너무 많은 습관보다는 핵심 습관에 집중해보세요');
      }

      final message = '''분석 결과를 말씀드릴게요.
      오늘 완료율은 ${completionRate.toInt()}%입니다.
      가장 잘하고 있는 카테고리는 $bestCategory입니다.
      현재 ${userStats.currentStreak}일 연속 성공 중이고, 레벨 ${userStats.level}에 도달했습니다.
      ${improvements.isNotEmpty ? '개선점: ${improvements.first}' : '훌륭하게 하고 계십니다!'}''';

      await speak(message);

      return VoiceCommandResult(
        success: true,
        action: 'analytics',
        habitName: '',
        message: message,
      );
    } catch (e) {
      const message = '분석 중 오류가 발생했습니다.';
      return VoiceCommandResult(
        success: false,
        action: 'analytics',
        habitName: '',
        message: message,
      );
    }
  }

  // 지원되는 명령어 목록 (확장됨)
  static List<String> getSupportedCommands() {
    return [
      '"물 마시기 완료" - 습관 완료',
      '"운동하기 취소" - 습관 완료 취소',
      '"독서하기 추가" - 새 습관 추가',
      '"명상하기 삭제" - 습관 삭제',
      '"상태 확인" - 오늘 진행 상황',
      '"추천해줘" - AI 스마트 추천',
      '"동기부여해줘" - 동기부여 메시지',
      '"지금 뭐할까" - 시간 기반 추천',
      '"힘들어" - 감정적 지원',
      '"분석해줘" - 고급 성과 분석',
    ];
  }
}

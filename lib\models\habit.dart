import 'package:flutter/material.dart';
import '../utils/date_utils.dart' as date_utils;
import 'habit_category.dart';

class Habit {
  final String id;
  final String name;
  final DateTime createdDate;
  final Map<String, bool> completedDates;
  final String categoryId;
  final String? description;
  final Color? customColor;

  Habit({
    required this.id,
    required this.name,
    required this.createdDate,
    required this.completedDates,
    required this.categoryId,
    this.description,
    this.customColor,
  });

  // JSON 직렬화
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'createdDate': createdDate.toIso8601String(),
      'completedDates': completedDates,
      'categoryId': categoryId,
      'description': description,
      'customColor': customColor?.value,
    };
  }

  // JSON 역직렬화
  factory Habit.fromJson(Map<String, dynamic> json) {
    return Habit(
      id: json['id'],
      name: json['name'],
      createdDate: DateTime.parse(json['createdDate']),
      completedDates: Map<String, bool>.from(json['completedDates'] ?? {}),
      categoryId: json['categoryId'] ?? 'other',
      description: json['description'],
      customColor:
          json['customColor'] != null ? Color(json['customColor']) : null,
    );
  }

  // 오늘 완료 여부 확인
  bool isCompletedToday() {
    final today = date_utils.DateUtils.getTodayString();
    return completedDates[today] ?? false;
  }

  // 특정 날짜에 완료되었는지 확인
  bool isCompletedOn(DateTime date) {
    final dateStr = date_utils.DateUtils.formatDate(date);
    return completedDates[dateStr] ?? false;
  }

  // 최근 7일간 달성률 계산
  double getWeeklyCompletionRate() {
    final pastWeekDates = date_utils.DateUtils.getPastDays(7);
    int completedDays = 0;

    for (final dateStr in pastWeekDates) {
      if (completedDates[dateStr] == true) {
        completedDays++;
      }
    }

    return completedDays / 7.0;
  }

  // 습관 완료 토글
  Habit toggleCompletion(DateTime date) {
    final dateStr = date_utils.DateUtils.formatDate(date);
    final newCompletedDates = Map<String, bool>.from(completedDates);
    newCompletedDates[dateStr] = !(newCompletedDates[dateStr] ?? false);

    return Habit(
      id: id,
      name: name,
      createdDate: createdDate,
      completedDates: newCompletedDates,
      categoryId: categoryId,
      description: description,
      customColor: customColor,
    );
  }

  // 새로운 습관 생성 팩토리
  factory Habit.create(String name,
      {String? categoryId, String? description, Color? customColor}) {
    return Habit(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      createdDate: DateTime.now(),
      completedDates: {},
      categoryId: categoryId ?? 'other',
      description: description,
      customColor: customColor,
    );
  }
}

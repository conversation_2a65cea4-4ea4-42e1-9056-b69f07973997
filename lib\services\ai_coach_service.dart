import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:geolocator/geolocator.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';
import 'wearable_integration_service.dart';
import 'social_ai_network_service.dart';

// AI 코칭 메시지 타입
enum CoachingType {
  motivation,
  reminder,
  correction,
  celebration,
  warning,
  suggestion,
  environmental,
  social,
}

// AI 코칭 메시지
class AICoachingMessage {
  final String id;
  final CoachingType type;
  final String title;
  final String message;
  final String emoji;
  final DateTime timestamp;
  final double urgency; // 0.0 - 1.0
  final Map<String, dynamic> context;
  final List<String> actionButtons;

  AICoachingMessage({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.emoji,
    required this.timestamp,
    required this.urgency,
    required this.context,
    required this.actionButtons,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.toString(),
    'title': title,
    'message': message,
    'emoji': emoji,
    'timestamp': timestamp.toIso8601String(),
    'urgency': urgency,
    'context': context,
    'actionButtons': actionButtons,
  };
}

// 환경적 요인 데이터
class EnvironmentalContext {
  final String weather;
  final double temperature;
  final int airQuality;
  final String location;
  final bool isWeekend;
  final int timeOfDay; // 0-23
  final String season;
  final double stressLevel;

  EnvironmentalContext({
    required this.weather,
    required this.temperature,
    required this.airQuality,
    required this.location,
    required this.isWeekend,
    required this.timeOfDay,
    required this.season,
    required this.stressLevel,
  });
}

// 24/7 개인 AI 코치 서비스
class AICoachService {
  static bool _isInitialized = false;
  static Timer? _coachingTimer;
  static Timer? _environmentalTimer;
  static List<AICoachingMessage> _messageHistory = [];
  static EnvironmentalContext? _currentEnvironment;
  
  // 실시간 코칭 메시지 스트림
  static final StreamController<AICoachingMessage> _coachingStreamController = 
      StreamController<AICoachingMessage>.broadcast();
  static Stream<AICoachingMessage> get coachingStream => _coachingStreamController.stream;

  // 사용자 행동 패턴 분석 데이터
  static Map<String, dynamic> _behaviorPatterns = {};
  static Map<String, List<DateTime>> _habitCompletionTimes = {};
  static List<double> _dailyMotivationLevels = [];

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 환경적 요인 모니터링 시작
      await _startEnvironmentalMonitoring();
      
      // 사용자 행동 패턴 분석 시작
      await _startBehaviorAnalysis();
      
      // 24/7 코칭 시스템 시작
      _start24x7Coaching();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('AI 코치 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 환경적 요인 모니터링 시작
  static Future<void> _startEnvironmentalMonitoring() async {
    _environmentalTimer = Timer.periodic(const Duration(minutes: 15), (timer) async {
      await _updateEnvironmentalContext();
    });
    
    // 초기 환경 데이터 수집
    await _updateEnvironmentalContext();
  }

  // 환경적 요인 업데이트
  static Future<void> _updateEnvironmentalContext() async {
    try {
      final now = DateTime.now();
      
      // 위치 정보 (권한이 있는 경우)
      String location = 'Unknown';
      try {
        final position = await Geolocator.getCurrentPosition();
        location = '${position.latitude.toStringAsFixed(2)}, ${position.longitude.toStringAsFixed(2)}';
      } catch (e) {
        // 위치 권한이 없거나 오류 발생
      }
      
      // 날씨 정보 (모의 데이터)
      final weather = _generateWeatherData();
      
      // 스트레스 레벨 (웨어러블 데이터 기반)
      double stressLevel = 0.5;
      final wearableData = WearableIntegrationService.getCurrentHealthData();
      if (wearableData != null) {
        stressLevel = wearableData.stressLevel / 5.0; // 0.0 - 1.0 범위로 정규화
      }
      
      _currentEnvironment = EnvironmentalContext(
        weather: weather['condition'],
        temperature: weather['temperature'],
        airQuality: weather['airQuality'],
        location: location,
        isWeekend: now.weekday >= 6,
        timeOfDay: now.hour,
        season: _getCurrentSeason(now),
        stressLevel: stressLevel,
      );
      
      // 환경 변화에 따른 코칭 메시지 생성
      await _generateEnvironmentalCoaching();
      
    } catch (e) {
      print('환경적 요인 업데이트 오류: $e');
    }
  }

  // 사용자 행동 패턴 분석 시작
  static Future<void> _startBehaviorAnalysis() async {
    // 기존 데이터 로드
    await _loadBehaviorPatterns();
    
    // 실시간 패턴 분석
    Timer.periodic(const Duration(hours: 1), (timer) async {
      await _analyzeBehaviorPatterns();
    });
  }

  // 24/7 코칭 시스템 시작
  static void _start24x7Coaching() {
    // 매 5분마다 코칭 기회 확인
    _coachingTimer = Timer.periodic(const Duration(minutes: 5), (timer) async {
      await _checkCoachingOpportunities();
    });
    
    // 특정 시간대별 코칭
    _scheduleTimeBasedCoaching();
  }

  // 코칭 기회 확인
  static Future<void> _checkCoachingOpportunities() async {
    try {
      final now = DateTime.now();
      
      // 1. 습관 완료 시간 체크
      await _checkHabitReminders(now);
      
      // 2. 동기부여 레벨 체크
      await _checkMotivationLevel();
      
      // 3. 스트레스 레벨 체크
      await _checkStressLevel();
      
      // 4. 소셜 활동 체크
      await _checkSocialActivity();
      
      // 5. 환경적 기회 체크
      await _checkEnvironmentalOpportunities();
      
    } catch (e) {
      print('코칭 기회 확인 오류: $e');
    }
  }

  // 습관 리마인더 체크
  static Future<void> _checkHabitReminders(DateTime now) async {
    // 사용자의 습관 완료 패턴 분석
    for (final entry in _habitCompletionTimes.entries) {
      final habitName = entry.key;
      final completionTimes = entry.value;
      
      if (completionTimes.isNotEmpty) {
        // 평균 완료 시간 계산
        final avgHour = completionTimes.map((t) => t.hour).reduce((a, b) => a + b) / completionTimes.length;
        
        // 평균 완료 시간 30분 전에 리마인더
        if (now.hour == (avgHour - 0.5).round() && now.minute < 5) {
          await _sendCoachingMessage(AICoachingMessage(
            id: 'reminder_${now.millisecondsSinceEpoch}',
            type: CoachingType.reminder,
            title: '습관 리마인더 ⏰',
            message: '곧 $habitName 시간이에요! 평소보다 30분 일찍 알려드려요.',
            emoji: '⏰',
            timestamp: now,
            urgency: 0.6,
            context: {'habitName': habitName, 'avgTime': avgHour},
            actionButtons: ['지금 하기', '30분 후 알림', '건너뛰기'],
          ));
        }
      }
    }
  }

  // 동기부여 레벨 체크
  static Future<void> _checkMotivationLevel() async {
    if (_dailyMotivationLevels.isNotEmpty) {
      final currentMotivation = _dailyMotivationLevels.last;
      
      if (currentMotivation < 0.3) { // 낮은 동기부여
        await _sendCoachingMessage(AICoachingMessage(
          id: 'motivation_${DateTime.now().millisecondsSinceEpoch}',
          type: CoachingType.motivation,
          title: '힘내세요! 💪',
          message: 'AI가 감지한 결과, 지금 조금 힘드신 것 같아요. 작은 습관부터 시작해보는 건 어떨까요?',
          emoji: '💪',
          timestamp: DateTime.now(),
          urgency: 0.8,
          context: {'motivationLevel': currentMotivation},
          actionButtons: ['간단한 습관 추천', '동기부여 영상', '친구와 대화'],
        ));
      }
    }
  }

  // 스트레스 레벨 체크
  static Future<void> _checkStressLevel() async {
    if (_currentEnvironment != null && _currentEnvironment!.stressLevel > 0.7) {
      await _sendCoachingMessage(AICoachingMessage(
        id: 'stress_${DateTime.now().millisecondsSinceEpoch}',
        type: CoachingType.warning,
        title: '스트레스 관리 필요 😰',
        message: '웨어러블 데이터를 보니 스트레스 수치가 높아요. 잠깐 휴식을 취해보세요.',
        emoji: '😰',
        timestamp: DateTime.now(),
        urgency: 0.9,
        context: {'stressLevel': _currentEnvironment!.stressLevel},
        actionButtons: ['명상하기', '산책하기', '깊게 숨쉬기'],
      ));
    }
  }

  // 소셜 활동 체크
  static Future<void> _checkSocialActivity() async {
    final friends = SocialAINetworkService.friends;
    final activeFriends = friends.where((f) => f.isOnline).length;
    
    if (activeFriends > 0 && Random().nextDouble() < 0.1) {
      final randomFriend = friends.where((f) => f.isOnline).first;
      
      await _sendCoachingMessage(AICoachingMessage(
        id: 'social_${DateTime.now().millisecondsSinceEpoch}',
        type: CoachingType.social,
        title: '친구와 함께해요! 👥',
        message: '${randomFriend.username}님이 온라인이에요! 함께 챌린지에 참여해보세요.',
        emoji: '👥',
        timestamp: DateTime.now(),
        urgency: 0.4,
        context: {'friendId': randomFriend.id, 'friendName': randomFriend.username},
        actionButtons: ['챌린지 보기', '메시지 보내기', '나중에'],
      ));
    }
  }

  // 환경적 기회 체크
  static Future<void> _checkEnvironmentalOpportunities() async {
    if (_currentEnvironment == null) return;
    
    final env = _currentEnvironment!;
    
    // 날씨가 좋을 때 야외 활동 추천
    if (env.weather == 'sunny' && env.temperature > 15 && env.temperature < 25) {
      await _sendCoachingMessage(AICoachingMessage(
        id: 'weather_${DateTime.now().millisecondsSinceEpoch}',
        type: CoachingType.environmental,
        title: '완벽한 날씨! ☀️',
        message: '지금 날씨가 정말 좋아요! 야외에서 운동하기 딱 좋은 날씨입니다.',
        emoji: '☀️',
        timestamp: DateTime.now(),
        urgency: 0.5,
        context: {'weather': env.weather, 'temperature': env.temperature},
        actionButtons: ['산책하기', '조깅하기', '실내에서 운동'],
      ));
    }
    
    // 주말에 새로운 습관 시도 추천
    if (env.isWeekend && env.timeOfDay >= 10 && env.timeOfDay <= 16) {
      await _sendCoachingMessage(AICoachingMessage(
        id: 'weekend_${DateTime.now().millisecondsSinceEpoch}',
        type: CoachingType.suggestion,
        title: '주말 특별 제안! 🎉',
        message: '주말이니까 평소에 못했던 새로운 습관을 시도해보는 건 어떨까요?',
        emoji: '🎉',
        timestamp: DateTime.now(),
        urgency: 0.3,
        context: {'isWeekend': true, 'timeOfDay': env.timeOfDay},
        actionButtons: ['새 습관 추천', '친구와 함께', '다음에'],
      ));
    }
  }

  // 시간대별 코칭 스케줄링
  static void _scheduleTimeBasedCoaching() {
    // 아침 코칭 (7:00)
    Timer.periodic(const Duration(minutes: 1), (timer) {
      final now = DateTime.now();
      if (now.hour == 7 && now.minute == 0) {
        _sendMorningCoaching();
      } else if (now.hour == 12 && now.minute == 0) {
        _sendLunchCoaching();
      } else if (now.hour == 18 && now.minute == 0) {
        _sendEveningCoaching();
      } else if (now.hour == 22 && now.minute == 0) {
        _sendNightCoaching();
      }
    });
  }

  // 아침 코칭
  static Future<void> _sendMorningCoaching() async {
    await _sendCoachingMessage(AICoachingMessage(
      id: 'morning_${DateTime.now().millisecondsSinceEpoch}',
      type: CoachingType.motivation,
      title: '좋은 아침이에요! 🌅',
      message: 'AI 분석 결과, 아침에 습관을 실천하면 성공률이 85% 높아져요! 오늘도 화이팅!',
      emoji: '🌅',
      timestamp: DateTime.now(),
      urgency: 0.7,
      context: {'timeOfDay': 'morning'},
      actionButtons: ['오늘 계획 보기', '아침 습관 시작', '동기부여 받기'],
    ));
  }

  // 점심 코칭
  static Future<void> _sendLunchCoaching() async {
    await _sendCoachingMessage(AICoachingMessage(
      id: 'lunch_${DateTime.now().millisecondsSinceEpoch}',
      type: CoachingType.reminder,
      title: '점심시간 체크! 🍽️',
      message: '점심 후 15분 산책하면 오후 집중력이 30% 향상돼요! 잠깐 밖으로 나가보세요.',
      emoji: '🍽️',
      timestamp: DateTime.now(),
      urgency: 0.5,
      context: {'timeOfDay': 'lunch'},
      actionButtons: ['산책하기', '스트레칭', '나중에'],
    ));
  }

  // 저녁 코칭
  static Future<void> _sendEveningCoaching() async {
    await _sendCoachingMessage(AICoachingMessage(
      id: 'evening_${DateTime.now().millisecondsSinceEpoch}',
      type: CoachingType.suggestion,
      title: '하루 마무리 시간! 🌆',
      message: '오늘 하루 어떠셨나요? 저녁 시간을 활용해서 자기계발 습관을 실천해보세요.',
      emoji: '🌆',
      timestamp: DateTime.now(),
      urgency: 0.6,
      context: {'timeOfDay': 'evening'},
      actionButtons: ['독서하기', '일기쓰기', '명상하기'],
    ));
  }

  // 밤 코칭
  static Future<void> _sendNightCoaching() async {
    await _sendCoachingMessage(AICoachingMessage(
      id: 'night_${DateTime.now().millisecondsSinceEpoch}',
      type: CoachingType.reminder,
      title: '숙면 준비! 🌙',
      message: '좋은 수면을 위해 스마트폰을 내려놓고 릴렉스 타임을 가져보세요.',
      emoji: '🌙',
      timestamp: DateTime.now(),
      urgency: 0.8,
      context: {'timeOfDay': 'night'},
      actionButtons: ['수면 모드', '명상하기', '내일 계획'],
    ));
  }

  // 코칭 메시지 전송
  static Future<void> _sendCoachingMessage(AICoachingMessage message) async {
    _messageHistory.add(message);
    
    // 최근 100개 메시지만 유지
    if (_messageHistory.length > 100) {
      _messageHistory.removeAt(0);
    }
    
    _coachingStreamController.add(message);
  }

  // 보조 메서드들
  static Map<String, dynamic> _generateWeatherData() {
    final conditions = ['sunny', 'cloudy', 'rainy', 'snowy'];
    final random = Random();
    
    return {
      'condition': conditions[random.nextInt(conditions.length)],
      'temperature': random.nextInt(30) + 5, // 5-35도
      'airQuality': random.nextInt(100) + 1, // 1-100
    };
  }

  static String _getCurrentSeason(DateTime date) {
    final month = date.month;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  static Future<void> _loadBehaviorPatterns() async {
    // 실제로는 로컬 저장소에서 로드
    _behaviorPatterns = {
      'preferredTimes': [7, 12, 18], // 선호하는 시간대
      'successRate': 0.75,
      'motivationPattern': 'morning_high',
    };
  }

  static Future<void> _analyzeBehaviorPatterns() async {
    // 행동 패턴 분석 로직
    // 실제로는 더 복잡한 ML 분석 수행
  }

  static Future<void> _generateEnvironmentalCoaching() async {
    // 환경 변화에 따른 코칭 메시지 생성
  }

  // Getter 메서드들
  static List<AICoachingMessage> get messageHistory => _messageHistory;
  static EnvironmentalContext? get currentEnvironment => _currentEnvironment;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _coachingTimer?.cancel();
    _environmentalTimer?.cancel();
    _coachingStreamController.close();
    _isInitialized = false;
  }
}

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:camera/camera.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/ar_pose_detection_service.dart';

class ARWorkoutScreen extends StatefulWidget {
  final String exerciseType;

  const ARWorkoutScreen({
    super.key,
    required this.exerciseType,
  });

  @override
  State<ARWorkoutScreen> createState() => _ARWorkoutScreenState();
}

class _ARWorkoutScreenState extends State<ARWorkoutScreen>
    with TickerProviderStateMixin {
  bool _isInitialized = false;
  bool _isSessionActive = false;
  PoseAnalysisResult? _currentResult;
  late AnimationController _pulseController;
  late AnimationController _countController;
  int _sessionDuration = 0;
  int _lastRepCount = 0;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _countController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _initializeAR();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _countController.dispose();
    ARPoseDetectionService.dispose();
    super.dispose();
  }

  Future<void> _initializeAR() async {
    final initialized = await ARPoseDetectionService.initialize();
    setState(() {
      _isInitialized = initialized;
    });

    if (!initialized) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('AR 기능을 사용할 수 없습니다. 카메라 권한을 확인해주세요.'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _startWorkoutSession() async {
    if (!_isInitialized) return;

    try {
      await ARPoseDetectionService.startExerciseSession(widget.exerciseType);
      setState(() {
        _isSessionActive = true;
        _sessionDuration = 0;
        _lastRepCount = 0;
      });

      _pulseController.repeat();

      // 1초마다 결과 업데이트
      Timer.periodic(const Duration(seconds: 1), (timer) {
        if (!_isSessionActive) {
          timer.cancel();
          return;
        }

        setState(() {
          _sessionDuration++;
          _currentResult = ARPoseDetectionService.getCurrentResult();

          // 새로운 반복 감지 시 애니메이션
          if (_currentResult != null &&
              _currentResult!.repetitions > _lastRepCount) {
            _lastRepCount = _currentResult!.repetitions;
            _countController.forward().then((_) => _countController.reset());
          }
        });
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('운동 세션 시작 오류: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _stopWorkoutSession() async {
    await ARPoseDetectionService.stopExerciseSession();
    setState(() {
      _isSessionActive = false;
    });
    _pulseController.stop();

    // 결과 다이얼로그 표시
    if (_currentResult != null && mounted) {
      _showWorkoutResults();
    }
  }

  void _showWorkoutResults() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        title: const Text('운동 완료! 🎉'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              '${widget.exerciseType.toUpperCase()}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildResultItem('반복 횟수', '${_currentResult!.repetitions}회'),
                _buildResultItem('운동 시간', '${_sessionDuration}초'),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildResultItem(
                    '정확도', '${(_currentResult!.accuracy).toInt()}%'),
                _buildResultItem(
                    '자세', _currentResult!.isCorrectForm ? '좋음' : '개선 필요'),
              ],
            ),
            if (_currentResult!.feedback.isNotEmpty) ...[
              const SizedBox(height: 16),
              const Text(
                '피드백:',
                style: TextStyle(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              ..._currentResult!.feedback.map(
                (feedback) => Padding(
                  padding: const EdgeInsets.only(bottom: 4),
                  child: Text(
                    '• $feedback',
                    style: const TextStyle(fontSize: 14),
                  ),
                ),
              ),
            ],
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop(); // AR 화면도 닫기
            },
            child: const Text('완료'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _startWorkoutSession(); // 다시 시작
            },
            child: const Text('다시 하기'),
          ),
        ],
      ),
    );
  }

  Widget _buildResultItem(String label, String value) {
    return Column(
      children: [
        Text(
          value,
          style: const TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.bold,
            color: Colors.blue,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            color: Colors.grey,
          ),
        ),
      ],
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text(
          'AR ${widget.exerciseType.toUpperCase()}',
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: Colors.black,
        iconTheme: const IconThemeData(color: Colors.white),
        actions: [
          if (_isSessionActive)
            IconButton(
              icon: const Icon(Icons.stop),
              onPressed: _stopWorkoutSession,
              tooltip: '운동 중지',
            ),
        ],
      ),
      body: _isInitialized
          ? Stack(
              children: [
                // 카메라 프리뷰
                _buildCameraPreview(),

                // AR 오버레이
                _buildAROverlay(),

                // 운동 정보 패널
                _buildInfoPanel(),

                // 시작/중지 버튼
                if (!_isSessionActive) _buildStartButton(),
              ],
            )
          : const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: Colors.white),
                  SizedBox(height: 16),
                  Text(
                    'AR 시스템 초기화 중...',
                    style: TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildCameraPreview() {
    final cameraController = ARPoseDetectionService.cameraController;

    if (cameraController == null || !cameraController.value.isInitialized) {
      return const Center(
        child: Text(
          '카메라 초기화 중...',
          style: TextStyle(color: Colors.white),
        ),
      );
    }

    return SizedBox.expand(
      child: CameraPreview(cameraController),
    );
  }

  Widget _buildAROverlay() {
    return Positioned.fill(
      child: CustomPaint(
        painter: AROverlayPainter(
          exerciseType: widget.exerciseType,
          isSessionActive: _isSessionActive,
          currentResult: _currentResult,
        ),
      ),
    );
  }

  Widget _buildInfoPanel() {
    return Positioned(
      top: 20,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.7),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            if (_isSessionActive) ...[
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceAround,
                children: [
                  _buildInfoItem(
                    '반복 횟수',
                    '${_currentResult?.repetitions ?? 0}',
                    Icons.repeat,
                    Colors.blue,
                  ),
                  _buildInfoItem(
                    '운동 시간',
                    '${_sessionDuration}s',
                    Icons.timer,
                    Colors.green,
                  ),
                  _buildInfoItem(
                    '정확도',
                    '${(_currentResult?.accuracy ?? 0).toInt()}%',
                    Icons.track_changes,
                    Colors.orange,
                  ),
                ],
              ),
              if (_currentResult?.feedback.isNotEmpty == true) ...[
                const SizedBox(height: 12),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Text(
                    _currentResult!.feedback.first,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ] else ...[
              Text(
                '${widget.exerciseType.toUpperCase()} 준비',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 8),
              const Text(
                '카메라 앞에 서서 운동 자세를 취해주세요',
                style: TextStyle(
                  color: Colors.white70,
                  fontSize: 14,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ],
        ),
      ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),
    );
  }

  Widget _buildInfoItem(
      String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        AnimatedBuilder(
          animation: _countController,
          builder: (context, child) {
            return Transform.scale(
              scale: 1.0 + (_countController.value * 0.3),
              child: Icon(
                icon,
                color: color,
                size: 24,
              ),
            );
          },
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: const TextStyle(
            color: Colors.white70,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildStartButton() {
    return Positioned(
      bottom: 50,
      left: 50,
      right: 50,
      child: AnimatedBuilder(
        animation: _pulseController,
        builder: (context, child) {
          return Transform.scale(
            scale: 1.0 + (_pulseController.value * 0.1),
            child: ElevatedButton(
              onPressed: _startWorkoutSession,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.red,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(30),
                ),
              ),
              child: const Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.play_arrow, size: 28),
                  SizedBox(width: 8),
                  Text(
                    '운동 시작',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ).animate().fadeIn(duration: 800.ms).slideY(begin: 0.5, end: 0),
    );
  }
}

// AR 오버레이 페인터
class AROverlayPainter extends CustomPainter {
  final String exerciseType;
  final bool isSessionActive;
  final PoseAnalysisResult? currentResult;

  AROverlayPainter({
    required this.exerciseType,
    required this.isSessionActive,
    this.currentResult,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (!isSessionActive) return;

    final paint = Paint()
      ..color = Colors.green.withValues(alpha: 0.3)
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    // 운동별 가이드라인 그리기
    switch (exerciseType.toLowerCase()) {
      case 'pushup':
        _drawPushupGuide(canvas, size, paint);
        break;
      case 'squat':
        _drawSquatGuide(canvas, size, paint);
        break;
      case 'plank':
        _drawPlankGuide(canvas, size, paint);
        break;
    }

    // 자세 정확도에 따른 피드백 표시
    if (currentResult != null) {
      final feedbackColor =
          currentResult!.isCorrectForm ? Colors.green : Colors.red;
      paint.color = feedbackColor.withValues(alpha: 0.5);

      // 화면 테두리에 피드백 색상 표시
      canvas.drawRect(
        Rect.fromLTWH(0, 0, size.width, size.height),
        paint,
      );
    }
  }

  void _drawPushupGuide(Canvas canvas, Size size, Paint paint) {
    // 팔굽혀펴기 가이드라인 (간단한 버전)
    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // 손 위치 가이드
    canvas.drawCircle(Offset(centerX - 100, centerY), 20, paint);
    canvas.drawCircle(Offset(centerX + 100, centerY), 20, paint);

    // 몸 중심선
    canvas.drawLine(
      Offset(centerX, centerY - 100),
      Offset(centerX, centerY + 100),
      paint,
    );
  }

  void _drawSquatGuide(Canvas canvas, Size size, Paint paint) {
    // 스쿼트 가이드라인
    final centerX = size.width / 2;
    final bottomY = size.height * 0.8;

    // 발 위치 가이드
    canvas.drawCircle(Offset(centerX - 50, bottomY), 15, paint);
    canvas.drawCircle(Offset(centerX + 50, bottomY), 15, paint);

    // 무릎 각도 가이드
    canvas.drawArc(
      Rect.fromCenter(
        center: Offset(centerX, bottomY - 100),
        width: 100,
        height: 100,
      ),
      0,
      1.57, // 90도
      false,
      paint,
    );
  }

  void _drawPlankGuide(Canvas canvas, Size size, Paint paint) {
    // 플랭크 가이드라인
    final centerY = size.height / 2;

    // 몸 직선 가이드
    canvas.drawLine(
      Offset(size.width * 0.2, centerY),
      Offset(size.width * 0.8, centerY),
      paint,
    );

    // 팔꿈치 위치 가이드
    canvas.drawCircle(Offset(size.width * 0.3, centerY), 15, paint);
    canvas.drawCircle(Offset(size.width * 0.7, centerY), 15, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

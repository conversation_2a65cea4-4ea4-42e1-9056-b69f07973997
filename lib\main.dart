import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'services/theme_service.dart';
import 'services/personal_ai_coach_service.dart';
import 'services/cloud_ai_service.dart';
import 'services/ar_pose_analysis_service.dart';
import 'services/wearable_integration_service.dart';
import 'services/social_ai_network_service.dart';
import 'services/ai_coach_service.dart';
import 'services/advanced_analytics_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 고급 AI 서비스들 초기화
  print('🚀 고급 AI 습관 추적기 시작...');

  // 기본 AI 서비스
  await PersonalAICoachService.initialize();
  await CloudAIService.initialize();

  // AR 자세 분석 서비스
  await ARPoseAnalysisService.initialize();
  print('✅ AR 자세 분석 시스템 초기화 완료');

  // 웨어러블 연동 서비스
  await WearableIntegrationService.initialize();
  print('✅ 웨어러블 디바이스 연동 완료');

  // 소셜 AI 네트워크 서비스
  await SocialAINetworkService.initialize(
      'user_${DateTime.now().millisecondsSinceEpoch}');
  print('✅ 소셜 AI 네트워크 연결 완료');

  // 24/7 AI 코치 서비스
  await AICoachService.initialize();
  print('✅ 24/7 개인 AI 코치 시작');

  // 고급 분석 서비스
  await AdvancedAnalyticsService.initialize();
  print('✅ 고급 데이터 분석 시스템 준비 완료');

  print('🎉 모든 고급 AI 기능이 활성화되었습니다!');

  runApp(
    ChangeNotifierProvider(
      create: (context) => ThemeService(),
      child: const HabitTrackerApp(),
    ),
  );
}

class HabitTrackerApp extends StatelessWidget {
  const HabitTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return MaterialApp(
          title: '습관 트래커',
          debugShowCheckedModeBanner: false,
          theme: themeService.currentTheme.themeData,
          home: const HomeScreen(),
        );
      },
    );
  }
}

import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'services/theme_service.dart';
import 'services/personal_ai_coach_service.dart';
import 'services/cloud_ai_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // AI 서비스들 초기화
  await PersonalAICoachService.initialize();
  await CloudAIService.initialize();

  runApp(
    ChangeNotifierProvider(
      create: (context) => ThemeService(),
      child: const HabitTrackerApp(),
    ),
  );
}

class HabitTrackerApp extends StatelessWidget {
  const HabitTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return MaterialApp(
          title: '습관 트래커',
          debugShowCheckedModeBanner: false,
          theme: themeService.currentTheme.themeData,
          home: const HomeScreen(),
        );
      },
    );
  }
}

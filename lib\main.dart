import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/home_screen.dart';
import 'services/theme_service.dart';
import 'services/personal_ai_coach_service.dart';
import 'services/cloud_ai_service.dart';
import 'services/ar_pose_analysis_service.dart';
import 'services/wearable_integration_service.dart';
import 'services/social_ai_network_service.dart';
import 'services/ai_coach_service.dart';
import 'services/advanced_analytics_service.dart';
import 'services/brainwave_monitoring_service.dart';
import 'services/hologram_trainer_service.dart';
import 'services/blockchain_nft_service.dart';
import 'services/ai_voice_synthesis_service.dart';
import 'services/metaverse_habit_space_service.dart';
import 'services/quantum_pattern_analysis_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // 고급 AI 서비스들 초기화
  print('🚀 고급 AI 습관 추적기 시작...');

  // 기본 AI 서비스
  await PersonalAICoachService.initialize();
  await CloudAIService.initialize();

  // AR 자세 분석 서비스
  await ARPoseAnalysisService.initialize();
  print('✅ AR 자세 분석 시스템 초기화 완료');

  // 웨어러블 연동 서비스
  await WearableIntegrationService.initialize();
  print('✅ 웨어러블 디바이스 연동 완료');

  // 소셜 AI 네트워크 서비스
  await SocialAINetworkService.initialize(
      'user_${DateTime.now().millisecondsSinceEpoch}');
  print('✅ 소셜 AI 네트워크 연결 완료');

  // 24/7 AI 코치 서비스
  await AICoachService.initialize();
  print('✅ 24/7 개인 AI 코치 시작');

  // 고급 분석 서비스
  await AdvancedAnalyticsService.initialize();
  print('✅ 고급 데이터 분석 시스템 준비 완료');

  print('🚀 혁신적 차세대 기능들 초기화 중...');

  // 뇌파 모니터링 서비스
  await BrainwaveMonitoringService.initialize();
  print('🧠 뇌파 기반 집중력 모니터링 시스템 활성화');

  // 홀로그램 트레이너 서비스
  await HologramTrainerService.initialize();
  print('🎭 3D 홀로그램 가상 트레이너 시스템 준비 완료');

  // 블록체인 NFT 서비스
  await BlockchainNFTService.initialize();
  print('⛓️ 블록체인 기반 습관 NFT 보상 시스템 연결');

  // AI 음성 합성 서비스
  await AIVoiceSynthesisService.initialize();
  print('🎤 AI 개인 맞춤형 음성 코치 준비 완료');

  // 메타버스 습관 공간 서비스
  await MetaverseHabitSpaceService.initialize();
  print('🌐 메타버스 가상 습관 공간 연결 완료');

  // 양자 컴퓨팅 패턴 분석 서비스
  await QuantumPatternAnalysisService.initialize();
  print('⚛️ 양자 컴퓨팅 기반 초고속 패턴 분석 시스템 온라인');

  print('🎉 모든 혁신적 AI 기능이 활성화되었습니다!');
  print('🌟 세계 최초의 차세대 습관 추적기가 준비되었습니다!');

  runApp(
    ChangeNotifierProvider(
      create: (context) => ThemeService(),
      child: const HabitTrackerApp(),
    ),
  );
}

class HabitTrackerApp extends StatelessWidget {
  const HabitTrackerApp({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<ThemeService>(
      builder: (context, themeService, child) {
        return MaterialApp(
          title: '습관 트래커',
          debugShowCheckedModeBanner: false,
          theme: themeService.currentTheme.themeData,
          home: const HomeScreen(),
        );
      },
    );
  }
}

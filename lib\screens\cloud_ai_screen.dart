import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/cloud_ai_service.dart';
import '../services/habit_service.dart';
import '../services/user_stats_service.dart';

class CloudAIScreen extends StatefulWidget {
  const CloudAIScreen({super.key});

  @override
  State<CloudAIScreen> createState() => _CloudAIScreenState();
}

class _CloudAIScreenState extends State<CloudAIScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _loadingController;

  bool _isLoading = false;
  bool _isConnected = false;
  List<Map<String, dynamic>> _recommendations = [];
  List<String> _motivationMessages = [];
  Map<String, dynamic> _behaviorAnalysis = {};
  Map<String, dynamic> _predictions = {};

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadingController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _checkConnection();
    _loadAllData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _loadingController.dispose();
    super.dispose();
  }

  Future<void> _checkConnection() async {
    setState(() {
      _isConnected = CloudAIService.isConnected;
    });
  }

  Future<void> _loadAllData() async {
    setState(() {
      _isLoading = true;
    });
    _loadingController.repeat();

    try {
      final results = await Future.wait([
        CloudAIService.getAdvancedRecommendations(),
        CloudAIService.generatePersonalizedMotivation(),
        CloudAIService.analyzeUserBehavior(),
        CloudAIService.predictHabitFailures(),
      ]);

      setState(() {
        _recommendations = results[0] as List<Map<String, dynamic>>;
        _motivationMessages = results[1] as List<String>;
        _behaviorAnalysis = results[2] as Map<String, dynamic>;
        _predictions = results[3] as Map<String, dynamic>;
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('데이터 로딩 오류: $e')),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
      _loadingController.stop();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '☁️ 클라우드 AI',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: const [
            Tab(icon: Icon(Icons.recommend), text: '추천'),
            Tab(icon: Icon(Icons.analytics), text: '분석'),
            Tab(icon: Icon(Icons.trending_up), text: '예측'),
            Tab(icon: Icon(Icons.psychology), text: '동기부여'),
          ],
        ),
        actions: [
          IconButton(
            icon: Icon(
              _isConnected ? Icons.cloud_done : Icons.cloud_off,
              color: _isConnected ? Colors.green : Colors.red,
            ),
            onPressed: _checkConnection,
            tooltip: _isConnected ? '클라우드 연결됨' : '클라우드 연결 안됨',
          ),
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: _isLoading ? null : _loadAllData,
            tooltip: '새로고침',
          ),
        ],
      ),
      body: _isLoading
          ? _buildLoadingScreen()
          : TabBarView(
              controller: _tabController,
              children: [
                _buildRecommendationsTab(),
                _buildAnalysisTab(),
                _buildPredictionsTab(),
                _buildMotivationTab(),
              ],
            ),
    );
  }

  Widget _buildLoadingScreen() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          AnimatedBuilder(
            animation: _loadingController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _loadingController.value * 2 * 3.14159,
                child: const Icon(
                  Icons.cloud_sync,
                  size: 64,
                  color: Colors.blue,
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          const Text(
            '클라우드 AI 분석 중...',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          const Text(
            '고급 머신러닝 모델이 당신의 데이터를 분석하고 있습니다',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationsTab() {
    return _recommendations.isEmpty
        ? _buildEmptyState('추천', '클라우드 AI 추천을 받으려면\n새로고침을 눌러주세요')
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _recommendations.length,
            itemBuilder: (context, index) {
              final recommendation = _recommendations[index];
              return _buildRecommendationCard(recommendation, index);
            },
          );
  }

  Widget _buildAnalysisTab() {
    if (_behaviorAnalysis.isEmpty) {
      return _buildEmptyState('분석', '행동 패턴 분석 데이터가 없습니다');
    }

    final patterns = _behaviorAnalysis['patterns'] as List? ?? [];
    final insights = _behaviorAnalysis['insights'] as List? ?? [];
    final recommendations = _behaviorAnalysis['recommendations'] as List? ?? [];

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        _buildAnalysisSection('🔍 행동 패턴', patterns),
        const SizedBox(height: 16),
        _buildAnalysisSection('💡 인사이트', insights),
        const SizedBox(height: 16),
        _buildAnalysisSection('📋 개선 방안', recommendations),
      ],
    );
  }

  Widget _buildPredictionsTab() {
    if (_predictions.isEmpty) {
      return _buildEmptyState('예측', '습관 실패 예측 데이터가 없습니다');
    }

    final predictions = _predictions['predictions'] as List? ?? [];
    final overallRisk = _predictions['overallRisk'] as String? ?? 'unknown';
    final recommendations = _predictions['recommendations'] as List? ?? [];

    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        // 전체 위험도 카드
        Card(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                Icon(
                  _getRiskIcon(overallRisk),
                  size: 48,
                  color: _getRiskColor(overallRisk),
                ),
                const SizedBox(height: 12),
                Text(
                  '전체 위험도: ${_getRiskDisplayName(overallRisk)}',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: _getRiskColor(overallRisk),
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  _getRiskDescription(overallRisk),
                  style: const TextStyle(fontSize: 14),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          ),
        )
            .animate()
            .fadeIn(duration: 600.ms)
            .scale(begin: const Offset(0.8, 0.8)),

        const SizedBox(height: 16),

        if (predictions.isNotEmpty) ...[
          const Text(
            '개별 습관 예측',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...predictions.asMap().entries.map((entry) {
            return _buildPredictionCard(entry.value, entry.key);
          }).toList(),
        ],

        if (recommendations.isNotEmpty) ...[
          const SizedBox(height: 16),
          const Text(
            '예방 조치',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          ...recommendations
              .map((rec) => Card(
                    child: ListTile(
                      leading:
                          const Icon(Icons.lightbulb, color: Colors.orange),
                      title: Text(rec.toString()),
                    ),
                  ))
              .toList(),
        ],
      ],
    );
  }

  Widget _buildMotivationTab() {
    return _motivationMessages.isEmpty
        ? _buildEmptyState('동기부여', '개인화된 동기부여 메시지가 없습니다')
        : ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _motivationMessages.length,
            itemBuilder: (context, index) {
              final message = _motivationMessages[index];
              return _buildMotivationCard(message, index);
            },
          );
  }

  Widget _buildEmptyState(String title, String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.cloud_off,
            size: 64,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 16),
          Text(
            message,
            style: TextStyle(
              fontSize: 16,
              color: Colors.grey[600],
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _loadAllData,
            icon: const Icon(Icons.refresh),
            label: const Text('다시 시도'),
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendationCard(
      Map<String, dynamic> recommendation, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.blue.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '신뢰도 ${((recommendation['confidence'] ?? 0.0) * 100).toInt()}%',
                    style: const TextStyle(
                      fontSize: 12,
                      color: Colors.blue,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              recommendation['name'] ?? '추천 습관',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              recommendation['description'] ?? '',
              style: const TextStyle(fontSize: 14),
            ),
            if (recommendation['reason'] != null) ...[
              const SizedBox(height: 8),
              Text(
                '추천 이유: ${recommendation['reason']}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
            const SizedBox(height: 12),
            ElevatedButton(
              onPressed: () {
                _addRecommendedHabit(recommendation);
              },
              child: const Text('습관 추가'),
            ),
          ],
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0);
  }

  Widget _buildAnalysisSection(String title, List items) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            if (items.isEmpty)
              const Text('데이터가 없습니다')
            else
              ...items
                  .map((item) => Padding(
                        padding: const EdgeInsets.only(bottom: 8),
                        child: Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const Text('• ',
                                style: TextStyle(fontWeight: FontWeight.bold)),
                            Expanded(child: Text(item.toString())),
                          ],
                        ),
                      ))
                  .toList(),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionCard(Map<String, dynamic> prediction, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      child: ListTile(
        leading: Icon(
          Icons.warning,
          color: _getRiskColor(prediction['risk'] ?? 'low'),
        ),
        title: Text(prediction['habitName'] ?? '습관'),
        subtitle: Text(prediction['reason'] ?? '예측 정보 없음'),
        trailing: Chip(
          label: Text(_getRiskDisplayName(prediction['risk'] ?? 'low')),
          backgroundColor:
              _getRiskColor(prediction['risk'] ?? 'low').withValues(alpha: 0.2),
        ),
      ),
    ).animate(delay: (index * 100).ms).fadeIn(duration: 400.ms);
  }

  Widget _buildMotivationCard(String message, int index) {
    return Card(
      margin: const EdgeInsets.only(bottom: 12),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            const Icon(
              Icons.favorite,
              color: Colors.red,
              size: 24,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
          ],
        ),
      ),
    )
        .animate(delay: (index * 150).ms)
        .fadeIn(duration: 500.ms)
        .slideX(begin: -0.3, end: 0);
  }

  void _addRecommendedHabit(Map<String, dynamic> recommendation) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('습관 추가'),
        content: Text('${recommendation['name']} 습관을 추가하시겠습니까?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () async {
              Navigator.pop(context);
              // 실제 습관 추가 로직
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('습관이 추가되었습니다!')),
              );
            },
            child: const Text('추가'),
          ),
        ],
      ),
    );
  }

  IconData _getRiskIcon(String risk) {
    switch (risk.toLowerCase()) {
      case 'high':
        return Icons.error;
      case 'medium':
        return Icons.warning;
      case 'low':
        return Icons.check_circle;
      default:
        return Icons.help;
    }
  }

  Color _getRiskColor(String risk) {
    switch (risk.toLowerCase()) {
      case 'high':
        return Colors.red;
      case 'medium':
        return Colors.orange;
      case 'low':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  String _getRiskDisplayName(String risk) {
    switch (risk.toLowerCase()) {
      case 'high':
        return '높음';
      case 'medium':
        return '보통';
      case 'low':
        return '낮음';
      default:
        return '알 수 없음';
    }
  }

  String _getRiskDescription(String risk) {
    switch (risk.toLowerCase()) {
      case 'high':
        return '습관 실패 위험이 높습니다. 즉시 조치가 필요합니다.';
      case 'medium':
        return '습관 실패 위험이 보통입니다. 주의가 필요합니다.';
      case 'low':
        return '습관 실패 위험이 낮습니다. 잘 하고 계십니다!';
      default:
        return '위험도를 분석할 수 없습니다.';
    }
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';

// 습관 NFT 모델
class HabitNFT {
  final String tokenId;
  final String name;
  final String description;
  final String imageUrl;
  final String rarity; // common, rare, epic, legendary, mythic
  final Map<String, dynamic> attributes;
  final DateTime mintedDate;
  final String habitType;
  final int streakAchieved;
  final double marketValue; // ETH 단위
  final String ownerAddress;

  HabitNFT({
    required this.tokenId,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.rarity,
    required this.attributes,
    required this.mintedDate,
    required this.habitType,
    required this.streakAchieved,
    required this.marketValue,
    required this.ownerAddress,
  });

  Map<String, dynamic> toJson() => {
    'tokenId': tokenId,
    'name': name,
    'description': description,
    'imageUrl': imageUrl,
    'rarity': rarity,
    'attributes': attributes,
    'mintedDate': mintedDate.toIso8601String(),
    'habitType': habitType,
    'streakAchieved': streakAchieved,
    'marketValue': marketValue,
    'ownerAddress': ownerAddress,
  };
}

// 블록체인 트랜잭션
class BlockchainTransaction {
  final String txHash;
  final String fromAddress;
  final String toAddress;
  final String type; // mint, transfer, trade
  final double gasUsed;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  BlockchainTransaction({
    required this.txHash,
    required this.fromAddress,
    required this.toAddress,
    required this.type,
    required this.gasUsed,
    required this.timestamp,
    required this.data,
  });
}

// 블록체인 기반 습관 NFT 보상 시스템
class BlockchainNFTService {
  static bool _isInitialized = false;
  static String _userWalletAddress = '';
  static List<HabitNFT> _userNFTs = [];
  static List<BlockchainTransaction> _transactionHistory = [];
  static double _userTokenBalance = 0.0; // HABIT 토큰 잔액
  
  // 실시간 NFT 이벤트 스트림
  static final StreamController<Map<String, dynamic>> _nftEventStreamController = 
      StreamController<Map<String, dynamic>>.broadcast();
  static Stream<Map<String, dynamic>> get nftEventStream => _nftEventStreamController.stream;

  // NFT 희귀도별 확률
  static const Map<String, double> _rarityProbabilities = {
    'common': 0.60,     // 60%
    'rare': 0.25,       // 25%
    'epic': 0.10,       // 10%
    'legendary': 0.04,  // 4%
    'mythic': 0.01,     // 1%
  };

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 블록체인 네트워크 연결
      await _connectToBlockchain();
      
      // 사용자 지갑 생성/연결
      await _initializeWallet();
      
      // 스마트 컨트랙트 연결
      await _connectToSmartContract();
      
      // 기존 NFT 로드
      await _loadUserNFTs();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('블록체인 NFT 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 블록체인 네트워크 연결
  static Future<void> _connectToBlockchain() async {
    print('⛓️ 블록체인 네트워크 연결 중...');
    await Future.delayed(const Duration(seconds: 2));
    print('✅ Ethereum 메인넷 연결 완료');
  }

  // 사용자 지갑 초기화
  static Future<void> _initializeWallet() async {
    // 실제로는 MetaMask나 WalletConnect 연동
    _userWalletAddress = _generateWalletAddress();
    _userTokenBalance = 100.0; // 초기 HABIT 토큰 지급
    print('💰 지갑 주소: $_userWalletAddress');
    print('💎 초기 HABIT 토큰: $_userTokenBalance');
  }

  // 스마트 컨트랙트 연결
  static Future<void> _connectToSmartContract() async {
    print('📜 스마트 컨트랙트 연결 중...');
    await Future.delayed(const Duration(seconds: 1));
    print('✅ HabitNFT 컨트랙트 연결 완료');
  }

  // 기존 NFT 로드
  static Future<void> _loadUserNFTs() async {
    // 블록체인에서 사용자 NFT 조회
    _userNFTs = []; // 초기에는 빈 상태
  }

  // 습관 달성 시 NFT 민팅
  static Future<HabitNFT?> mintHabitNFT({
    required String habitName,
    required String habitType,
    required int streakAchieved,
    required Map<String, dynamic> achievementData,
  }) async {
    try {
      // 민팅 조건 확인
      if (!_canMintNFT(streakAchieved)) {
        return null;
      }

      // NFT 희귀도 결정
      final rarity = _determineRarity(streakAchieved, achievementData);
      
      // NFT 메타데이터 생성
      final nft = await _createNFTMetadata(
        habitName: habitName,
        habitType: habitType,
        streakAchieved: streakAchieved,
        rarity: rarity,
        achievementData: achievementData,
      );

      // 블록체인에 민팅 트랜잭션 전송
      final txHash = await _sendMintTransaction(nft);
      
      // NFT를 사용자 컬렉션에 추가
      _userNFTs.add(nft);
      
      // 이벤트 발생
      _nftEventStreamController.add({
        'type': 'nft_minted',
        'nft': nft.toJson(),
        'txHash': txHash,
        'timestamp': DateTime.now(),
      });

      // HABIT 토큰 보상 지급
      final tokenReward = _calculateTokenReward(rarity);
      _userTokenBalance += tokenReward;

      print('🎉 NFT 민팅 완료!');
      print('📛 이름: ${nft.name}');
      print('⭐ 희귀도: ${nft.rarity}');
      print('💰 토큰 보상: $tokenReward HABIT');

      return nft;
    } catch (e) {
      print('NFT 민팅 오류: $e');
      return null;
    }
  }

  // 민팅 조건 확인
  static bool _canMintNFT(int streakAchieved) {
    // 7일, 30일, 100일, 365일 스트릭에서 NFT 민팅 가능
    final milestones = [7, 30, 100, 365];
    return milestones.contains(streakAchieved);
  }

  // 희귀도 결정
  static String _determineRarity(int streakAchieved, Map<String, dynamic> achievementData) {
    final random = Random();
    double rarityBonus = 0.0;

    // 스트릭 기반 희귀도 보너스
    if (streakAchieved >= 365) rarityBonus += 0.3;
    else if (streakAchieved >= 100) rarityBonus += 0.2;
    else if (streakAchieved >= 30) rarityBonus += 0.1;

    // 성과 기반 희귀도 보너스
    final completionRate = achievementData['completionRate'] ?? 0.0;
    if (completionRate >= 0.95) rarityBonus += 0.15;

    // 희귀도 결정
    final roll = random.nextDouble() + rarityBonus;
    
    if (roll >= 0.99) return 'mythic';
    if (roll >= 0.95) return 'legendary';
    if (roll >= 0.85) return 'epic';
    if (roll >= 0.60) return 'rare';
    return 'common';
  }

  // NFT 메타데이터 생성
  static Future<HabitNFT> _createNFTMetadata({
    required String habitName,
    required String habitType,
    required int streakAchieved,
    required String rarity,
    required Map<String, dynamic> achievementData,
  }) async {
    final tokenId = _generateTokenId();
    final name = _generateNFTName(habitName, streakAchieved, rarity);
    final description = _generateNFTDescription(habitName, streakAchieved, achievementData);
    final imageUrl = _generateNFTImage(habitType, rarity, streakAchieved);
    final attributes = _generateNFTAttributes(habitType, streakAchieved, rarity, achievementData);
    final marketValue = _calculateMarketValue(rarity, streakAchieved);

    return HabitNFT(
      tokenId: tokenId,
      name: name,
      description: description,
      imageUrl: imageUrl,
      rarity: rarity,
      attributes: attributes,
      mintedDate: DateTime.now(),
      habitType: habitType,
      streakAchieved: streakAchieved,
      marketValue: marketValue,
      ownerAddress: _userWalletAddress,
    );
  }

  // NFT 이름 생성
  static String _generateNFTName(String habitName, int streakAchieved, String rarity) {
    final rarityEmojis = {
      'common': '⚪',
      'rare': '🔵',
      'epic': '🟣',
      'legendary': '🟡',
      'mythic': '🔴',
    };

    return '${rarityEmojis[rarity]} $habitName ${streakAchieved}일 마스터';
  }

  // NFT 설명 생성
  static String _generateNFTDescription(String habitName, int streakAchieved, Map<String, dynamic> data) {
    final completionRate = ((data['completionRate'] ?? 0.0) * 100).toInt();
    return '''
🏆 $habitName 습관을 $streakAchieved일 연속으로 달성한 증명서입니다.

📊 성과 요약:
• 연속 달성: $streakAchieved일
• 완료율: $completionRate%
• 달성 날짜: ${DateTime.now().toString().split(' ')[0]}

이 NFT는 당신의 끈기와 의지력을 영원히 기록합니다.
''';
  }

  // NFT 이미지 생성
  static String _generateNFTImage(String habitType, String rarity, int streakAchieved) {
    // 실제로는 AI 기반 이미지 생성 또는 미리 제작된 템플릿 사용
    return 'https://nft.habittracker.ai/images/${habitType}_${rarity}_$streakAchieved.png';
  }

  // NFT 속성 생성
  static Map<String, dynamic> _generateNFTAttributes(
    String habitType, 
    int streakAchieved, 
    String rarity, 
    Map<String, dynamic> data
  ) {
    return {
      'Habit Type': habitType,
      'Streak Days': streakAchieved,
      'Rarity': rarity,
      'Completion Rate': '${((data['completionRate'] ?? 0.0) * 100).toInt()}%',
      'Mint Date': DateTime.now().toString().split(' ')[0],
      'Power Level': _calculatePowerLevel(streakAchieved, rarity),
      'Element': _getHabitElement(habitType),
    };
  }

  // NFT 거래
  static Future<bool> tradeNFT({
    required String tokenId,
    required String toAddress,
    required double price,
  }) async {
    try {
      final nft = _userNFTs.firstWhere((n) => n.tokenId == tokenId);
      
      // 거래 트랜잭션 생성
      final txHash = await _sendTradeTransaction(nft, toAddress, price);
      
      // NFT 소유권 이전
      _userNFTs.removeWhere((n) => n.tokenId == tokenId);
      
      // HABIT 토큰 수령
      _userTokenBalance += price;
      
      // 이벤트 발생
      _nftEventStreamController.add({
        'type': 'nft_traded',
        'tokenId': tokenId,
        'price': price,
        'toAddress': toAddress,
        'txHash': txHash,
        'timestamp': DateTime.now(),
      });

      return true;
    } catch (e) {
      print('NFT 거래 오류: $e');
      return false;
    }
  }

  // NFT 마켓플레이스
  static Future<List<HabitNFT>> getMarketplaceNFTs() async {
    // 실제로는 블록체인에서 판매 중인 NFT 조회
    return [
      // 모의 마켓플레이스 NFT들
    ];
  }

  // 보조 메서드들
  static String _generateWalletAddress() {
    final random = Random();
    final bytes = List<int>.generate(20, (i) => random.nextInt(256));
    return '0x${bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}';
  }

  static String _generateTokenId() {
    return DateTime.now().millisecondsSinceEpoch.toString();
  }

  static Future<String> _sendMintTransaction(HabitNFT nft) async {
    // 실제 블록체인 트랜잭션 전송
    await Future.delayed(const Duration(seconds: 2));
    return _generateTxHash();
  }

  static Future<String> _sendTradeTransaction(HabitNFT nft, String toAddress, double price) async {
    await Future.delayed(const Duration(seconds: 2));
    return _generateTxHash();
  }

  static String _generateTxHash() {
    final random = Random();
    final bytes = List<int>.generate(32, (i) => random.nextInt(256));
    return '0x${bytes.map((b) => b.toRadixString(16).padLeft(2, '0')).join()}';
  }

  static double _calculateTokenReward(String rarity) {
    const rewards = {
      'common': 10.0,
      'rare': 25.0,
      'epic': 50.0,
      'legendary': 100.0,
      'mythic': 250.0,
    };
    return rewards[rarity] ?? 10.0;
  }

  static double _calculateMarketValue(String rarity, int streakAchieved) {
    const baseValues = {
      'common': 0.01,
      'rare': 0.05,
      'epic': 0.1,
      'legendary': 0.5,
      'mythic': 2.0,
    };
    
    final baseValue = baseValues[rarity] ?? 0.01;
    final streakMultiplier = 1.0 + (streakAchieved / 365.0);
    
    return baseValue * streakMultiplier;
  }

  static int _calculatePowerLevel(int streakAchieved, String rarity) {
    const rarityMultipliers = {
      'common': 1,
      'rare': 2,
      'epic': 3,
      'legendary': 5,
      'mythic': 10,
    };
    
    return (streakAchieved * (rarityMultipliers[rarity] ?? 1)) ~/ 7;
  }

  static String _getHabitElement(String habitType) {
    const elements = {
      'fitness': 'Fire',
      'mindfulness': 'Water',
      'productivity': 'Earth',
      'learning': 'Air',
      'health': 'Nature',
    };
    return elements[habitType] ?? 'Neutral';
  }

  // Getter 메서드들
  static List<HabitNFT> get userNFTs => _userNFTs;
  static double get tokenBalance => _userTokenBalance;
  static String get walletAddress => _userWalletAddress;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _nftEventStreamController.close();
    _isInitialized = false;
  }
}

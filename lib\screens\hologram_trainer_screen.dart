import 'package:flutter/material.dart';
import 'dart:async';
import 'dart:math';
import '../services/hologram_trainer_service.dart';

class HologramTrainerScreen extends StatefulWidget {
  const HologramTrainerScreen({super.key});

  @override
  State<HologramTrainerScreen> createState() => _HologramTrainerScreenState();
}

class _HologramTrainerScreenState extends State<HologramTrainerScreen>
    with TickerProviderStateMixin {
  late AnimationController _hologramController;
  late AnimationController _particleController;
  StreamSubscription? _actionSubscription;

  HologramTrainer? _activeTrainer;
  HologramAction? _currentAction;
  bool _isHologramActive = false;

  @override
  void initState() {
    super.initState();
    _hologramController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );

    _particleController = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();

    _initializeHologramSystem();
  }

  void _initializeHologramSystem() {
    _actionSubscription = HologramTrainerService.actionStream.listen((action) {
      setState(() {
        _currentAction = action;
      });
      _playActionAnimation(action);
    });
  }

  void _playActionAnimation(HologramAction action) {
    _hologramController.reset();
    _hologramController.forward();

    // 액션 지속 시간 후 초기화
    Timer(action.duration, () {
      if (mounted) {
        setState(() {
          _currentAction = null;
        });
      }
    });
  }

  @override
  void dispose() {
    _hologramController.dispose();
    _particleController.dispose();
    _actionSubscription?.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title:
            const Text('🎭 홀로그램 트레이너', style: TextStyle(color: Colors.white)),
        backgroundColor: Colors.transparent,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _isHologramActive ? Icons.visibility : Icons.visibility_off,
              color: _isHologramActive ? Colors.cyan : Colors.grey,
            ),
            onPressed: _toggleHologram,
          ),
        ],
      ),
      body: Stack(
        children: [
          // AR 카메라 뷰 시뮬레이션
          _buildARCameraView(),

          // 홀로그램 오버레이
          if (_isHologramActive) _buildHologramOverlay(),

          // 컨트롤 패널
          _buildControlPanel(),

          // 액션 피드백
          if (_currentAction != null) _buildActionFeedback(),
        ],
      ),
    );
  }

  Widget _buildARCameraView() {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: const BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.center,
          colors: [
            Color(0xFF001122),
            Color(0xFF000000),
          ],
        ),
      ),
      child: AnimatedBuilder(
        animation: _particleController,
        builder: (context, child) {
          return CustomPaint(
            size: Size.infinite,
            painter: AREnvironmentPainter(_particleController.value),
          );
        },
      ),
    );
  }

  Widget _buildHologramOverlay() {
    return Center(
      child: AnimatedBuilder(
        animation: _hologramController,
        builder: (context, child) {
          return Transform.scale(
            scale: 0.8 + 0.2 * _hologramController.value,
            child: Opacity(
              opacity: _isHologramActive ? 0.9 : 0.0,
              child: Container(
                width: 300,
                height: 400,
                child: Stack(
                  children: [
                    // 홀로그램 베이스
                    _buildHologramBase(),

                    // 트레이너 아바타
                    if (_activeTrainer != null) _buildTrainerAvatar(),

                    // 홀로그램 효과
                    _buildHologramEffects(),
                  ],
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildHologramBase() {
    return Container(
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: RadialGradient(
          colors: [
            Colors.cyan.withOpacity(0.1),
            Colors.blue.withOpacity(0.3),
            Colors.transparent,
          ],
        ),
      ),
      child: AnimatedBuilder(
        animation: _particleController,
        builder: (context, child) {
          return CustomPaint(
            size: const Size(300, 400),
            painter: HologramBasePainter(_particleController.value),
          );
        },
      ),
    );
  }

  Widget _buildTrainerAvatar() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // 아바타 아이콘
          AnimatedBuilder(
            animation: _hologramController,
            builder: (context, child) {
              return Transform.rotate(
                angle: _hologramController.value * 0.1,
                child: Container(
                  width: 120,
                  height: 120,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    gradient: LinearGradient(
                      colors: [
                        Colors.cyan.withOpacity(0.8),
                        Colors.blue.withOpacity(0.6),
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.cyan.withOpacity(0.5),
                        blurRadius: 20,
                        spreadRadius: 5,
                      ),
                    ],
                  ),
                  child: Icon(
                    _getTrainerIcon(),
                    size: 60,
                    color: Colors.white,
                  ),
                ),
              );
            },
          ),

          const SizedBox(height: 20),

          // 트레이너 이름
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: Colors.cyan.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(color: Colors.cyan.withOpacity(0.5)),
            ),
            child: Text(
              _activeTrainer?.name ?? '트레이너',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),

          const SizedBox(height: 10),

          // 전문 분야
          Text(
            _activeTrainer?.specialization ?? '',
            style: TextStyle(
              color: Colors.cyan.withOpacity(0.8),
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHologramEffects() {
    return AnimatedBuilder(
      animation: _particleController,
      builder: (context, child) {
        return CustomPaint(
          size: const Size(300, 400),
          painter: HologramEffectsPainter(
            _particleController.value,
            _currentAction?.visualEffects ?? {},
          ),
        );
      },
    );
  }

  Widget _buildControlPanel() {
    return Positioned(
      bottom: 50,
      left: 20,
      right: 20,
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Colors.black.withOpacity(0.8),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(color: Colors.cyan.withOpacity(0.3)),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // 트레이너 선택
            _buildTrainerSelector(),

            const SizedBox(height: 16),

            // 액션 버튼들
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  Widget _buildTrainerSelector() {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: HologramTrainerService.availableTrainers.length,
        itemBuilder: (context, index) {
          final trainer = HologramTrainerService.availableTrainers[index];
          final isSelected = _activeTrainer?.id == trainer.id;

          return GestureDetector(
            onTap: () => _selectTrainer(trainer),
            child: Container(
              width: 70,
              margin: const EdgeInsets.only(right: 12),
              decoration: BoxDecoration(
                color: isSelected
                    ? Colors.cyan.withOpacity(0.3)
                    : Colors.grey.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color:
                      isSelected ? Colors.cyan : Colors.grey.withOpacity(0.5),
                ),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _getTrainerIconById(trainer.id),
                    color: isSelected ? Colors.cyan : Colors.white70,
                    size: 24,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    trainer.name.split(' ')[0],
                    style: TextStyle(
                      color: isSelected ? Colors.cyan : Colors.white70,
                      fontSize: 10,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildActionButtons() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
      children: [
        _buildActionButton('인사', Icons.waving_hand, 'greeting'),
        _buildActionButton('격려', Icons.thumb_up, 'encouragement'),
        _buildActionButton('시연', Icons.fitness_center, 'demonstration'),
        _buildActionButton('축하', Icons.celebration, 'celebration'),
      ],
    );
  }

  Widget _buildActionButton(String label, IconData icon, String actionType) {
    return GestureDetector(
      onTap: () => _triggerAction(actionType),
      child: Container(
        width: 60,
        height: 60,
        decoration: BoxDecoration(
          color: Colors.cyan.withOpacity(0.2),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(color: Colors.cyan.withOpacity(0.5)),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, color: Colors.cyan, size: 20),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(color: Colors.cyan, fontSize: 10),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionFeedback() {
    return Positioned(
      top: 100,
      left: 20,
      right: 20,
      child: AnimatedBuilder(
        animation: _hologramController,
        builder: (context, child) {
          return Opacity(
            opacity: 1.0 - _hologramController.value,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.cyan.withOpacity(0.9),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: Colors.cyan.withOpacity(0.5),
                    blurRadius: 10,
                    spreadRadius: 2,
                  ),
                ],
              ),
              child: Text(
                _currentAction?.voiceLine ?? '',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
          );
        },
      ),
    );
  }

  void _toggleHologram() {
    setState(() {
      _isHologramActive = !_isHologramActive;
    });

    if (_isHologramActive && _activeTrainer != null) {
      HologramTrainerService.activateTrainer(_activeTrainer!.id);
    } else {
      HologramTrainerService.deactivateTrainer();
    }
  }

  void _selectTrainer(HologramTrainer trainer) {
    setState(() {
      _activeTrainer = trainer;
    });

    if (_isHologramActive) {
      HologramTrainerService.activateTrainer(trainer.id);
    }
  }

  void _triggerAction(String actionType) {
    if (_isHologramActive && _activeTrainer != null) {
      // 액션 시뮬레이션
      final action = HologramAction(
        actionType: actionType,
        animation: _activeTrainer!.animations[actionType] ?? 'default',
        voiceLine: _getActionVoiceLine(actionType),
        visualEffects: {'glow_effect': true},
        duration: const Duration(seconds: 2),
      );
      _playActionAnimation(action);
    }
  }

  String _getActionVoiceLine(String actionType) {
    switch (actionType) {
      case 'greeting':
        return '안녕하세요! 오늘도 멋진 하루 되세요! 👋';
      case 'encouragement':
        return '정말 잘하고 있어요! 계속 힘내세요! 💪';
      case 'demonstration':
        return '이렇게 해보세요! 저를 따라해보세요! 🎯';
      case 'celebration':
        return '와! 정말 대단해요! 축하합니다! 🎉';
      default:
        return '함께 열심히 해봐요! ✨';
    }
  }

  IconData _getTrainerIcon() {
    switch (_activeTrainer?.id) {
      case 'trainer_alex':
        return Icons.fitness_center;
      case 'trainer_zen':
        return Icons.self_improvement;
      case 'trainer_nova':
        return Icons.computer;
      default:
        return Icons.person;
    }
  }

  IconData _getTrainerIconById(String id) {
    switch (id) {
      case 'trainer_alex':
        return Icons.fitness_center;
      case 'trainer_zen':
        return Icons.self_improvement;
      case 'trainer_nova':
        return Icons.computer;
      default:
        return Icons.person;
    }
  }
}

class AREnvironmentPainter extends CustomPainter {
  final double animationValue;

  AREnvironmentPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withOpacity(0.1)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    // AR 그리드 그리기
    for (int i = 0; i < 10; i++) {
      final y = (size.height / 10) * i;
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }

    for (int i = 0; i < 10; i++) {
      final x = (size.width / 10) * i;
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    // 떠다니는 파티클
    final particlePaint = Paint()
      ..color = Colors.cyan.withOpacity(0.6)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 20; i++) {
      final x = (size.width * (i / 20)) + sin(animationValue * 2 * pi + i) * 20;
      final y = (size.height * 0.5) + cos(animationValue * 2 * pi + i) * 50;

      canvas.drawCircle(
        Offset(x, y),
        2 + sin(animationValue * 4 * pi + i) * 1,
        particlePaint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class HologramBasePainter extends CustomPainter {
  final double animationValue;

  HologramBasePainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2;

    final centerX = size.width / 2;
    final centerY = size.height / 2;

    // 홀로그램 베이스 링
    for (int i = 0; i < 3; i++) {
      paint.color = Colors.cyan.withOpacity(0.8 - i * 0.2);
      final radius = 50.0 + i * 20 + sin(animationValue * 2 * pi) * 5;

      canvas.drawCircle(
        Offset(centerX, centerY + 100),
        radius,
        paint,
      );
    }

    // 수직 홀로그램 라인들
    paint.color = Colors.cyan.withOpacity(0.6);
    for (int i = 0; i < 8; i++) {
      final angle = (i / 8) * 2 * pi + animationValue * 2 * pi;
      final startX = centerX + cos(angle) * 60;
      final startY = centerY + 100 + sin(angle) * 10;
      final endX = centerX + cos(angle) * 40;
      final endY = centerY - 100;

      canvas.drawLine(
        Offset(startX, startY),
        Offset(endX, endY),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class HologramEffectsPainter extends CustomPainter {
  final double animationValue;
  final Map<String, dynamic> effects;

  HologramEffectsPainter(this.animationValue, this.effects);

  @override
  void paint(Canvas canvas, Size size) {
    if (effects['hologram_materialize'] == true) {
      _drawMaterializeEffect(canvas, size);
    }

    if (effects['particle_effects'] == true) {
      _drawParticleEffects(canvas, size);
    }

    if (effects['glow_effect'] == true) {
      _drawGlowEffect(canvas, size);
    }
  }

  void _drawMaterializeEffect(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withOpacity(0.8)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1;

    for (int i = 0; i < 20; i++) {
      final y = (size.height / 20) * i;
      final opacity = sin(animationValue * 4 * pi + i * 0.5).abs();
      paint.color = Colors.cyan.withOpacity(opacity * 0.8);

      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  void _drawParticleEffects(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.white.withOpacity(0.8)
      ..style = PaintingStyle.fill;

    for (int i = 0; i < 30; i++) {
      final x = Random().nextDouble() * size.width;
      final y = Random().nextDouble() * size.height;
      final radius = 1 + Random().nextDouble() * 2;

      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  void _drawGlowEffect(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Colors.cyan.withOpacity(0.3)
      ..style = PaintingStyle.fill
      ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 10);

    canvas.drawCircle(
      Offset(size.width / 2, size.height / 2),
      100 + sin(animationValue * 2 * pi) * 20,
      paint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:habit_tracker/main.dart';

void main() {
  group('HabitTrackerApp Widget Tests', () {
    testWidgets('should display app title', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const HabitTrackerApp());

      // Wait for the initial frame
      await tester.pump();

      // Verify that our app displays the correct title
      expect(find.text('습관 트래커'), findsOneWidget);

      // Verify that the app bar is present
      expect(find.byType(AppBar), findsOneWidget);
    });

    testWidgets('should show add habit dialog when FAB is tapped', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const HabitTrackerApp());

      // Wait for the initial frame
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Tap the '+' icon and trigger a frame.
      await tester.tap(find.byIcon(Icons.add));
      await tester.pump();

      // Verify that the add habit dialog appears
      expect(find.text('새로운 습관 추가'), findsOneWidget);
      expect(find.text('습관 이름'), findsOneWidget);
    });

    testWidgets('should show refresh button in app bar', (WidgetTester tester) async {
      // Build our app and trigger a frame.
      await tester.pumpWidget(const HabitTrackerApp());

      // Wait for the initial frame
      await tester.pump();
      await tester.pump(const Duration(seconds: 1));

      // Verify that refresh button is present
      expect(find.byIcon(Icons.refresh), findsOneWidget);
    });
  });
}
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/app_theme.dart';
import '../services/theme_service.dart';
import '../services/user_stats_service.dart';
import '../services/habit_service.dart';

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  bool _notificationsEnabled = true;
  bool _soundEnabled = true;
  bool _vibrationEnabled = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '설정',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
      ),
      body: ListView(
        padding: const EdgeInsets.all(16),
        children: [
          _buildThemeSection(),
          const SizedBox(height: 24),
          _buildNotificationSection(),
          const SizedBox(height: 24),
          _buildDataSection(),
          const SizedBox(height: 24),
          _buildAboutSection(),
        ],
      ),
    );
  }

  Widget _buildThemeSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '테마 설정',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Consumer<ThemeService>(
              builder: (context, themeService, child) {
                return Column(
                  children: [
                    SwitchListTile(
                      title: const Text('다크 모드'),
                      subtitle: const Text('어두운 테마 사용'),
                      value: themeService.isDarkMode,
                      onChanged: (value) {
                        themeService.toggleDarkMode();
                      },
                      secondary: Icon(
                        themeService.isDarkMode 
                            ? Icons.dark_mode 
                            : Icons.light_mode,
                      ),
                    ),
                    const Divider(),
                    ListTile(
                      title: const Text('테마 색상'),
                      subtitle: Text('현재: ${themeService.currentTheme.name}'),
                      trailing: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: themeService.currentTheme.primaryColor,
                          shape: BoxShape.circle,
                        ),
                      ),
                      onTap: () => _showThemeSelector(context, themeService),
                    ),
                  ],
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotificationSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '알림 설정',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            SwitchListTile(
              title: const Text('알림 허용'),
              subtitle: const Text('습관 리마인더 알림'),
              value: _notificationsEnabled,
              onChanged: (value) {
                setState(() {
                  _notificationsEnabled = value;
                });
              },
              secondary: const Icon(Icons.notifications),
            ),
            SwitchListTile(
              title: const Text('소리'),
              subtitle: const Text('알림 소리 재생'),
              value: _soundEnabled,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _soundEnabled = value;
                });
              } : null,
              secondary: const Icon(Icons.volume_up),
            ),
            SwitchListTile(
              title: const Text('진동'),
              subtitle: const Text('알림 시 진동'),
              value: _vibrationEnabled,
              onChanged: _notificationsEnabled ? (value) {
                setState(() {
                  _vibrationEnabled = value;
                });
              } : null,
              secondary: const Icon(Icons.vibration),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDataSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '데이터 관리',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('데이터 내보내기'),
              subtitle: const Text('습관 데이터를 파일로 저장'),
              leading: const Icon(Icons.file_download),
              onTap: _exportData,
            ),
            ListTile(
              title: const Text('데이터 공유'),
              subtitle: const Text('진행 상황을 친구와 공유'),
              leading: const Icon(Icons.share),
              onTap: _shareProgress,
            ),
            const Divider(),
            ListTile(
              title: const Text(
                '모든 데이터 초기화',
                style: TextStyle(color: Colors.red),
              ),
              subtitle: const Text('모든 습관과 통계가 삭제됩니다'),
              leading: const Icon(Icons.delete_forever, color: Colors.red),
              onTap: _showResetDialog,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAboutSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '앱 정보',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            ListTile(
              title: const Text('버전'),
              subtitle: const Text('1.0.0'),
              leading: const Icon(Icons.info),
            ),
            ListTile(
              title: const Text('개발자'),
              subtitle: const Text('Habit Tracker Team'),
              leading: const Icon(Icons.person),
            ),
            ListTile(
              title: const Text('피드백 보내기'),
              subtitle: const Text('의견이나 버그 신고'),
              leading: const Icon(Icons.feedback),
              onTap: _sendFeedback,
            ),
            ListTile(
              title: const Text('평가하기'),
              subtitle: const Text('앱스토어에서 평가해주세요'),
              leading: const Icon(Icons.star),
              onTap: _rateApp,
            ),
          ],
        ),
      ),
    );
  }

  void _showThemeSelector(BuildContext context, ThemeService themeService) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('테마 선택'),
        content: SizedBox(
          width: double.maxFinite,
          child: ListView.builder(
            shrinkWrap: true,
            itemCount: AppThemes.allThemes.length,
            itemBuilder: (context, index) {
              final theme = AppThemes.allThemes[index];
              final isSelected = themeService.currentTheme.type == theme.type;
              
              return ListTile(
                title: Text(theme.name),
                leading: Container(
                  width: 24,
                  height: 24,
                  decoration: BoxDecoration(
                    color: theme.primaryColor,
                    shape: BoxShape.circle,
                    border: isSelected 
                        ? Border.all(color: Colors.black, width: 2)
                        : null,
                  ),
                ),
                trailing: isSelected ? const Icon(Icons.check) : null,
                onTap: () {
                  themeService.setTheme(theme);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('닫기'),
          ),
        ],
      ),
    );
  }

  void _exportData() async {
    try {
      // 데이터 내보내기 로직 구현
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('데이터 내보내기 기능은 곧 추가될 예정입니다.'),
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('데이터 내보내기 중 오류가 발생했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _shareProgress() async {
    try {
      final stats = await UserStatsService.loadUserStats();
      final message = '''
🎯 내 습관 추적 현황

📊 레벨: ${stats.level}
⭐ 경험치: ${stats.experience} XP
🔥 현재 스트릭: ${stats.currentStreak}일
🏆 최장 스트릭: ${stats.longestStreak}일
✅ 총 완료: ${stats.totalHabitsCompleted}개
🎖️ 업적: ${stats.achievements.length}개

습관 추적기 앱으로 함께 성장해요! 💪
      ''';
      
      await Share.share(message);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('공유 중 오류가 발생했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('데이터 초기화'),
        content: const Text(
          '정말로 모든 데이터를 삭제하시겠습니까?\n\n'
          '이 작업은 되돌릴 수 없으며, 모든 습관과 통계가 영구적으로 삭제됩니다.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('취소'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.of(context).pop();
              await _resetAllData();
            },
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('삭제'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetAllData() async {
    try {
      // 모든 데이터 삭제
      await UserStatsService.resetUserStats();
      // 습관 데이터도 삭제하는 메서드가 필요하면 추가
      
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('모든 데이터가 초기화되었습니다.'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('데이터 초기화 중 오류가 발생했습니다.'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  void _sendFeedback() async {
    const email = '<EMAIL>';
    const subject = '습관 추적기 앱 피드백';
    final uri = Uri.parse('mailto:$email?subject=$subject');
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('이메일 앱을 열 수 없습니다.'),
        ),
      );
    }
  }

  void _rateApp() async {
    // 실제 앱스토어 URL로 변경 필요
    const url = 'https://play.google.com/store/apps/details?id=com.habittracker.habit_tracker';
    final uri = Uri.parse(url);
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('앱스토어를 열 수 없습니다.'),
        ),
      );
    }
  }
}

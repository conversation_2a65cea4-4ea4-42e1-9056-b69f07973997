import 'package:flutter/material.dart';

enum AppThemeType {
  light,
  dark,
  green,
  blue,
  purple,
  orange,
  pink,
  teal,
}

class AppTheme {
  final String name;
  final AppThemeType type;
  final Color primaryColor;
  final Color secondaryColor;
  final Color backgroundColor;
  final Color surfaceColor;
  final Color cardColor;
  final Brightness brightness;
  final LinearGradient gradient;

  const AppTheme({
    required this.name,
    required this.type,
    required this.primaryColor,
    required this.secondaryColor,
    required this.backgroundColor,
    required this.surfaceColor,
    required this.cardColor,
    required this.brightness,
    required this.gradient,
  });

  ThemeData get themeData {
    return ThemeData(
      useMaterial3: true,
      brightness: brightness,
      colorScheme: ColorScheme.fromSeed(
        seedColor: primaryColor,
        brightness: brightness,
        primary: primaryColor,
        secondary: secondaryColor,
        surface: surfaceColor,
        background: backgroundColor,
      ),
      cardTheme: CardTheme(
        elevation: 2,
        color: cardColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(12),
        ),
      ),
      appBarTheme: AppBarTheme(
        centerTitle: true,
        backgroundColor: brightness == Brightness.dark 
            ? surfaceColor 
            : primaryColor.withOpacity(0.1),
        foregroundColor: brightness == Brightness.dark 
            ? Colors.white 
            : primaryColor,
      ),
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: primaryColor,
        foregroundColor: brightness == Brightness.dark 
            ? Colors.black 
            : Colors.white,
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: primaryColor,
          foregroundColor: brightness == Brightness.dark 
              ? Colors.black 
              : Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
    );
  }
}

class AppThemes {
  static const AppTheme lightTheme = AppTheme(
    name: '라이트',
    type: AppThemeType.light,
    primaryColor: Colors.green,
    secondaryColor: Colors.greenAccent,
    backgroundColor: Colors.white,
    surfaceColor: Color(0xFFF5F5F5),
    cardColor: Colors.white,
    brightness: Brightness.light,
    gradient: LinearGradient(
      colors: [Color(0xFF4CAF50), Color(0xFF8BC34A)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const AppTheme darkTheme = AppTheme(
    name: '다크',
    type: AppThemeType.dark,
    primaryColor: Color(0xFF66BB6A),
    secondaryColor: Color(0xFF81C784),
    backgroundColor: Color(0xFF121212),
    surfaceColor: Color(0xFF1E1E1E),
    cardColor: Color(0xFF2D2D2D),
    brightness: Brightness.dark,
    gradient: LinearGradient(
      colors: [Color(0xFF66BB6A), Color(0xFF4CAF50)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const AppTheme blueTheme = AppTheme(
    name: '블루',
    type: AppThemeType.blue,
    primaryColor: Colors.blue,
    secondaryColor: Colors.blueAccent,
    backgroundColor: Colors.white,
    surfaceColor: Color(0xFFF3F7FF),
    cardColor: Colors.white,
    brightness: Brightness.light,
    gradient: LinearGradient(
      colors: [Color(0xFF2196F3), Color(0xFF03A9F4)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const AppTheme purpleTheme = AppTheme(
    name: '퍼플',
    type: AppThemeType.purple,
    primaryColor: Colors.purple,
    secondaryColor: Colors.purpleAccent,
    backgroundColor: Colors.white,
    surfaceColor: Color(0xFFF8F5FF),
    cardColor: Colors.white,
    brightness: Brightness.light,
    gradient: LinearGradient(
      colors: [Color(0xFF9C27B0), Color(0xFFE91E63)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const AppTheme orangeTheme = AppTheme(
    name: '오렌지',
    type: AppThemeType.orange,
    primaryColor: Colors.orange,
    secondaryColor: Colors.orangeAccent,
    backgroundColor: Colors.white,
    surfaceColor: Color(0xFFFFF8F0),
    cardColor: Colors.white,
    brightness: Brightness.light,
    gradient: LinearGradient(
      colors: [Color(0xFFFF9800), Color(0xFFFF5722)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const AppTheme pinkTheme = AppTheme(
    name: '핑크',
    type: AppThemeType.pink,
    primaryColor: Colors.pink,
    secondaryColor: Colors.pinkAccent,
    backgroundColor: Colors.white,
    surfaceColor: Color(0xFFFFF0F5),
    cardColor: Colors.white,
    brightness: Brightness.light,
    gradient: LinearGradient(
      colors: [Color(0xFFE91E63), Color(0xFFF06292)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const AppTheme tealTheme = AppTheme(
    name: '틸',
    type: AppThemeType.teal,
    primaryColor: Colors.teal,
    secondaryColor: Colors.tealAccent,
    backgroundColor: Colors.white,
    surfaceColor: Color(0xFFF0FDFA),
    cardColor: Colors.white,
    brightness: Brightness.light,
    gradient: LinearGradient(
      colors: [Color(0xFF009688), Color(0xFF26A69A)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    ),
  );

  static const List<AppTheme> allThemes = [
    lightTheme,
    darkTheme,
    blueTheme,
    purpleTheme,
    orangeTheme,
    pinkTheme,
    tealTheme,
  ];

  static AppTheme getThemeByType(AppThemeType type) {
    return allThemes.firstWhere(
      (theme) => theme.type == type,
      orElse: () => lightTheme,
    );
  }
}

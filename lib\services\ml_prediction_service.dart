import 'dart:math';
import '../models/habit.dart';
import '../models/user_stats.dart';
import '../utils/date_utils.dart' as date_utils;
import 'habit_service.dart';
import 'user_stats_service.dart';

class HabitPrediction {
  final String habitId;
  final String habitName;
  final double failureRisk; // 0.0 ~ 1.0
  final String riskLevel; // 'low', 'medium', 'high'
  final List<String> riskFactors;
  final List<String> recommendations;
  final DateTime predictedDate;

  HabitPrediction({
    required this.habitId,
    required this.habitName,
    required this.failureRisk,
    required this.riskLevel,
    required this.riskFactors,
    required this.recommendations,
    required this.predictedDate,
  });
}

class MLPredictionService {
  // 머신러닝 기반 습관 실패 예측
  static Future<List<HabitPrediction>> predictHabitFailures() async {
    try {
      final habits = await HabitService.loadHabits();
      final userStats = await UserStatsService.loadUserStats();
      
      final predictions = <HabitPrediction>[];
      
      for (final habit in habits) {
        final prediction = await _predictHabitFailure(habit, userStats, habits);
        if (prediction.failureRisk > 0.3) { // 30% 이상 위험도만 포함
          predictions.add(prediction);
        }
      }
      
      // 위험도 순으로 정렬
      predictions.sort((a, b) => b.failureRisk.compareTo(a.failureRisk));
      return predictions;
    } catch (e) {
      print('ML 예측 오류: $e');
      return [];
    }
  }

  // 개별 습관 실패 예측
  static Future<HabitPrediction> _predictHabitFailure(
    Habit habit,
    UserStats userStats,
    List<Habit> allHabits,
  ) async {
    double riskScore = 0.0;
    final riskFactors = <String>[];
    final recommendations = <String>[];

    // 1. 최근 완료율 분석 (가중치: 30%)
    final recentCompletionRate = _calculateRecentCompletionRate(habit, 7);
    if (recentCompletionRate < 0.5) {
      riskScore += 0.3;
      riskFactors.add('최근 7일 완료율이 낮음 (${(recentCompletionRate * 100).toInt()}%)');
      recommendations.add('작은 목표부터 시작해보세요');
    }

    // 2. 연속 실패 일수 분석 (가중치: 25%)
    final consecutiveFailures = _getConsecutiveFailures(habit);
    if (consecutiveFailures >= 3) {
      riskScore += 0.25;
      riskFactors.add('$consecutiveFailures일 연속 실패');
      recommendations.add('습관을 더 쉽게 만들어보세요');
    }

    // 3. 요일별 패턴 분석 (가중치: 15%)
    final weekdayRisk = _analyzeWeekdayPattern(habit);
    if (weekdayRisk > 0.6) {
      riskScore += 0.15;
      riskFactors.add('특정 요일에 실패 패턴 발견');
      recommendations.add('어려운 요일에 특별한 전략을 세워보세요');
    }

    // 4. 전체 습관 부담 분석 (가중치: 10%)
    if (allHabits.length > 5) {
      riskScore += 0.1;
      riskFactors.add('너무 많은 습관 (${allHabits.length}개)');
      recommendations.add('습관 개수를 줄이는 것을 고려해보세요');
    }

    // 5. 사용자 전체 스트릭 상태 (가중치: 10%)
    if (userStats.currentStreak == 0) {
      riskScore += 0.1;
      riskFactors.add('현재 스트릭이 끊어진 상태');
      recommendations.add('다시 시작하는 마음으로 천천히 진행하세요');
    }

    // 6. 시간대별 패턴 분석 (가중치: 10%)
    final timeRisk = _analyzeTimePattern(habit);
    if (timeRisk > 0.5) {
      riskScore += 0.1;
      riskFactors.add('특정 시간대에 실패 패턴');
      recommendations.add('습관 실행 시간을 조정해보세요');
    }

    // 위험도 레벨 결정
    String riskLevel;
    if (riskScore < 0.3) {
      riskLevel = 'low';
    } else if (riskScore < 0.7) {
      riskLevel = 'medium';
    } else {
      riskLevel = 'high';
    }

    // 기본 추천사항 추가
    if (recommendations.isEmpty) {
      recommendations.add('현재 잘 진행하고 있습니다. 계속 유지하세요!');
    }

    return HabitPrediction(
      habitId: habit.id,
      habitName: habit.name,
      failureRisk: riskScore.clamp(0.0, 1.0),
      riskLevel: riskLevel,
      riskFactors: riskFactors,
      recommendations: recommendations,
      predictedDate: DateTime.now().add(const Duration(days: 1)),
    );
  }

  // 최근 완료율 계산
  static double _calculateRecentCompletionRate(Habit habit, int days) {
    final now = DateTime.now();
    int completedDays = 0;
    int totalDays = 0;

    for (int i = 0; i < days; i++) {
      final date = now.subtract(Duration(days: i));
      if (date.isBefore(habit.createdDate)) break;
      
      totalDays++;
      if (habit.isCompletedOn(date)) {
        completedDays++;
      }
    }

    return totalDays > 0 ? completedDays / totalDays : 0.0;
  }

  // 연속 실패 일수 계산
  static int _getConsecutiveFailures(Habit habit) {
    final now = DateTime.now();
    int failures = 0;

    for (int i = 0; i < 30; i++) { // 최대 30일까지 확인
      final date = now.subtract(Duration(days: i));
      if (date.isBefore(habit.createdDate)) break;
      
      if (!habit.isCompletedOn(date)) {
        failures++;
      } else {
        break; // 완료된 날을 만나면 중단
      }
    }

    return failures;
  }

  // 요일별 패턴 분석
  static double _analyzeWeekdayPattern(Habit habit) {
    final weekdayFailures = <int, int>{};
    final weekdayTotals = <int, int>{};
    final now = DateTime.now();

    // 지난 4주 데이터 분석
    for (int i = 0; i < 28; i++) {
      final date = now.subtract(Duration(days: i));
      if (date.isBefore(habit.createdDate)) break;
      
      final weekday = date.weekday;
      weekdayTotals[weekday] = (weekdayTotals[weekday] ?? 0) + 1;
      
      if (!habit.isCompletedOn(date)) {
        weekdayFailures[weekday] = (weekdayFailures[weekday] ?? 0) + 1;
      }
    }

    // 가장 실패율이 높은 요일의 실패율 반환
    double maxFailureRate = 0.0;
    for (final weekday in weekdayFailures.keys) {
      final total = weekdayTotals[weekday] ?? 1;
      final failures = weekdayFailures[weekday] ?? 0;
      final failureRate = failures / total;
      if (failureRate > maxFailureRate) {
        maxFailureRate = failureRate;
      }
    }

    return maxFailureRate;
  }

  // 시간대별 패턴 분석 (단순화된 버전)
  static double _analyzeTimePattern(Habit habit) {
    // 실제로는 사용자의 활동 시간대 데이터가 필요하지만,
    // 여기서는 단순화된 분석을 수행
    final now = DateTime.now();
    final hour = now.hour;
    
    // 일반적으로 어려운 시간대 (늦은 밤, 이른 아침)
    if (hour < 6 || hour > 22) {
      return 0.7; // 높은 위험도
    } else if (hour < 8 || hour > 20) {
      return 0.4; // 중간 위험도
    } else {
      return 0.1; // 낮은 위험도
    }
  }

  // 개인화된 동기부여 메시지 생성
  static Future<List<String>> generateMotivationalMessages(
    List<HabitPrediction> predictions,
  ) async {
    final messages = <String>[];
    
    if (predictions.isEmpty) {
      messages.add('모든 습관이 순조롭게 진행되고 있어요! 👏');
      messages.add('이 조자로 계속 유지해보세요! 💪');
      return messages;
    }

    for (final prediction in predictions.take(3)) {
      switch (prediction.riskLevel) {
        case 'high':
          messages.add(
            '⚠️ ${prediction.habitName} 습관이 위험해요. '
            '${prediction.recommendations.first}'
          );
          break;
        case 'medium':
          messages.add(
            '⚡ ${prediction.habitName} 습관에 주의가 필요해요. '
            '${prediction.recommendations.first}'
          );
          break;
        case 'low':
          messages.add(
            '✨ ${prediction.habitName} 습관을 잘 유지하고 있어요!'
          );
          break;
      }
    }

    return messages;
  }

  // 최적의 습관 실행 시간 추천
  static Future<Map<String, String>> recommendOptimalTimes(
    List<Habit> habits,
  ) async {
    final recommendations = <String, String>{};
    
    for (final habit in habits) {
      final completionRate = _calculateRecentCompletionRate(habit, 14);
      
      if (completionRate < 0.6) {
        // 완료율이 낮은 습관에 대해 시간 추천
        final categoryId = habit.categoryId;
        String recommendedTime;
        
        switch (categoryId) {
          case 'fitness':
            recommendedTime = '아침 7-9시 (에너지가 높은 시간)';
            break;
          case 'learning':
            recommendedTime = '오전 10-12시 (집중력이 높은 시간)';
            break;
          case 'mindfulness':
            recommendedTime = '저녁 8-10시 (하루를 마무리하며)';
            break;
          case 'health':
            recommendedTime = '식사 전후 (루틴과 연결)';
            break;
          default:
            recommendedTime = '기존 루틴과 연결된 시간';
        }
        
        recommendations[habit.id] = recommendedTime;
      }
    }
    
    return recommendations;
  }

  // 습관 난이도 조정 제안
  static Future<Map<String, String>> suggestDifficultyAdjustments(
    List<HabitPrediction> predictions,
  ) async {
    final suggestions = <String, String>{};
    
    for (final prediction in predictions) {
      if (prediction.failureRisk > 0.6) {
        // 높은 위험도의 습관에 대해 난이도 조정 제안
        final habitName = prediction.habitName;
        String suggestion;
        
        if (habitName.contains('30분') || habitName.contains('1시간')) {
          suggestion = '시간을 10-15분으로 줄여보세요';
        } else if (habitName.contains('매일')) {
          suggestion = '주 3-4회로 시작해보세요';
        } else if (habitName.contains('10개') || habitName.contains('20개')) {
          suggestion = '개수를 절반으로 줄여보세요';
        } else {
          suggestion = '더 작고 쉬운 단계로 나누어보세요';
        }
        
        suggestions[prediction.habitId] = suggestion;
      }
    }
    
    return suggestions;
  }
}

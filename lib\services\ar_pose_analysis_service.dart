import 'dart:async';
import 'dart:math';
import 'dart:typed_data';
import 'dart:ui';
import 'package:camera/camera.dart';
import 'package:google_mlkit_pose_detection/google_mlkit_pose_detection.dart';

// AR 운동 자세 분석 결과
class PoseAnalysisResult {
  final String exerciseType;
  final int repetitionCount;
  final double accuracy;
  final List<String> corrections;
  final bool isCorrectForm;
  final Map<String, double> jointAngles;
  final double confidence;

  PoseAnalysisResult({
    required this.exerciseType,
    required this.repetitionCount,
    required this.accuracy,
    required this.corrections,
    required this.isCorrectForm,
    required this.jointAngles,
    required this.confidence,
  });
}

// 실시간 AR 운동 자세 분석 서비스
class ARPoseAnalysisService {
  static final PoseDetector _poseDetector = PoseDetector(
    options: PoseDetectorOptions(
      mode: PoseDetectionMode.stream,
      model: PoseDetectionModel.accurate,
    ),
  );

  static bool _isInitialized = false;
  static String _currentExercise = '';
  static int _repetitionCount = 0;
  static List<Pose> _poseHistory = [];
  static Timer? _analysisTimer;

  // 운동별 기준 각도 및 임계값
  static const Map<String, Map<String, dynamic>> _exerciseStandards = {
    'pushup': {
      'minElbowAngle': 70.0,
      'maxElbowAngle': 160.0,
      'shoulderAlignment': 15.0,
      'bodyLineDeviation': 20.0,
    },
    'squat': {
      'minKneeAngle': 80.0,
      'maxKneeAngle': 170.0,
      'backStraightness': 15.0,
      'kneeAlignment': 10.0,
    },
    'plank': {
      'bodyLineDeviation': 10.0,
      'shoulderAlignment': 5.0,
      'hipAlignment': 10.0,
    },
  };

  // 초기화
  static Future<bool> initialize() async {
    try {
      // AR 자세 분석 시스템 초기화 (모의)
      await Future.delayed(const Duration(milliseconds: 500));

      _isInitialized = true;
      return true;
    } catch (e) {
      print('AR 자세 분석 서비스 초기화 오류: $e');
      return false;
    }
  }

  // 운동 시작
  static void startExercise(String exerciseType) {
    _currentExercise = exerciseType.toLowerCase();
    _repetitionCount = 0;
    _poseHistory.clear();

    // 실시간 분석 타이머 시작
    _analysisTimer = Timer.periodic(const Duration(milliseconds: 100), (timer) {
      _analyzeMovementPattern();
    });
  }

  // 운동 종료
  static void stopExercise() {
    _analysisTimer?.cancel();
    _currentExercise = '';
    _poseHistory.clear();
  }

  // 실시간 자세 분석
  static Future<PoseAnalysisResult?> analyzePose(CameraImage image) async {
    if (!_isInitialized || _currentExercise.isEmpty) return null;

    try {
      // CameraImage를 InputImage로 변환
      final inputImage = _convertCameraImage(image);

      // 자세 감지
      final poses = await _poseDetector.processImage(inputImage);

      if (poses.isNotEmpty) {
        final pose = poses.first;
        _poseHistory.add(pose);

        // 자세 분석 수행
        return await _performDetailedAnalysis(pose);
      }

      return null;
    } catch (e) {
      print('자세 분석 오류: $e');
      return null;
    }
  }

  // 상세 자세 분석
  static Future<PoseAnalysisResult> _performDetailedAnalysis(Pose pose) async {
    final corrections = <String>[];
    final jointAngles = <String, double>{};
    double accuracy = 0.0;
    bool isCorrectForm = true;

    switch (_currentExercise) {
      case 'pushup':
        return _analyzePushup(pose);
      case 'squat':
        return _analyzeSquat(pose);
      case 'plank':
        return _analyzePlank(pose);
      default:
        return _getDefaultAnalysis();
    }
  }

  // 팔굽혀펴기 분석
  static PoseAnalysisResult _analyzePushup(Pose pose) {
    final corrections = <String>[];
    final jointAngles = <String, double>{};

    // 팔꿈치 각도 계산
    final leftElbowAngle = _calculateElbowAngle(pose, true);
    final rightElbowAngle = _calculateElbowAngle(pose, false);
    jointAngles['leftElbow'] = leftElbowAngle;
    jointAngles['rightElbow'] = rightElbowAngle;

    // 어깨 정렬 확인
    final shoulderAlignment = _checkShoulderAlignment(pose);
    jointAngles['shoulderAlignment'] = shoulderAlignment;

    // 몸의 일직선 확인
    final bodyLineDeviation = _checkBodyLine(pose);
    jointAngles['bodyLine'] = bodyLineDeviation;

    // 정확도 계산
    double accuracy = 100.0;
    bool isCorrectForm = true;

    // 팔꿈치 각도 검사
    final standards = _exerciseStandards['pushup']!;
    if (leftElbowAngle < standards['minElbowAngle'] ||
        leftElbowAngle > standards['maxElbowAngle']) {
      corrections.add('왼쪽 팔꿈치 각도를 조정하세요 (현재: ${leftElbowAngle.toInt()}°)');
      accuracy -= 20;
      isCorrectForm = false;
    }

    if (rightElbowAngle < standards['minElbowAngle'] ||
        rightElbowAngle > standards['maxElbowAngle']) {
      corrections.add('오른쪽 팔꿈치 각도를 조정하세요 (현재: ${rightElbowAngle.toInt()}°)');
      accuracy -= 20;
      isCorrectForm = false;
    }

    // 어깨 정렬 검사
    if (shoulderAlignment > standards['shoulderAlignment']) {
      corrections.add('어깨를 수평으로 맞춰주세요');
      accuracy -= 15;
      isCorrectForm = false;
    }

    // 몸의 일직선 검사
    if (bodyLineDeviation > standards['bodyLineDeviation']) {
      corrections.add('몸을 일직선으로 유지하세요');
      accuracy -= 25;
      isCorrectForm = false;
    }

    // 반복 횟수 카운팅
    _countRepetition(leftElbowAngle, 'pushup');

    return PoseAnalysisResult(
      exerciseType: 'pushup',
      repetitionCount: _repetitionCount,
      accuracy: accuracy.clamp(0, 100),
      corrections: corrections,
      isCorrectForm: isCorrectForm,
      jointAngles: jointAngles,
      confidence: 0.95,
    );
  }

  // 스쿼트 분석
  static PoseAnalysisResult _analyzeSquat(Pose pose) {
    final corrections = <String>[];
    final jointAngles = <String, double>{};

    // 무릎 각도 계산
    final leftKneeAngle = _calculateKneeAngle(pose, true);
    final rightKneeAngle = _calculateKneeAngle(pose, false);
    jointAngles['leftKnee'] = leftKneeAngle;
    jointAngles['rightKnee'] = rightKneeAngle;

    // 등의 직선성 확인
    final backStraightness = _checkBackStraightness(pose);
    jointAngles['backStraightness'] = backStraightness;

    // 무릎 정렬 확인
    final kneeAlignment = _checkKneeAlignment(pose);
    jointAngles['kneeAlignment'] = kneeAlignment;

    double accuracy = 100.0;
    bool isCorrectForm = true;

    final standards = _exerciseStandards['squat']!;

    // 무릎 각도 검사
    if (leftKneeAngle < standards['minKneeAngle'] ||
        leftKneeAngle > standards['maxKneeAngle']) {
      corrections.add('왼쪽 무릎을 더 구부리거나 펴세요 (현재: ${leftKneeAngle.toInt()}°)');
      accuracy -= 25;
      isCorrectForm = false;
    }

    if (rightKneeAngle < standards['minKneeAngle'] ||
        rightKneeAngle > standards['maxKneeAngle']) {
      corrections.add('오른쪽 무릎을 더 구부리거나 펴세요 (현재: ${rightKneeAngle.toInt()}°)');
      accuracy -= 25;
      isCorrectForm = false;
    }

    // 등의 직선성 검사
    if (backStraightness > standards['backStraightness']) {
      corrections.add('등을 곧게 펴세요');
      accuracy -= 20;
      isCorrectForm = false;
    }

    // 무릎 정렬 검사
    if (kneeAlignment > standards['kneeAlignment']) {
      corrections.add('무릎이 발끝 방향을 향하도록 하세요');
      accuracy -= 15;
      isCorrectForm = false;
    }

    // 반복 횟수 카운팅
    _countRepetition(leftKneeAngle, 'squat');

    return PoseAnalysisResult(
      exerciseType: 'squat',
      repetitionCount: _repetitionCount,
      accuracy: accuracy.clamp(0, 100),
      corrections: corrections,
      isCorrectForm: isCorrectForm,
      jointAngles: jointAngles,
      confidence: 0.92,
    );
  }

  // 플랭크 분석
  static PoseAnalysisResult _analyzePlank(Pose pose) {
    final corrections = <String>[];
    final jointAngles = <String, double>{};

    // 몸의 일직선 확인
    final bodyLineDeviation = _checkBodyLine(pose);
    jointAngles['bodyLine'] = bodyLineDeviation;

    // 어깨 정렬 확인
    final shoulderAlignment = _checkShoulderAlignment(pose);
    jointAngles['shoulderAlignment'] = shoulderAlignment;

    // 엉덩이 정렬 확인
    final hipAlignment = _checkHipAlignment(pose);
    jointAngles['hipAlignment'] = hipAlignment;

    double accuracy = 100.0;
    bool isCorrectForm = true;

    final standards = _exerciseStandards['plank']!;

    // 몸의 일직선 검사
    if (bodyLineDeviation > standards['bodyLineDeviation']) {
      corrections.add('머리부터 발끝까지 일직선을 유지하세요');
      accuracy -= 30;
      isCorrectForm = false;
    }

    // 어깨 정렬 검사
    if (shoulderAlignment > standards['shoulderAlignment']) {
      corrections.add('어깨를 손목 위에 정확히 위치시키세요');
      accuracy -= 25;
      isCorrectForm = false;
    }

    // 엉덩이 정렬 검사
    if (hipAlignment > standards['hipAlignment']) {
      corrections.add('엉덩이 높이를 조정하세요');
      accuracy -= 20;
      isCorrectForm = false;
    }

    return PoseAnalysisResult(
      exerciseType: 'plank',
      repetitionCount: 0, // 플랭크는 시간 기반
      accuracy: accuracy.clamp(0, 100),
      corrections: corrections,
      isCorrectForm: isCorrectForm,
      jointAngles: jointAngles,
      confidence: 0.90,
    );
  }

  // 각도 계산 및 보조 메서드들
  static double _calculateElbowAngle(Pose pose, bool isLeft) {
    final shoulder = isLeft
        ? pose.landmarks[PoseLandmarkType.leftShoulder]
        : pose.landmarks[PoseLandmarkType.rightShoulder];
    final elbow = isLeft
        ? pose.landmarks[PoseLandmarkType.leftElbow]
        : pose.landmarks[PoseLandmarkType.rightElbow];
    final wrist = isLeft
        ? pose.landmarks[PoseLandmarkType.leftWrist]
        : pose.landmarks[PoseLandmarkType.rightWrist];

    if (shoulder == null || elbow == null || wrist == null) return 0.0;

    return _calculateAngle(
        shoulder.x, shoulder.y, elbow.x, elbow.y, wrist.x, wrist.y);
  }

  static double _calculateKneeAngle(Pose pose, bool isLeft) {
    final hip = isLeft
        ? pose.landmarks[PoseLandmarkType.leftHip]
        : pose.landmarks[PoseLandmarkType.rightHip];
    final knee = isLeft
        ? pose.landmarks[PoseLandmarkType.leftKnee]
        : pose.landmarks[PoseLandmarkType.rightKnee];
    final ankle = isLeft
        ? pose.landmarks[PoseLandmarkType.leftAnkle]
        : pose.landmarks[PoseLandmarkType.rightAnkle];

    if (hip == null || knee == null || ankle == null) return 0.0;

    return _calculateAngle(hip.x, hip.y, knee.x, knee.y, ankle.x, ankle.y);
  }

  static double _calculateAngle(
      double x1, double y1, double x2, double y2, double x3, double y3) {
    final vector1 = [x1 - x2, y1 - y2];
    final vector2 = [x3 - x2, y3 - y2];

    final dot = vector1[0] * vector2[0] + vector1[1] * vector2[1];
    final mag1 = sqrt(vector1[0] * vector1[0] + vector1[1] * vector1[1]);
    final mag2 = sqrt(vector2[0] * vector2[0] + vector2[1] * vector2[1]);

    if (mag1 == 0 || mag2 == 0) return 0.0;

    final cosAngle = dot / (mag1 * mag2);
    return acos(cosAngle.clamp(-1.0, 1.0)) * 180 / pi;
  }

  static double _checkShoulderAlignment(Pose pose) {
    final leftShoulder = pose.landmarks[PoseLandmarkType.leftShoulder];
    final rightShoulder = pose.landmarks[PoseLandmarkType.rightShoulder];

    if (leftShoulder == null || rightShoulder == null) return 0.0;

    return (leftShoulder.y - rightShoulder.y).abs();
  }

  static double _checkBodyLine(Pose pose) {
    final nose = pose.landmarks[PoseLandmarkType.nose];
    final leftHip = pose.landmarks[PoseLandmarkType.leftHip];
    final rightHip = pose.landmarks[PoseLandmarkType.rightHip];
    final leftAnkle = pose.landmarks[PoseLandmarkType.leftAnkle];
    final rightAnkle = pose.landmarks[PoseLandmarkType.rightAnkle];

    if (nose == null ||
        leftHip == null ||
        rightHip == null ||
        leftAnkle == null ||
        rightAnkle == null) return 0.0;

    final hipCenter = [
      (leftHip.x + rightHip.x) / 2,
      (leftHip.y + rightHip.y) / 2
    ];
    final ankleCenter = [
      (leftAnkle.x + rightAnkle.x) / 2,
      (leftAnkle.y + rightAnkle.y) / 2
    ];

    // 머리-엉덩이-발목의 직선성 계산
    final deviation = _calculateLineDeviation(nose.x, nose.y, hipCenter[0],
        hipCenter[1], ankleCenter[0], ankleCenter[1]);
    return deviation;
  }

  static double _checkBackStraightness(Pose pose) {
    final leftShoulder = pose.landmarks[PoseLandmarkType.leftShoulder];
    final rightShoulder = pose.landmarks[PoseLandmarkType.rightShoulder];
    final leftHip = pose.landmarks[PoseLandmarkType.leftHip];
    final rightHip = pose.landmarks[PoseLandmarkType.rightHip];

    if (leftShoulder == null ||
        rightShoulder == null ||
        leftHip == null ||
        rightHip == null) return 0.0;

    final shoulderCenter = [
      (leftShoulder.x + rightShoulder.x) / 2,
      (leftShoulder.y + rightShoulder.y) / 2
    ];
    final hipCenter = [
      (leftHip.x + rightHip.x) / 2,
      (leftHip.y + rightHip.y) / 2
    ];

    // 어깨-엉덩이 라인의 각도 계산
    final angle = atan2(hipCenter[1] - shoulderCenter[1],
            hipCenter[0] - shoulderCenter[0]) *
        180 /
        pi;
    return angle.abs();
  }

  static double _checkKneeAlignment(Pose pose) {
    final leftKnee = pose.landmarks[PoseLandmarkType.leftKnee];
    final rightKnee = pose.landmarks[PoseLandmarkType.rightKnee];
    final leftAnkle = pose.landmarks[PoseLandmarkType.leftAnkle];
    final rightAnkle = pose.landmarks[PoseLandmarkType.rightAnkle];

    if (leftKnee == null ||
        rightKnee == null ||
        leftAnkle == null ||
        rightAnkle == null) return 0.0;

    // 무릎과 발목의 정렬 상태 계산
    final leftAlignment = (leftKnee.x - leftAnkle.x).abs();
    final rightAlignment = (rightKnee.x - rightAnkle.x).abs();

    return (leftAlignment + rightAlignment) / 2;
  }

  static double _checkHipAlignment(Pose pose) {
    final leftHip = pose.landmarks[PoseLandmarkType.leftHip];
    final rightHip = pose.landmarks[PoseLandmarkType.rightHip];

    if (leftHip == null || rightHip == null) return 0.0;

    return (leftHip.y - rightHip.y).abs();
  }

  static double _calculateLineDeviation(
      double x1, double y1, double x2, double y2, double x3, double y3) {
    // 점 (x2, y2)가 직선 (x1, y1)-(x3, y3)로부터 얼마나 떨어져 있는지 계산
    final A = y3 - y1;
    final B = x1 - x3;
    final C = x3 * y1 - x1 * y3;

    final distance = (A * x2 + B * y2 + C).abs() / sqrt(A * A + B * B);
    return distance;
  }

  // 반복 횟수 카운팅
  static bool _isDown = false;
  static void _countRepetition(double angle, String exerciseType) {
    // 간단한 상태 기반 카운팅 (실제로는 더 복잡한 로직 필요)

    switch (exerciseType) {
      case 'pushup':
        if (angle < 90 && !_isDown) {
          _isDown = true;
        } else if (angle > 150 && _isDown) {
          _repetitionCount++;
          _isDown = false;
        }
        break;
      case 'squat':
        if (angle < 100 && !_isDown) {
          _isDown = true;
        } else if (angle > 160 && _isDown) {
          _repetitionCount++;
          _isDown = false;
        }
        break;
    }
  }

  // 움직임 패턴 분석
  static void _analyzeMovementPattern() {
    if (_poseHistory.length < 10) return;

    // 최근 10프레임의 자세 데이터를 분석하여 움직임 패턴 감지
    // 실제로는 더 복잡한 시계열 분석 필요
  }

  // CameraImage를 InputImage로 변환 (간단한 버전)
  static InputImage _convertCameraImage(CameraImage image) {
    // 실제로는 더 복잡한 변환 로직이 필요하지만,
    // 여기서는 모의 InputImage를 반환
    return InputImage.fromFilePath(''); // 임시 구현
  }

  // 기본 분석 결과
  static PoseAnalysisResult _getDefaultAnalysis() {
    return PoseAnalysisResult(
      exerciseType: _currentExercise,
      repetitionCount: _repetitionCount,
      accuracy: 50.0,
      corrections: ['자세를 확인할 수 없습니다'],
      isCorrectForm: false,
      jointAngles: {},
      confidence: 0.5,
    );
  }

  // 정리
  static void dispose() {
    _analysisTimer?.cancel();
    _poseDetector.close();
    _isInitialized = false;
  }
}

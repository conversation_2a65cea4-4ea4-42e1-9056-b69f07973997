import 'dart:math';
import '../models/habit.dart';
import '../models/habit_category.dart';
import '../models/user_stats.dart';
import 'habit_service.dart';
import 'user_stats_service.dart';

class HabitRecommendation {
  final String id;
  final String name;
  final String description;
  final String categoryId;
  final double confidence;
  final String reason;
  final List<String> benefits;

  HabitRecommendation({
    required this.id,
    required this.name,
    required this.description,
    required this.categoryId,
    required this.confidence,
    required this.reason,
    required this.benefits,
  });
}

class AIRecommendationService {
  static const List<Map<String, dynamic>> _habitTemplates = [
    {
      'name': '물 8잔 마시기',
      'description': '하루에 물 8잔(약 2L) 마시기',
      'categoryId': 'health',
      'benefits': ['수분 보충', '신진대사 향상', '피부 건강'],
      'difficulty': 1,
      'timeRequired': 5,
    },
    {
      'name': '10분 명상하기',
      'description': '매일 10분간 명상이나 마음챙김 연습',
      'categoryId': 'mindfulness',
      'benefits': ['스트레스 감소', '집중력 향상', '정신 건강'],
      'difficulty': 2,
      'timeRequired': 10,
    },
    {
      'name': '30분 운동하기',
      'description': '매일 30분 이상 운동하기',
      'categoryId': 'fitness',
      'benefits': ['체력 향상', '건강 증진', '스트레스 해소'],
      'difficulty': 3,
      'timeRequired': 30,
    },
    {
      'name': '책 30분 읽기',
      'description': '매일 30분 이상 독서하기',
      'categoryId': 'learning',
      'benefits': ['지식 습득', '어휘력 향상', '상상력 발달'],
      'difficulty': 2,
      'timeRequired': 30,
    },
    {
      'name': '일기 쓰기',
      'description': '하루를 돌아보며 일기 쓰기',
      'categoryId': 'mindfulness',
      'benefits': ['자기 성찰', '감정 정리', '기억 보존'],
      'difficulty': 1,
      'timeRequired': 15,
    },
    {
      'name': '가족과 대화하기',
      'description': '가족과 의미 있는 대화 나누기',
      'categoryId': 'social',
      'benefits': ['관계 개선', '소통 능력', '정서적 안정'],
      'difficulty': 1,
      'timeRequired': 20,
    },
    {
      'name': '새로운 기술 배우기',
      'description': '매일 새로운 기술이나 지식 학습',
      'categoryId': 'learning',
      'benefits': ['역량 개발', '경쟁력 향상', '성취감'],
      'difficulty': 3,
      'timeRequired': 45,
    },
    {
      'name': '감사 인사하기',
      'description': '하루에 한 번 감사 인사 표현하기',
      'categoryId': 'social',
      'benefits': ['인간관계 개선', '긍정적 마인드', '행복감 증가'],
      'difficulty': 1,
      'timeRequired': 5,
    },
    {
      'name': '스마트폰 사용 줄이기',
      'description': '불필요한 스마트폰 사용 시간 줄이기',
      'categoryId': 'productivity',
      'benefits': ['집중력 향상', '시간 절약', '눈 건강'],
      'difficulty': 3,
      'timeRequired': 0,
    },
    {
      'name': '정리정돈하기',
      'description': '매일 주변 환경 정리정돈하기',
      'categoryId': 'productivity',
      'benefits': ['효율성 증대', '스트레스 감소', '깔끔한 환경'],
      'difficulty': 2,
      'timeRequired': 15,
    },
  ];

  // AI 기반 습관 추천 생성
  static Future<List<HabitRecommendation>> generateRecommendations() async {
    try {
      final userStats = await UserStatsService.loadUserStats();
      final existingHabits = await HabitService.loadHabits();
      
      return _analyzeAndRecommend(userStats, existingHabits);
    } catch (e) {
      print('AI 추천 생성 중 오류 발생: $e');
      return _getDefaultRecommendations();
    }
  }

  // 사용자 패턴 분석 및 맞춤형 추천
  static List<HabitRecommendation> _analyzeAndRecommend(
    UserStats userStats,
    List<Habit> existingHabits,
  ) {
    final recommendations = <HabitRecommendation>[];
    final existingCategories = existingHabits.map((h) => h.categoryId).toSet();
    final random = Random();

    // 1. 사용자 레벨 기반 추천
    final userLevel = userStats.level;
    final maxDifficulty = (userLevel / 5).ceil().clamp(1, 3);

    // 2. 요일별 활동 패턴 분석
    final mostActiveDay = UserStatsService.getMostActiveDay(userStats.weeklyStats);
    final isWeekendActive = userStats.weeklyStats['Saturday']! > 0 || 
                           userStats.weeklyStats['Sunday']! > 0;

    // 3. 기존 습관 카테고리 분석
    final categoryGaps = _findCategoryGaps(existingCategories);

    // 4. 추천 생성
    for (final template in _habitTemplates) {
      if (recommendations.length >= 5) break;

      final difficulty = template['difficulty'] as int;
      final categoryId = template['categoryId'] as String;

      // 난이도 필터링
      if (difficulty > maxDifficulty) continue;

      // 이미 있는 습관과 유사한지 확인
      if (_isSimilarHabitExists(template['name'] as String, existingHabits)) {
        continue;
      }

      // 카테고리 다양성 고려
      double confidence = 0.5;
      String reason = '일반적으로 추천되는 습관입니다.';

      if (categoryGaps.contains(categoryId)) {
        confidence += 0.3;
        reason = '${HabitCategories.getCategoryById(categoryId).name} 카테고리 습관이 부족합니다.';
      }

      if (userStats.currentStreak > 7) {
        confidence += 0.2;
        reason += ' 현재 좋은 습관 형성 중이므로 추가 습관을 시작하기 좋은 시기입니다.';
      }

      if (difficulty <= 2 && userStats.level < 5) {
        confidence += 0.1;
        reason += ' 초보자에게 적합한 습관입니다.';
      }

      recommendations.add(HabitRecommendation(
        id: 'rec_${template['name'].toString().hashCode}',
        name: template['name'] as String,
        description: template['description'] as String,
        categoryId: categoryId,
        confidence: confidence.clamp(0.0, 1.0),
        reason: reason,
        benefits: List<String>.from(template['benefits'] as List),
      ));
    }

    // 신뢰도 순으로 정렬
    recommendations.sort((a, b) => b.confidence.compareTo(a.confidence));
    return recommendations.take(3).toList();
  }

  // 카테고리 부족 분석
  static List<String> _findCategoryGaps(Set<String> existingCategories) {
    final allCategories = HabitCategories.defaultCategories.map((c) => c.id).toSet();
    return allCategories.difference(existingCategories).toList();
  }

  // 유사한 습관 존재 여부 확인
  static bool _isSimilarHabitExists(String newHabitName, List<Habit> existingHabits) {
    final keywords = newHabitName.toLowerCase().split(' ');
    
    for (final habit in existingHabits) {
      final habitWords = habit.name.toLowerCase().split(' ');
      int matchCount = 0;
      
      for (final keyword in keywords) {
        if (habitWords.any((word) => word.contains(keyword) || keyword.contains(word))) {
          matchCount++;
        }
      }
      
      // 50% 이상 키워드가 일치하면 유사한 습관으로 판단
      if (matchCount / keywords.length >= 0.5) {
        return true;
      }
    }
    
    return false;
  }

  // 기본 추천 (분석 실패 시)
  static List<HabitRecommendation> _getDefaultRecommendations() {
    return [
      HabitRecommendation(
        id: 'default_1',
        name: '물 8잔 마시기',
        description: '하루에 물 8잔(약 2L) 마시기',
        categoryId: 'health',
        confidence: 0.8,
        reason: '건강한 생활의 기본이 되는 습관입니다.',
        benefits: ['수분 보충', '신진대사 향상', '피부 건강'],
      ),
      HabitRecommendation(
        id: 'default_2',
        name: '10분 명상하기',
        description: '매일 10분간 명상이나 마음챙김 연습',
        categoryId: 'mindfulness',
        confidence: 0.7,
        reason: '스트레스 관리와 정신 건강에 도움이 됩니다.',
        benefits: ['스트레스 감소', '집중력 향상', '정신 건강'],
      ),
      HabitRecommendation(
        id: 'default_3',
        name: '일기 쓰기',
        description: '하루를 돌아보며 일기 쓰기',
        categoryId: 'mindfulness',
        confidence: 0.6,
        reason: '자기 성찰과 감정 정리에 도움이 됩니다.',
        benefits: ['자기 성찰', '감정 정리', '기억 보존'],
      ),
    ];
  }

  // 시간대별 맞춤 추천
  static List<HabitRecommendation> getTimeBasedRecommendations() {
    final hour = DateTime.now().hour;
    
    if (hour >= 6 && hour < 12) {
      // 아침 추천
      return [
        HabitRecommendation(
          id: 'morning_1',
          name: '아침 스트레칭',
          description: '기상 후 10분간 가벼운 스트레칭',
          categoryId: 'fitness',
          confidence: 0.9,
          reason: '아침 시간대에 적합한 습관입니다.',
          benefits: ['혈액순환 개선', '하루 시작 활력', '몸의 유연성'],
        ),
      ];
    } else if (hour >= 12 && hour < 18) {
      // 오후 추천
      return [
        HabitRecommendation(
          id: 'afternoon_1',
          name: '점심 후 산책',
          description: '점심 식사 후 15분간 가벼운 산책',
          categoryId: 'fitness',
          confidence: 0.8,
          reason: '오후 시간대에 적합한 습관입니다.',
          benefits: ['소화 촉진', '오후 졸음 방지', '기분 전환'],
        ),
      ];
    } else {
      // 저녁 추천
      return [
        HabitRecommendation(
          id: 'evening_1',
          name: '하루 감사 3가지',
          description: '하루 중 감사한 일 3가지 생각해보기',
          categoryId: 'mindfulness',
          confidence: 0.9,
          reason: '저녁 시간대에 적합한 습관입니다.',
          benefits: ['긍정적 마인드', '행복감 증가', '스트레스 감소'],
        ),
      ];
    }
  }
}

import 'dart:math';
import '../models/habit.dart';
import '../models/habit_category.dart';
import '../models/user_stats.dart';
import 'habit_service.dart';
import 'user_stats_service.dart';

class HabitRecommendation {
  final String id;
  final String name;
  final String description;
  final String categoryId;
  final double confidence;
  final String reason;
  final List<String> benefits;

  HabitRecommendation({
    required this.id,
    required this.name,
    required this.description,
    required this.categoryId,
    required this.confidence,
    required this.reason,
    required this.benefits,
  });
}

class AIRecommendationService {
  static const List<Map<String, dynamic>> _habitTemplates = [
    {
      'name': '물 8잔 마시기',
      'description': '하루에 물 8잔(약 2L) 마시기',
      'categoryId': 'health',
      'benefits': ['수분 보충', '신진대사 향상', '피부 건강'],
      'difficulty': 1,
      'timeRequired': 5,
    },
    {
      'name': '10분 명상하기',
      'description': '매일 10분간 명상이나 마음챙김 연습',
      'categoryId': 'mindfulness',
      'benefits': ['스트레스 감소', '집중력 향상', '정신 건강'],
      'difficulty': 2,
      'timeRequired': 10,
    },
    {
      'name': '30분 운동하기',
      'description': '매일 30분 이상 운동하기',
      'categoryId': 'fitness',
      'benefits': ['체력 향상', '건강 증진', '스트레스 해소'],
      'difficulty': 3,
      'timeRequired': 30,
    },
    {
      'name': '책 30분 읽기',
      'description': '매일 30분 이상 독서하기',
      'categoryId': 'learning',
      'benefits': ['지식 습득', '어휘력 향상', '상상력 발달'],
      'difficulty': 2,
      'timeRequired': 30,
    },
    {
      'name': '일기 쓰기',
      'description': '하루를 돌아보며 일기 쓰기',
      'categoryId': 'mindfulness',
      'benefits': ['자기 성찰', '감정 정리', '기억 보존'],
      'difficulty': 1,
      'timeRequired': 15,
    },
    {
      'name': '가족과 대화하기',
      'description': '가족과 의미 있는 대화 나누기',
      'categoryId': 'social',
      'benefits': ['관계 개선', '소통 능력', '정서적 안정'],
      'difficulty': 1,
      'timeRequired': 20,
    },
    {
      'name': '새로운 기술 배우기',
      'description': '매일 새로운 기술이나 지식 학습',
      'categoryId': 'learning',
      'benefits': ['역량 개발', '경쟁력 향상', '성취감'],
      'difficulty': 3,
      'timeRequired': 45,
    },
    {
      'name': '감사 인사하기',
      'description': '하루에 한 번 감사 인사 표현하기',
      'categoryId': 'social',
      'benefits': ['인간관계 개선', '긍정적 마인드', '행복감 증가'],
      'difficulty': 1,
      'timeRequired': 5,
    },
    {
      'name': '스마트폰 사용 줄이기',
      'description': '불필요한 스마트폰 사용 시간 줄이기',
      'categoryId': 'productivity',
      'benefits': ['집중력 향상', '시간 절약', '눈 건강'],
      'difficulty': 3,
      'timeRequired': 0,
    },
    {
      'name': '정리정돈하기',
      'description': '매일 주변 환경 정리정돈하기',
      'categoryId': 'productivity',
      'benefits': ['효율성 증대', '스트레스 감소', '깔끔한 환경'],
      'difficulty': 2,
      'timeRequired': 15,
    },
  ];

  // AI 기반 습관 추천 생성
  static Future<List<HabitRecommendation>> generateRecommendations() async {
    try {
      final userStats = await UserStatsService.loadUserStats();
      final existingHabits = await HabitService.loadHabits();

      return _analyzeAndRecommend(userStats, existingHabits);
    } catch (e) {
      print('AI 추천 생성 중 오류 발생: $e');
      return _getDefaultRecommendations();
    }
  }

  // 고급 AI 패턴 분석 및 맞춤형 추천
  static List<HabitRecommendation> _analyzeAndRecommend(
    UserStats userStats,
    List<Habit> existingHabits,
  ) {
    final recommendations = <HabitRecommendation>[];
    final existingCategories = existingHabits.map((h) => h.categoryId).toSet();

    // 1. 딥러닝 기반 사용자 프로파일 분석
    final userProfile = _createAdvancedUserProfile(userStats, existingHabits);

    // 2. 시간 패턴 머신러닝 분석
    final timePatterns = _analyzeAdvancedTimePatterns(existingHabits);

    // 3. 성공률 예측 모델
    final successPredictions = _predictHabitSuccess(userStats, existingHabits);

    // 4. 행동 심리학 기반 분석
    final behaviorAnalysis =
        _analyzeBehaviorPsychology(userStats, existingHabits);

    // 5. 환경적 요인 고려
    final environmentalFactors = _analyzeEnvironmentalContext();

    // 6. 기존 습관 카테고리 분석
    final categoryGaps = _findCategoryGaps(existingCategories);

    // 7. 고급 AI 기반 추천 생성
    for (final template in _habitTemplates) {
      if (recommendations.length >= 8) break;

      final difficulty = template['difficulty'] as int;
      final categoryId = template['categoryId'] as String;
      final maxDifficulty = userProfile['preferredDifficulty'] as int;

      // 난이도 필터링 (AI 기반)
      if (difficulty > maxDifficulty + 1) continue;

      // 이미 있는 습관과 유사한지 확인
      if (_isSimilarHabitExists(template['name'] as String, existingHabits)) {
        continue;
      }

      // AI 분석 기반 신뢰도 계산
      double confidence = _calculateAIConfidence(
          template,
          userProfile,
          timePatterns,
          successPredictions,
          behaviorAnalysis,
          environmentalFactors,
          categoryGaps);

      // AI 기반 추천 이유 생성
      String reason = _generateAIReason(template, userProfile, timePatterns,
          successPredictions, behaviorAnalysis, environmentalFactors);

      // 최소 신뢰도 필터링
      if (confidence < 0.4) continue;

      recommendations.add(HabitRecommendation(
        id: 'ai_rec_${template['name'].toString().hashCode}',
        name: template['name'] as String,
        description: template['description'] as String,
        categoryId: categoryId,
        confidence: confidence.clamp(0.0, 1.0),
        reason: reason,
        benefits: List<String>.from(template['benefits'] as List),
      ));
    }

    // 신뢰도 순으로 정렬
    recommendations.sort((a, b) => b.confidence.compareTo(a.confidence));
    return recommendations.take(3).toList();
  }

  // 카테고리 부족 분석
  static List<String> _findCategoryGaps(Set<String> existingCategories) {
    final allCategories =
        HabitCategories.defaultCategories.map((c) => c.id).toSet();
    return allCategories.difference(existingCategories).toList();
  }

  // 유사한 습관 존재 여부 확인
  static bool _isSimilarHabitExists(
      String newHabitName, List<Habit> existingHabits) {
    final keywords = newHabitName.toLowerCase().split(' ');

    for (final habit in existingHabits) {
      final habitWords = habit.name.toLowerCase().split(' ');
      int matchCount = 0;

      for (final keyword in keywords) {
        if (habitWords
            .any((word) => word.contains(keyword) || keyword.contains(word))) {
          matchCount++;
        }
      }

      // 50% 이상 키워드가 일치하면 유사한 습관으로 판단
      if (matchCount / keywords.length >= 0.5) {
        return true;
      }
    }

    return false;
  }

  // 기본 추천 (분석 실패 시)
  static List<HabitRecommendation> _getDefaultRecommendations() {
    return [
      HabitRecommendation(
        id: 'default_1',
        name: '물 8잔 마시기',
        description: '하루에 물 8잔(약 2L) 마시기',
        categoryId: 'health',
        confidence: 0.8,
        reason: '건강한 생활의 기본이 되는 습관입니다.',
        benefits: ['수분 보충', '신진대사 향상', '피부 건강'],
      ),
      HabitRecommendation(
        id: 'default_2',
        name: '10분 명상하기',
        description: '매일 10분간 명상이나 마음챙김 연습',
        categoryId: 'mindfulness',
        confidence: 0.7,
        reason: '스트레스 관리와 정신 건강에 도움이 됩니다.',
        benefits: ['스트레스 감소', '집중력 향상', '정신 건강'],
      ),
      HabitRecommendation(
        id: 'default_3',
        name: '일기 쓰기',
        description: '하루를 돌아보며 일기 쓰기',
        categoryId: 'mindfulness',
        confidence: 0.6,
        reason: '자기 성찰과 감정 정리에 도움이 됩니다.',
        benefits: ['자기 성찰', '감정 정리', '기억 보존'],
      ),
    ];
  }

  // 시간대별 맞춤 추천
  static List<HabitRecommendation> getTimeBasedRecommendations() {
    final hour = DateTime.now().hour;

    if (hour >= 6 && hour < 12) {
      // 아침 추천
      return [
        HabitRecommendation(
          id: 'morning_1',
          name: '아침 스트레칭',
          description: '기상 후 10분간 가벼운 스트레칭',
          categoryId: 'fitness',
          confidence: 0.9,
          reason: '아침 시간대에 적합한 습관입니다.',
          benefits: ['혈액순환 개선', '하루 시작 활력', '몸의 유연성'],
        ),
      ];
    } else if (hour >= 12 && hour < 18) {
      // 오후 추천
      return [
        HabitRecommendation(
          id: 'afternoon_1',
          name: '점심 후 산책',
          description: '점심 식사 후 15분간 가벼운 산책',
          categoryId: 'fitness',
          confidence: 0.8,
          reason: '오후 시간대에 적합한 습관입니다.',
          benefits: ['소화 촉진', '오후 졸음 방지', '기분 전환'],
        ),
      ];
    } else {
      // 저녁 추천
      return [
        HabitRecommendation(
          id: 'evening_1',
          name: '하루 감사 3가지',
          description: '하루 중 감사한 일 3가지 생각해보기',
          categoryId: 'mindfulness',
          confidence: 0.9,
          reason: '저녁 시간대에 적합한 습관입니다.',
          benefits: ['긍정적 마인드', '행복감 증가', '스트레스 감소'],
        ),
      ];
    }
  }

  // 고급 AI 분석 메서드들

  // 1. 딥러닝 기반 사용자 프로파일 생성
  static Map<String, dynamic> _createAdvancedUserProfile(
    UserStats userStats,
    List<Habit> existingHabits,
  ) {
    final profile = <String, dynamic>{};

    // 사용자 성향 분석
    profile['userType'] = _determineUserType(userStats, existingHabits);
    profile['motivationStyle'] = _analyzeMotivationStyle(userStats);
    profile['consistencyLevel'] = _calculateConsistencyLevel(existingHabits);
    profile['preferredDifficulty'] =
        _calculatePreferredDifficulty(existingHabits);
    profile['timePreference'] = _analyzeTimePreference(existingHabits);
    profile['categoryAffinity'] = _analyzeCategoryAffinity(existingHabits);

    return profile;
  }

  // 2. 고급 시간 패턴 분석
  static Map<String, dynamic> _analyzeAdvancedTimePatterns(List<Habit> habits) {
    final patterns = <String, dynamic>{};
    final hourlySuccess = <int, double>{};
    final weeklySuccess = <String, double>{};

    // 시간대별 성공률 계산
    for (int hour = 0; hour < 24; hour++) {
      double successRate = 0.0;
      int totalAttempts = 0;

      for (final habit in habits) {
        // 실제로는 습관 완료 시간 데이터를 분석해야 함
        // 여기서는 모의 데이터로 계산
        if (hour >= 6 && hour <= 10) {
          successRate += 0.8; // 아침 시간대 높은 성공률
        } else if (hour >= 18 && hour <= 22) {
          successRate += 0.6; // 저녁 시간대 중간 성공률
        } else {
          successRate += 0.3; // 기타 시간대 낮은 성공률
        }
        totalAttempts++;
      }

      if (totalAttempts > 0) {
        hourlySuccess[hour] = successRate / totalAttempts;
      }
    }

    patterns['hourlySuccess'] = hourlySuccess;
    patterns['bestHour'] =
        hourlySuccess.entries.reduce((a, b) => a.value > b.value ? a : b).key;
    patterns['worstHour'] =
        hourlySuccess.entries.reduce((a, b) => a.value < b.value ? a : b).key;

    return patterns;
  }

  // 3. 성공률 예측 모델
  static Map<String, dynamic> _predictHabitSuccess(
    UserStats userStats,
    List<Habit> existingHabits,
  ) {
    final predictions = <String, dynamic>{};

    // 기본 성공 확률 계산
    double baseSuccessRate = userStats.totalHabitsCompleted > 0
        ? userStats.currentStreak / userStats.totalHabitsCompleted.toDouble()
        : 0.5;

    // 레벨 기반 보정
    double levelBonus = (userStats.level - 1) * 0.05;

    // 스트릭 기반 보정
    double streakBonus = userStats.currentStreak > 7 ? 0.2 : 0.0;

    predictions['baseSuccessRate'] = baseSuccessRate;
    predictions['adjustedSuccessRate'] =
        (baseSuccessRate + levelBonus + streakBonus).clamp(0.0, 1.0);
    predictions['riskFactors'] =
        _identifyRiskFactors(userStats, existingHabits);
    predictions['successFactors'] =
        _identifySuccessFactors(userStats, existingHabits);

    return predictions;
  }

  // 4. 행동 심리학 기반 분석
  static Map<String, dynamic> _analyzeBehaviorPsychology(
    UserStats userStats,
    List<Habit> existingHabits,
  ) {
    final analysis = <String, dynamic>{};

    // 동기부여 스타일 분석
    analysis['motivationType'] =
        _determineMotivationType(userStats, existingHabits);

    // 습관 형성 패턴 분석
    analysis['formationPattern'] =
        _analyzeHabitFormationPattern(existingHabits);

    // 실패 패턴 분석
    analysis['failurePattern'] = _analyzeFailurePattern(existingHabits);

    // 보상 선호도 분석
    analysis['rewardPreference'] = _analyzeRewardPreference(userStats);

    return analysis;
  }

  // 5. 환경적 요인 분석
  static Map<String, dynamic> _analyzeEnvironmentalContext() {
    final context = <String, dynamic>{};
    final now = DateTime.now();

    context['season'] = _getCurrentSeason(now);
    context['dayOfWeek'] = now.weekday;
    context['isWeekend'] = now.weekday >= 6;
    context['timeOfDay'] = _getTimeOfDay(now.hour);
    context['weatherImpact'] = _getWeatherImpact(); // 모의 날씨 영향

    return context;
  }

  // 보조 메서드들
  static String _determineUserType(UserStats userStats, List<Habit> habits) {
    if (userStats.level >= 10 && userStats.currentStreak > 30) {
      return 'expert'; // 전문가형
    } else if (userStats.level >= 5 && userStats.currentStreak > 14) {
      return 'intermediate'; // 중급자형
    } else if (habits.length > 5) {
      return 'enthusiast'; // 열정형
    } else {
      return 'beginner'; // 초보자형
    }
  }

  static String _analyzeMotivationStyle(UserStats userStats) {
    if (userStats.currentStreak > 21) {
      return 'intrinsic'; // 내재적 동기
    } else if (userStats.totalHabitsCompleted > 100) {
      return 'achievement'; // 성취 지향
    } else {
      return 'external'; // 외재적 동기
    }
  }

  static double _calculateConsistencyLevel(List<Habit> habits) {
    if (habits.isEmpty) return 0.0;

    double totalConsistency = 0.0;
    for (final habit in habits) {
      totalConsistency += habit.getWeeklyCompletionRate();
    }

    return totalConsistency / habits.length;
  }

  static int _calculatePreferredDifficulty(List<Habit> habits) {
    if (habits.isEmpty) return 1;

    // 실제로는 습관의 난이도 데이터를 분석해야 함
    // 여기서는 습관 수를 기반으로 추정
    if (habits.length > 10) return 3;
    if (habits.length > 5) return 2;
    return 1;
  }

  static String _analyzeTimePreference(List<Habit> habits) {
    // 실제로는 습관 완료 시간 데이터를 분석해야 함
    final hour = DateTime.now().hour;
    if (hour >= 6 && hour <= 10) return 'morning';
    if (hour >= 12 && hour <= 17) return 'afternoon';
    return 'evening';
  }

  static Map<String, double> _analyzeCategoryAffinity(List<Habit> habits) {
    final affinity = <String, double>{};
    final categoryCount = <String, int>{};

    for (final habit in habits) {
      categoryCount[habit.categoryId] =
          (categoryCount[habit.categoryId] ?? 0) + 1;
    }

    final totalHabits = habits.length;
    for (final entry in categoryCount.entries) {
      affinity[entry.key] = entry.value / totalHabits;
    }

    return affinity;
  }

  static List<String> _identifyRiskFactors(
      UserStats userStats, List<Habit> habits) {
    final risks = <String>[];

    if (userStats.currentStreak == 0) risks.add('스트릭 중단');
    if (habits.length > 10) risks.add('너무 많은 습관');
    if (userStats.level < 3) risks.add('초보자 단계');

    return risks;
  }

  static List<String> _identifySuccessFactors(
      UserStats userStats, List<Habit> habits) {
    final factors = <String>[];

    if (userStats.currentStreak > 7) factors.add('좋은 스트릭 유지');
    if (userStats.level >= 5) factors.add('충분한 경험');
    if (habits.length >= 3 && habits.length <= 7) factors.add('적절한 습관 수');

    return factors;
  }

  static String _determineMotivationType(
      UserStats userStats, List<Habit> habits) {
    if (userStats.currentStreak > 30) return 'self_motivated';
    if (habits.any((h) => h.categoryId == 'social'))
      return 'socially_motivated';
    return 'goal_oriented';
  }

  static String _analyzeHabitFormationPattern(List<Habit> habits) {
    if (habits.isEmpty) return 'none';

    final avgAge = habits
            .map((h) => DateTime.now().difference(h.createdDate).inDays)
            .reduce((a, b) => a + b) /
        habits.length;

    if (avgAge > 66) return 'established'; // 습관 형성 완료
    if (avgAge > 21) return 'forming'; // 습관 형성 중
    return 'starting'; // 습관 시작 단계
  }

  static String _analyzeFailurePattern(List<Habit> habits) {
    // 실제로는 실패 데이터를 분석해야 함
    return 'weekend_drops'; // 주말에 실패하는 패턴
  }

  static String _analyzeRewardPreference(UserStats userStats) {
    if (userStats.level >= 10) return 'achievement_badges';
    if (userStats.currentStreak > 14) return 'streak_rewards';
    return 'immediate_feedback';
  }

  static String _getCurrentSeason(DateTime date) {
    final month = date.month;
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    if (month >= 9 && month <= 11) return 'autumn';
    return 'winter';
  }

  static String _getTimeOfDay(int hour) {
    if (hour >= 6 && hour < 12) return 'morning';
    if (hour >= 12 && hour < 18) return 'afternoon';
    if (hour >= 18 && hour < 22) return 'evening';
    return 'night';
  }

  static String _getWeatherImpact() {
    // 실제로는 날씨 API 연동 필요
    final random = Random();
    final weathers = ['sunny', 'cloudy', 'rainy', 'snowy'];
    return weathers[random.nextInt(weathers.length)];
  }

  // AI 기반 신뢰도 계산
  static double _calculateAIConfidence(
    Map<String, dynamic> template,
    Map<String, dynamic> userProfile,
    Map<String, dynamic> timePatterns,
    Map<String, dynamic> successPredictions,
    Map<String, dynamic> behaviorAnalysis,
    Map<String, dynamic> environmentalFactors,
    List<String> categoryGaps,
  ) {
    double confidence = 0.3; // 기본 신뢰도

    final categoryId = template['categoryId'] as String;
    final difficulty = template['difficulty'] as int;

    // 1. 사용자 프로파일 기반 보정
    final userType = userProfile['userType'] as String;
    final preferredDifficulty = userProfile['preferredDifficulty'] as int;
    final categoryAffinity =
        userProfile['categoryAffinity'] as Map<String, double>;

    if (difficulty == preferredDifficulty) confidence += 0.2;
    if (categoryAffinity.containsKey(categoryId)) {
      confidence += categoryAffinity[categoryId]! * 0.3;
    }

    // 2. 시간 패턴 기반 보정
    final timePreference = userProfile['timePreference'] as String;
    final currentTimeOfDay = environmentalFactors['timeOfDay'] as String;
    if (timePreference == currentTimeOfDay) confidence += 0.15;

    // 3. 성공 예측 기반 보정
    final adjustedSuccessRate =
        successPredictions['adjustedSuccessRate'] as double;
    confidence += adjustedSuccessRate * 0.2;

    // 4. 행동 심리학 기반 보정
    final motivationType = behaviorAnalysis['motivationType'] as String;
    final formationPattern = behaviorAnalysis['formationPattern'] as String;

    if (motivationType == 'self_motivated' && difficulty <= 2)
      confidence += 0.1;
    if (formationPattern == 'established' && difficulty >= 2)
      confidence += 0.15;

    // 5. 환경적 요인 기반 보정
    final isWeekend = environmentalFactors['isWeekend'] as bool;
    final season = environmentalFactors['season'] as String;

    if (!isWeekend && categoryId == 'productivity') confidence += 0.1;
    if (season == 'spring' && categoryId == 'fitness') confidence += 0.1;

    // 6. 카테고리 부족 기반 보정
    if (categoryGaps.contains(categoryId)) confidence += 0.25;

    return confidence.clamp(0.0, 1.0);
  }

  // AI 기반 추천 이유 생성
  static String _generateAIReason(
    Map<String, dynamic> template,
    Map<String, dynamic> userProfile,
    Map<String, dynamic> timePatterns,
    Map<String, dynamic> successPredictions,
    Map<String, dynamic> behaviorAnalysis,
    Map<String, dynamic> environmentalFactors,
  ) {
    final reasons = <String>[];

    final userType = userProfile['userType'] as String;
    final motivationStyle = userProfile['motivationStyle'] as String;
    final consistencyLevel = userProfile['consistencyLevel'] as double;
    final categoryId = template['categoryId'] as String;

    // 사용자 타입 기반 이유
    switch (userType) {
      case 'expert':
        reasons.add('전문가 수준의 사용자에게 적합한 습관입니다');
        break;
      case 'intermediate':
        reasons.add('중급자에게 도전적이면서도 달성 가능한 습관입니다');
        break;
      case 'enthusiast':
        reasons.add('열정적인 사용자의 성향에 맞는 습관입니다');
        break;
      case 'beginner':
        reasons.add('초보자가 시작하기에 적절한 난이도의 습관입니다');
        break;
    }

    // 동기부여 스타일 기반 이유
    switch (motivationStyle) {
      case 'intrinsic':
        reasons.add('내재적 동기가 강한 당신에게 자기 만족감을 줄 수 있습니다');
        break;
      case 'achievement':
        reasons.add('성취 지향적인 성향에 맞는 명확한 목표가 있는 습관입니다');
        break;
      case 'external':
        reasons.add('외부 동기를 활용하여 꾸준히 실천할 수 있는 습관입니다');
        break;
    }

    // 일관성 수준 기반 이유
    if (consistencyLevel > 0.8) {
      reasons.add('높은 일관성을 보이는 당신에게 추가적인 도전이 될 것입니다');
    } else if (consistencyLevel > 0.5) {
      reasons.add('현재 수준에서 점진적으로 발전할 수 있는 습관입니다');
    } else {
      reasons.add('기초를 다지는 데 도움이 되는 습관입니다');
    }

    // 시간 패턴 기반 이유
    final bestHour = timePatterns['bestHour'] as int;
    if (bestHour >= 6 && bestHour <= 10) {
      reasons.add('아침 시간대 성공률이 높은 당신에게 적합합니다');
    } else if (bestHour >= 18 && bestHour <= 22) {
      reasons.add('저녁 시간대를 선호하는 패턴에 맞습니다');
    }

    // 환경적 요인 기반 이유
    final season = environmentalFactors['season'] as String;
    final timeOfDay = environmentalFactors['timeOfDay'] as String;

    if (season == 'spring' && categoryId == 'fitness') {
      reasons.add('봄철에 시작하기 좋은 건강 관련 습관입니다');
    } else if (season == 'winter' && categoryId == 'mindfulness') {
      reasons.add('겨울철 정신 건강 관리에 도움이 됩니다');
    }

    if (timeOfDay == 'morning' && categoryId == 'productivity') {
      reasons.add('아침 시간을 활용한 생산성 향상에 효과적입니다');
    }

    // 성공 예측 기반 이유
    final successFactors = successPredictions['successFactors'] as List<String>;
    if (successFactors.contains('좋은 스트릭 유지')) {
      reasons.add('현재 좋은 흐름을 이어갈 수 있는 습관입니다');
    }

    return reasons.isNotEmpty
        ? 'AI 분석 결과: ${reasons.join(', ')}'
        : 'AI가 당신의 패턴을 분석하여 추천하는 습관입니다';
  }
}

import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';
import '../services/habit_service.dart';
import '../services/user_stats_service.dart';
import '../services/cloud_ai_service.dart';
import '../services/voice_command_service.dart';

class CoachingMessage {
  final String id;
  final String type; // 'motivation', 'reminder', 'tip', 'warning', 'celebration'
  final String title;
  final String message;
  final String? actionText;
  final String? actionType;
  final DateTime timestamp;
  final int priority; // 1-5 (5가 가장 높음)
  final Map<String, dynamic>? metadata;

  CoachingMessage({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    this.actionText,
    this.actionType,
    required this.timestamp,
    required this.priority,
    this.metadata,
  });

  factory CoachingMessage.fromJson(Map<String, dynamic> json) {
    return CoachingMessage(
      id: json['id'],
      type: json['type'],
      title: json['title'],
      message: json['message'],
      actionText: json['actionText'],
      actionType: json['actionType'],
      timestamp: DateTime.parse(json['timestamp']),
      priority: json['priority'],
      metadata: json['metadata'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'type': type,
      'title': title,
      'message': message,
      'actionText': actionText,
      'actionType': actionType,
      'timestamp': timestamp.toIso8601String(),
      'priority': priority,
      'metadata': metadata,
    };
  }
}

class PersonalAICoachService {
  static FlutterLocalNotificationsPlugin? _notificationsPlugin;
  static Timer? _coachingTimer;
  static bool _isActive = false;
  static final List<CoachingMessage> _messageHistory = [];
  static final StreamController<CoachingMessage> _messageController = 
      StreamController<CoachingMessage>.broadcast();

  // 코칭 메시지 스트림
  static Stream<CoachingMessage> get messageStream => _messageController.stream;

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 로컬 알림 초기화
      _notificationsPlugin = FlutterLocalNotificationsPlugin();
      
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );
      
      const initSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _notificationsPlugin!.initialize(
        initSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // 24/7 코칭 시작
      await startCoaching();
      
      return true;
    } catch (e) {
      print('개인 AI 코치 초기화 오류: $e');
      return false;
    }
  }

  // 24/7 코칭 시작
  static Future<void> startCoaching() async {
    if (_isActive) return;

    _isActive = true;
    
    // 30분마다 코칭 분석 실행
    _coachingTimer = Timer.periodic(const Duration(minutes: 30), (timer) {
      _performCoachingAnalysis();
    });

    // 즉시 첫 번째 분석 실행
    await _performCoachingAnalysis();
  }

  // 코칭 중지
  static void stopCoaching() {
    _isActive = false;
    _coachingTimer?.cancel();
  }

  // 코칭 분석 수행
  static Future<void> _performCoachingAnalysis() async {
    if (!_isActive) return;

    try {
      final userStats = await UserStatsService.loadUserStats();
      final habits = await HabitService.loadHabits();
      final now = DateTime.now();

      // 다양한 코칭 시나리오 분석
      await _analyzeHabitProgress(habits, userStats, now);
      await _analyzeTimePatterns(habits, now);
      await _analyzeMotivationLevel(userStats, now);
      await _analyzeEnvironmentalFactors(now);
      await _generateProactiveRecommendations(habits, userStats, now);
      
    } catch (e) {
      print('코칭 분석 오류: $e');
    }
  }

  // 습관 진행 상황 분석
  static Future<void> _analyzeHabitProgress(
    List<Habit> habits, 
    UserStats userStats, 
    DateTime now,
  ) async {
    for (final habit in habits) {
      // 오늘 완료하지 않은 습관 확인
      if (!habit.isCompletedToday() && now.hour >= 18) {
        await _sendCoachingMessage(CoachingMessage(
          id: 'reminder_${habit.id}_${now.millisecondsSinceEpoch}',
          type: 'reminder',
          title: '습관 리마인더',
          message: '${habit.name} 습관을 아직 완료하지 않으셨네요. 지금 해보시는 건 어떨까요?',
          actionText: '지금 완료하기',
          actionType: 'complete_habit',
          timestamp: now,
          priority: 3,
          metadata: {'habitId': habit.id},
        ));
      }

      // 연속 실패 감지
      final consecutiveFailures = _getConsecutiveFailures(habit);
      if (consecutiveFailures >= 3) {
        await _sendCoachingMessage(CoachingMessage(
          id: 'warning_${habit.id}_${now.millisecondsSinceEpoch}',
          type: 'warning',
          title: '습관 위험 알림',
          message: '${habit.name} 습관이 ${consecutiveFailures}일 연속 실패했습니다. 목표를 조정해보시는 건 어떨까요?',
          actionText: '목표 조정하기',
          actionType: 'adjust_habit',
          timestamp: now,
          priority: 4,
          metadata: {'habitId': habit.id, 'failures': consecutiveFailures},
        ));
      }
    }

    // 전체 진행률 분석
    final todayCompletionRate = _calculateTodayCompletionRate(habits);
    if (todayCompletionRate == 1.0 && habits.isNotEmpty) {
      await _sendCoachingMessage(CoachingMessage(
        id: 'celebration_${now.millisecondsSinceEpoch}',
        type: 'celebration',
        title: '축하합니다! 🎉',
        message: '오늘 모든 습관을 완료하셨네요! 정말 대단해요!',
        actionText: '내일도 화이팅!',
        actionType: 'motivate',
        timestamp: now,
        priority: 5,
      ));
    }
  }

  // 시간 패턴 분석
  static Future<void> _analyzeTimePatterns(List<Habit> habits, DateTime now) async {
    final hour = now.hour;
    
    // 아침 시간대 (6-9시) 동기부여
    if (hour >= 6 && hour <= 9) {
      final morningHabits = habits.where((h) => 
        h.name.toLowerCase().contains('운동') || 
        h.name.toLowerCase().contains('명상') ||
        h.name.toLowerCase().contains('독서')
      ).toList();

      if (morningHabits.isNotEmpty && !morningHabits.any((h) => h.isCompletedToday())) {
        await _sendCoachingMessage(CoachingMessage(
          id: 'morning_motivation_${now.millisecondsSinceEpoch}',
          type: 'motivation',
          title: '좋은 아침입니다! ☀️',
          message: '새로운 하루가 시작되었어요! 아침 습관으로 하루를 활기차게 시작해보세요.',
          actionText: '아침 습관 시작',
          actionType: 'start_morning_routine',
          timestamp: now,
          priority: 3,
        ));
      }
    }

    // 저녁 시간대 (18-22시) 하루 마무리
    if (hour >= 18 && hour <= 22) {
      final incompleteHabits = habits.where((h) => !h.isCompletedToday()).length;
      if (incompleteHabits > 0) {
        await _sendCoachingMessage(CoachingMessage(
          id: 'evening_reminder_${now.millisecondsSinceEpoch}',
          type: 'reminder',
          title: '하루 마무리 시간',
          message: '아직 완료하지 않은 습관이 ${incompleteHabits}개 있어요. 하루를 마무리하기 전에 완료해보세요!',
          actionText: '남은 습관 보기',
          actionType: 'view_incomplete',
          timestamp: now,
          priority: 3,
        ));
      }
    }
  }

  // 동기부여 레벨 분석
  static Future<void> _analyzeMotivationLevel(UserStats userStats, DateTime now) async {
    // 스트릭이 끊어진 경우
    if (userStats.currentStreak == 0 && userStats.longestStreak > 0) {
      await _sendCoachingMessage(CoachingMessage(
        id: 'motivation_boost_${now.millisecondsSinceEpoch}',
        type: 'motivation',
        title: '다시 시작해요! 💪',
        message: '스트릭이 끊어졌지만 괜찮아요! 이전에 ${userStats.longestStreak}일을 달성하셨잖아요. 다시 시작해봐요!',
        actionText: '새로운 시작',
        actionType: 'restart_motivation',
        timestamp: now,
        priority: 4,
      ));
    }

    // 레벨업 축하
    if (userStats.experienceToNextLevel <= 20) {
      await _sendCoachingMessage(CoachingMessage(
        id: 'levelup_soon_${now.millisecondsSinceEpoch}',
        type: 'motivation',
        title: '레벨업이 얼마 남지 않았어요! ⭐',
        message: '${userStats.experienceToNextLevel} XP만 더 얻으면 레벨 ${userStats.level + 1}이 됩니다!',
        actionText: '레벨업 도전',
        actionType: 'push_for_levelup',
        timestamp: now,
        priority: 3,
      ));
    }
  }

  // 환경적 요인 분석
  static Future<void> _analyzeEnvironmentalFactors(DateTime now) async {
    final hour = now.hour;
    final isWeekend = now.weekday >= 6;
    
    // 주말 특별 메시지
    if (isWeekend && hour >= 10 && hour <= 12) {
      await _sendCoachingMessage(CoachingMessage(
        id: 'weekend_motivation_${now.millisecondsSinceEpoch}',
        type: 'motivation',
        title: '즐거운 주말! 🌈',
        message: '주말에도 습관을 유지하는 것이 성공의 비결이에요! 오늘도 화이팅!',
        actionText: '주말 습관 체크',
        actionType: 'weekend_check',
        timestamp: now,
        priority: 2,
      ));
    }

    // 늦은 밤 수면 권장
    if (hour >= 23 || hour <= 5) {
      await _sendCoachingMessage(CoachingMessage(
        id: 'sleep_reminder_${now.millisecondsSinceEpoch}',
        type: 'tip',
        title: '수면 시간이에요 😴',
        message: '충분한 수면은 습관 형성에 매우 중요해요. 내일을 위해 푹 쉬세요!',
        actionText: '수면 모드',
        actionType: 'sleep_mode',
        timestamp: now,
        priority: 2,
      ));
    }
  }

  // 사전 예방적 추천
  static Future<void> _generateProactiveRecommendations(
    List<Habit> habits, 
    UserStats userStats, 
    DateTime now,
  ) async {
    // 클라우드 AI에서 고급 추천 가져오기
    try {
      final cloudRecommendations = await CloudAIService.generatePersonalizedMotivation();
      
      for (final recommendation in cloudRecommendations.take(1)) {
        await _sendCoachingMessage(CoachingMessage(
          id: 'ai_recommendation_${now.millisecondsSinceEpoch}',
          type: 'tip',
          title: 'AI 코치 추천',
          message: recommendation,
          actionText: '더 알아보기',
          actionType: 'learn_more',
          timestamp: now,
          priority: 2,
        ));
      }
    } catch (e) {
      // 클라우드 연결 실패 시 로컬 추천
      await _sendLocalRecommendation(habits, userStats, now);
    }
  }

  // 로컬 추천 생성
  static Future<void> _sendLocalRecommendation(
    List<Habit> habits, 
    UserStats userStats, 
    DateTime now,
  ) async {
    final tips = [
      '작은 습관부터 시작하는 것이 성공의 열쇠예요!',
      '습관을 특정 시간에 하면 더 쉽게 기억할 수 있어요.',
      '완벽하지 않아도 괜찮아요. 꾸준함이 더 중요해요!',
      '습관을 다른 활동과 연결하면 더 쉽게 할 수 있어요.',
      '진행 상황을 기록하면 동기부여가 더 잘 돼요!',
    ];

    final randomTip = tips[Random().nextInt(tips.length)];
    
    await _sendCoachingMessage(CoachingMessage(
      id: 'local_tip_${now.millisecondsSinceEpoch}',
      type: 'tip',
      title: '💡 코치 팁',
      message: randomTip,
      actionText: '적용해보기',
      actionType: 'apply_tip',
      timestamp: now,
      priority: 1,
    ));
  }

  // 코칭 메시지 전송
  static Future<void> _sendCoachingMessage(CoachingMessage message) async {
    try {
      // 메시지 히스토리에 추가
      _messageHistory.add(message);
      
      // 최근 100개 메시지만 유지
      if (_messageHistory.length > 100) {
        _messageHistory.removeAt(0);
      }

      // 스트림으로 전송
      _messageController.add(message);

      // 높은 우선순위 메시지는 알림으로 전송
      if (message.priority >= 3) {
        await _sendNotification(message);
      }

      // 음성 메시지 (선택적)
      if (message.priority >= 4) {
        await VoiceCommandService.speak(message.message);
      }

    } catch (e) {
      print('코칭 메시지 전송 오류: $e');
    }
  }

  // 알림 전송
  static Future<void> _sendNotification(CoachingMessage message) async {
    try {
      const androidDetails = AndroidNotificationDetails(
        'coaching_channel',
        'AI 코치',
        channelDescription: '개인 AI 코치 알림',
        importance: Importance.high,
        priority: Priority.high,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _notificationsPlugin!.show(
        message.hashCode,
        message.title,
        message.message,
        notificationDetails,
        payload: jsonEncode(message.toJson()),
      );
    } catch (e) {
      print('알림 전송 오류: $e');
    }
  }

  // 알림 탭 처리
  static void _onNotificationTapped(NotificationResponse response) {
    try {
      if (response.payload != null) {
        final messageData = jsonDecode(response.payload!);
        final message = CoachingMessage.fromJson(messageData);
        
        // 액션 처리
        _handleMessageAction(message);
      }
    } catch (e) {
      print('알림 탭 처리 오류: $e');
    }
  }

  // 메시지 액션 처리
  static Future<void> _handleMessageAction(CoachingMessage message) async {
    switch (message.actionType) {
      case 'complete_habit':
        final habitId = message.metadata?['habitId'];
        if (habitId != null) {
          await HabitService.toggleHabitCompletion(habitId, DateTime.now());
        }
        break;
      case 'adjust_habit':
        // 습관 조정 화면으로 이동 (UI에서 처리)
        break;
      case 'view_incomplete':
        // 미완료 습관 화면으로 이동 (UI에서 처리)
        break;
      default:
        break;
    }
  }

  // 유틸리티 메서드들
  static int _getConsecutiveFailures(Habit habit) {
    final now = DateTime.now();
    int failures = 0;

    for (int i = 0; i < 30; i++) {
      final date = now.subtract(Duration(days: i));
      if (date.isBefore(habit.createdDate)) break;
      
      if (!habit.isCompletedOn(date)) {
        failures++;
      } else {
        break;
      }
    }

    return failures;
  }

  static double _calculateTodayCompletionRate(List<Habit> habits) {
    if (habits.isEmpty) return 0.0;
    
    final completedToday = habits.where((h) => h.isCompletedToday()).length;
    return completedToday / habits.length;
  }

  // 메시지 히스토리 가져오기
  static List<CoachingMessage> getMessageHistory() {
    return List.from(_messageHistory);
  }

  // 특정 타입의 메시지만 가져오기
  static List<CoachingMessage> getMessagesByType(String type) {
    return _messageHistory.where((m) => m.type == type).toList();
  }

  // 오늘의 메시지만 가져오기
  static List<CoachingMessage> getTodayMessages() {
    final today = DateTime.now();
    return _messageHistory.where((m) => 
      m.timestamp.year == today.year &&
      m.timestamp.month == today.month &&
      m.timestamp.day == today.day
    ).toList();
  }

  // 활성 상태 확인
  static bool get isActive => _isActive;

  // 정리
  static void dispose() {
    stopCoaching();
    _messageController.close();
  }
}

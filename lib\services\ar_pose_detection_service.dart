import 'dart:async';
import 'dart:io';
import 'dart:math' as math;
import 'package:camera/camera.dart';
import 'package:google_mlkit_pose_detection/google_mlkit_pose_detection.dart';
import 'package:flutter/services.dart';

class PoseAnalysisResult {
  final String exerciseType;
  final int repetitions;
  final double accuracy;
  final List<String> feedback;
  final bool isCorrectForm;

  PoseAnalysisResult({
    required this.exerciseType,
    required this.repetitions,
    required this.accuracy,
    required this.feedback,
    required this.isCorrectForm,
  });
}

class ARPoseDetectionService {
  static PoseDetector? _poseDetector;
  static CameraController? _cameraController;
  static bool _isInitialized = false;
  static bool _isDetecting = false;

  // 운동 상태 추적
  static String _currentExercise = '';
  static int _repetitionCount = 0;
  static List<Pose> _poseHistory = [];
  static DateTime _lastDetectionTime = DateTime.now();

  // 운동별 기준 각도 및 임계값
  static const Map<String, Map<String, dynamic>> _exerciseThresholds = {
    'pushup': {
      'minElbowAngle': 70.0,
      'maxElbowAngle': 160.0,
      'minShoulderAngle': 30.0,
      'maxShoulderAngle': 90.0,
    },
    'squat': {
      'minKneeAngle': 70.0,
      'maxKneeAngle': 160.0,
      'minHipAngle': 60.0,
      'maxHipAngle': 120.0,
    },
    'plank': {
      'minBackAngle': 160.0,
      'maxBackAngle': 190.0,
      'minElbowAngle': 80.0,
      'maxElbowAngle': 100.0,
    },
  };

  // 초기화
  static Future<bool> initialize() async {
    try {
      // 카메라 권한 확인
      final cameras = await availableCameras();
      if (cameras.isEmpty) return false;

      // 전면 카메라 선택
      final frontCamera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.front,
        orElse: () => cameras.first,
      );

      // 카메라 컨트롤러 초기화
      _cameraController = CameraController(
        frontCamera,
        ResolutionPreset.medium,
        enableAudio: false,
      );

      await _cameraController!.initialize();

      // Pose Detector 초기화
      final options = PoseDetectorOptions(
        mode: PoseDetectionMode.stream,
        model: PoseDetectionModel.accurate,
      );
      _poseDetector = PoseDetector(options: options);

      _isInitialized = true;
      return true;
    } catch (e) {
      print('AR Pose Detection 초기화 오류: $e');
      return false;
    }
  }

  // 운동 세션 시작
  static Future<void> startExerciseSession(String exerciseType) async {
    if (!_isInitialized) {
      throw Exception('AR Pose Detection이 초기화되지 않았습니다.');
    }

    _currentExercise = exerciseType.toLowerCase();
    _repetitionCount = 0;
    _poseHistory.clear();
    _lastDetectionTime = DateTime.now();
    _isDetecting = true;

    // 카메라 스트림 시작
    await _cameraController!.startImageStream(_processCameraImage);
  }

  // 운동 세션 중지
  static Future<void> stopExerciseSession() async {
    _isDetecting = false;
    if (_cameraController != null &&
        _cameraController!.value.isStreamingImages) {
      await _cameraController!.stopImageStream();
    }
  }

  // 카메라 이미지 처리
  static Future<void> _processCameraImage(CameraImage image) async {
    if (!_isDetecting || _poseDetector == null) return;

    try {
      final inputImage = _convertCameraImage(image);
      if (inputImage == null) return;

      final poses = await _poseDetector!.processImage(inputImage);

      if (poses.isNotEmpty) {
        final pose = poses.first;
        _analyzePose(pose);
      }
    } catch (e) {
      print('포즈 분석 오류: $e');
    }
  }

  // CameraImage를 InputImage로 변환 (간단한 버전)
  static InputImage? _convertCameraImage(CameraImage image) {
    try {
      // 간단한 변환 로직 (실제 구현에서는 더 복잡한 변환 필요)
      return null; // 임시로 null 반환
    } catch (e) {
      print('이미지 변환 오류: $e');
      return null;
    }
  }

  // 포즈 분석
  static void _analyzePose(Pose pose) {
    _poseHistory.add(pose);

    // 최근 10개 포즈만 유지
    if (_poseHistory.length > 10) {
      _poseHistory.removeAt(0);
    }

    switch (_currentExercise) {
      case 'pushup':
        _analyzePushup(pose);
        break;
      case 'squat':
        _analyzeSquat(pose);
        break;
      case 'plank':
        _analyzePlank(pose);
        break;
    }
  }

  // 팔굽혀펴기 분석
  static void _analyzePushup(Pose pose) {
    final leftElbow = pose.landmarks[PoseLandmarkType.leftElbow];
    final rightElbow = pose.landmarks[PoseLandmarkType.rightElbow];
    final leftShoulder = pose.landmarks[PoseLandmarkType.leftShoulder];
    final rightShoulder = pose.landmarks[PoseLandmarkType.rightShoulder];
    final leftWrist = pose.landmarks[PoseLandmarkType.leftWrist];
    final rightWrist = pose.landmarks[PoseLandmarkType.rightWrist];

    if (leftElbow != null &&
        rightElbow != null &&
        leftShoulder != null &&
        rightShoulder != null &&
        leftWrist != null &&
        rightWrist != null) {
      // 팔꿈치 각도 계산
      final leftElbowAngle = _calculateAngle(
        leftShoulder.x,
        leftShoulder.y,
        leftElbow.x,
        leftElbow.y,
        leftWrist.x,
        leftWrist.y,
      );

      final rightElbowAngle = _calculateAngle(
        rightShoulder.x,
        rightShoulder.y,
        rightElbow.x,
        rightElbow.y,
        rightWrist.x,
        rightWrist.y,
      );

      final avgElbowAngle = (leftElbowAngle + rightElbowAngle) / 2;

      // 팔굽혀펴기 동작 감지
      if (_poseHistory.length >= 3) {
        final prevAngles = _poseHistory.skip(_poseHistory.length - 3).map((p) {
          final le = p.landmarks[PoseLandmarkType.leftElbow];
          final re = p.landmarks[PoseLandmarkType.rightElbow];
          final ls = p.landmarks[PoseLandmarkType.leftShoulder];
          final rs = p.landmarks[PoseLandmarkType.rightShoulder];
          final lw = p.landmarks[PoseLandmarkType.leftWrist];
          final rw = p.landmarks[PoseLandmarkType.rightWrist];

          if (le != null &&
              re != null &&
              ls != null &&
              rs != null &&
              lw != null &&
              rw != null) {
            final la = _calculateAngle(ls.x, ls.y, le.x, le.y, lw.x, lw.y);
            final ra = _calculateAngle(rs.x, rs.y, re.x, re.y, rw.x, rw.y);
            return (la + ra) / 2;
          }
          return 0.0;
        }).toList();

        // 팔굽혀펴기 한 회 완료 감지 (각도 변화 패턴)
        if (prevAngles.length == 3) {
          final isDownPhase =
              prevAngles[0] > 120 && prevAngles[1] < 90 && prevAngles[2] > 120;
          if (isDownPhase) {
            _repetitionCount++;
            HapticFeedback.lightImpact();
          }
        }
      }
    }
  }

  // 스쿼트 분석
  static void _analyzeSquat(Pose pose) {
    final leftKnee = pose.landmarks[PoseLandmarkType.leftKnee];
    final rightKnee = pose.landmarks[PoseLandmarkType.rightKnee];
    final leftHip = pose.landmarks[PoseLandmarkType.leftHip];
    final rightHip = pose.landmarks[PoseLandmarkType.rightHip];
    final leftAnkle = pose.landmarks[PoseLandmarkType.leftAnkle];
    final rightAnkle = pose.landmarks[PoseLandmarkType.rightAnkle];

    if (leftKnee != null &&
        rightKnee != null &&
        leftHip != null &&
        rightHip != null &&
        leftAnkle != null &&
        rightAnkle != null) {
      // 무릎 각도 계산
      final leftKneeAngle = _calculateAngle(
        leftHip.x,
        leftHip.y,
        leftKnee.x,
        leftKnee.y,
        leftAnkle.x,
        leftAnkle.y,
      );

      final rightKneeAngle = _calculateAngle(
        rightHip.x,
        rightHip.y,
        rightKnee.x,
        rightKnee.y,
        rightAnkle.x,
        rightAnkle.y,
      );

      final avgKneeAngle = (leftKneeAngle + rightKneeAngle) / 2;

      // 스쿼트 동작 감지 (무릎 각도 변화)
      if (_poseHistory.length >= 3) {
        final prevAngles = _poseHistory.skip(_poseHistory.length - 3).map((p) {
          final lk = p.landmarks[PoseLandmarkType.leftKnee];
          final rk = p.landmarks[PoseLandmarkType.rightKnee];
          final lh = p.landmarks[PoseLandmarkType.leftHip];
          final rh = p.landmarks[PoseLandmarkType.rightHip];
          final la = p.landmarks[PoseLandmarkType.leftAnkle];
          final ra = p.landmarks[PoseLandmarkType.rightAnkle];

          if (lk != null &&
              rk != null &&
              lh != null &&
              rh != null &&
              la != null &&
              ra != null) {
            final lka = _calculateAngle(lh.x, lh.y, lk.x, lk.y, la.x, la.y);
            final rka = _calculateAngle(rh.x, rh.y, rk.x, rk.y, ra.x, ra.y);
            return (lka + rka) / 2;
          }
          return 0.0;
        }).toList();

        // 스쿼트 한 회 완료 감지
        if (prevAngles.length == 3) {
          final isSquatCycle =
              prevAngles[0] > 150 && prevAngles[1] < 100 && prevAngles[2] > 150;
          if (isSquatCycle) {
            _repetitionCount++;
            HapticFeedback.lightImpact();
          }
        }
      }
    }
  }

  // 플랭크 분석
  static void _analyzePlank(Pose pose) {
    final leftShoulder = pose.landmarks[PoseLandmarkType.leftShoulder];
    final rightShoulder = pose.landmarks[PoseLandmarkType.rightShoulder];
    final leftHip = pose.landmarks[PoseLandmarkType.leftHip];
    final rightHip = pose.landmarks[PoseLandmarkType.rightHip];
    final leftAnkle = pose.landmarks[PoseLandmarkType.leftAnkle];
    final rightAnkle = pose.landmarks[PoseLandmarkType.rightAnkle];

    if (leftShoulder != null &&
        rightShoulder != null &&
        leftHip != null &&
        rightHip != null &&
        leftAnkle != null &&
        rightAnkle != null) {
      // 등 직선도 계산 (어깨-엉덩이-발목 각도)
      final leftBackAngle = _calculateAngle(
        leftShoulder.x,
        leftShoulder.y,
        leftHip.x,
        leftHip.y,
        leftAnkle.x,
        leftAnkle.y,
      );

      final rightBackAngle = _calculateAngle(
        rightShoulder.x,
        rightShoulder.y,
        rightHip.x,
        rightHip.y,
        rightAnkle.x,
        rightAnkle.y,
      );

      final avgBackAngle = (leftBackAngle + rightBackAngle) / 2;

      // 플랭크 자세 유지 시간 계산
      final thresholds = _exerciseThresholds['plank']!;
      final isCorrectForm = avgBackAngle >= thresholds['minBackAngle'] &&
          avgBackAngle <= thresholds['maxBackAngle'];

      if (isCorrectForm) {
        _repetitionCount++; // 플랭크는 시간 단위로 카운트
      }
    }
  }

  // 두 점 사이의 각도 계산
  static double _calculateAngle(
      double x1, double y1, double x2, double y2, double x3, double y3) {
    final angle1 = math.atan2(y1 - y2, x1 - x2);
    final angle2 = math.atan2(y3 - y2, x3 - x2);
    final angle = (angle2 - angle1) * 180 / math.pi;
    return angle.abs();
  }

  // 현재 운동 결과 가져오기
  static PoseAnalysisResult getCurrentResult() {
    final feedback = <String>[];
    double accuracy = 0.0;
    bool isCorrectForm = false;

    if (_poseHistory.isNotEmpty) {
      final latestPose = _poseHistory.last;

      switch (_currentExercise) {
        case 'pushup':
          final result = _evaluatePushupForm(latestPose);
          feedback.addAll(result['feedback']);
          accuracy = result['accuracy'];
          isCorrectForm = result['isCorrect'];
          break;
        case 'squat':
          final result = _evaluateSquatForm(latestPose);
          feedback.addAll(result['feedback']);
          accuracy = result['accuracy'];
          isCorrectForm = result['isCorrect'];
          break;
        case 'plank':
          final result = _evaluatePlankForm(latestPose);
          feedback.addAll(result['feedback']);
          accuracy = result['accuracy'];
          isCorrectForm = result['isCorrect'];
          break;
      }
    }

    return PoseAnalysisResult(
      exerciseType: _currentExercise,
      repetitions: _repetitionCount,
      accuracy: accuracy,
      feedback: feedback,
      isCorrectForm: isCorrectForm,
    );
  }

  // 팔굽혀펴기 자세 평가
  static Map<String, dynamic> _evaluatePushupForm(Pose pose) {
    final feedback = <String>[];
    double accuracy = 100.0;
    bool isCorrect = true;

    // 여기에 상세한 자세 평가 로직 구현
    feedback.add('팔꿈치를 몸에 가깝게 유지하세요');
    feedback.add('등을 곧게 펴세요');

    return {
      'feedback': feedback,
      'accuracy': accuracy,
      'isCorrect': isCorrect,
    };
  }

  // 스쿼트 자세 평가
  static Map<String, dynamic> _evaluateSquatForm(Pose pose) {
    final feedback = <String>[];
    double accuracy = 100.0;
    bool isCorrect = true;

    feedback.add('무릎이 발끝을 넘지 않도록 하세요');
    feedback.add('등을 곧게 유지하세요');

    return {
      'feedback': feedback,
      'accuracy': accuracy,
      'isCorrect': isCorrect,
    };
  }

  // 플랭크 자세 평가
  static Map<String, dynamic> _evaluatePlankForm(Pose pose) {
    final feedback = <String>[];
    double accuracy = 100.0;
    bool isCorrect = true;

    feedback.add('몸을 일직선으로 유지하세요');
    feedback.add('엉덩이가 너무 높거나 낮지 않도록 하세요');

    return {
      'feedback': feedback,
      'accuracy': accuracy,
      'isCorrect': isCorrect,
    };
  }

  // 리소스 정리
  static Future<void> dispose() async {
    _isDetecting = false;
    await _poseDetector?.close();
    await _cameraController?.dispose();
    _isInitialized = false;
  }

  // 카메라 컨트롤러 가져오기
  static CameraController? get cameraController => _cameraController;

  // 초기화 상태 확인
  static bool get isInitialized => _isInitialized;

  // 현재 반복 횟수
  static int get repetitionCount => _repetitionCount;
}

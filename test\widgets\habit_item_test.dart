import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:habit_tracker/widgets/habit_item.dart';
import 'package:habit_tracker/models/habit.dart';

void main() {
  group('HabitItem Widget Tests', () {
    late Habit testHabit;
    late Function(String) mockOnToggleCompletion;
    late Function(String) mockOnDeleteHabit;

    setUp(() {
      testHabit = Habit(
        id: 'test-id',
        name: 'Test Habit',
        createdDate: DateTime.now(),
        completedDates: {},
      );
      
      mockOnToggleCompletion = (String id) {};
      mockOnDeleteHabit = (String id) {};
    });

    Widget createTestWidget(Habit habit) {
      return MaterialApp(
        home: Scaffold(
          body: HabitItem(
            habit: habit,
            onToggleCompletion: mockOnToggleCompletion,
            onDeleteHabit: mockOnDeleteHabit,
          ),
        ),
      );
    }

    testWidgets('should display habit name', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testHabit));

      expect(find.text('Test Habit'), findsOneWidget);
    });

    testWidgets('should display checkbox for completion', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testHabit));

      expect(find.byType(Checkbox), findsOneWidget);
    });

    testWidgets('should display progress indicator', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testHabit));

      expect(find.byType(LinearProgressIndicator), findsOneWidget);
    });

    testWidgets('should display completion percentage', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testHabit));

      expect(find.text('0%'), findsOneWidget);
      expect(find.text('최근 7일간 달성률'), findsOneWidget);
    });

    testWidgets('should display popup menu button', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testHabit));

      expect(find.byType(PopupMenuButton<String>), findsOneWidget);
    });

    testWidgets('should show completed state when habit is completed today', (WidgetTester tester) async {
      final today = DateTime.now();
      final todayStr = '${today.year}-${today.month.toString().padLeft(2, '0')}-${today.day.toString().padLeft(2, '0')}';
      
      final completedHabit = Habit(
        id: 'test-id',
        name: 'Completed Habit',
        createdDate: DateTime.now(),
        completedDates: {todayStr: true},
      );

      await tester.pumpWidget(createTestWidget(completedHabit));

      final checkbox = tester.widget<Checkbox>(find.byType(Checkbox));
      expect(checkbox.value, isTrue);
    });

    testWidgets('should call onToggleCompletion when checkbox is tapped', (WidgetTester tester) async {
      bool wasToggled = false;
      String toggledId = '';

      final testWidget = MaterialApp(
        home: Scaffold(
          body: HabitItem(
            habit: testHabit,
            onToggleCompletion: (String id) {
              wasToggled = true;
              toggledId = id;
            },
            onDeleteHabit: mockOnDeleteHabit,
          ),
        ),
      );

      await tester.pumpWidget(testWidget);
      await tester.tap(find.byType(Checkbox));

      expect(wasToggled, isTrue);
      expect(toggledId, equals('test-id'));
    });

    testWidgets('should show delete confirmation dialog when delete is selected', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget(testHabit));

      // Tap the popup menu button
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();

      // Tap the delete option
      await tester.tap(find.text('삭제'));
      await tester.pumpAndSettle();

      // Verify delete confirmation dialog appears
      expect(find.text('습관 삭제'), findsOneWidget);
      expect(find.text('Test Habit 습관을 삭제하시겠습니까?\n모든 기록이 사라집니다.'), findsOneWidget);
      expect(find.text('취소'), findsOneWidget);
      expect(find.text('삭제'), findsAtLeastNWidgets(1)); // One in popup, one in dialog
    });

    testWidgets('should call onDeleteHabit when delete is confirmed', (WidgetTester tester) async {
      bool wasDeleted = false;
      String deletedId = '';

      final testWidget = MaterialApp(
        home: Scaffold(
          body: HabitItem(
            habit: testHabit,
            onToggleCompletion: mockOnToggleCompletion,
            onDeleteHabit: (String id) {
              wasDeleted = true;
              deletedId = id;
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Open popup menu and select delete
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('삭제'));
      await tester.pumpAndSettle();

      // Confirm deletion
      await tester.tap(find.text('삭제').last); // Last one is in the dialog
      await tester.pumpAndSettle();

      expect(wasDeleted, isTrue);
      expect(deletedId, equals('test-id'));
    });

    testWidgets('should cancel deletion when cancel is tapped', (WidgetTester tester) async {
      bool wasDeleted = false;

      final testWidget = MaterialApp(
        home: Scaffold(
          body: HabitItem(
            habit: testHabit,
            onToggleCompletion: mockOnToggleCompletion,
            onDeleteHabit: (String id) {
              wasDeleted = true;
            },
          ),
        ),
      );

      await tester.pumpWidget(testWidget);

      // Open popup menu and select delete
      await tester.tap(find.byType(PopupMenuButton<String>));
      await tester.pumpAndSettle();
      await tester.tap(find.text('삭제'));
      await tester.pumpAndSettle();

      // Cancel deletion
      await tester.tap(find.text('취소'));
      await tester.pumpAndSettle();

      expect(wasDeleted, isFalse);
      expect(find.text('습관 삭제'), findsNothing); // Dialog should be closed
    });

    testWidgets('should display correct completion percentage', (WidgetTester tester) async {
      final now = DateTime.now();
      final completedDates = <String, bool>{};
      
      // Add 3 out of 7 days as completed (approximately 43%)
      for (int i = 0; i < 3; i++) {
        final date = now.subtract(Duration(days: i));
        final dateStr = '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
        completedDates[dateStr] = true;
      }

      final habitWithProgress = Habit(
        id: 'test-id',
        name: 'Progress Habit',
        createdDate: DateTime.now(),
        completedDates: completedDates,
      );

      await tester.pumpWidget(createTestWidget(habitWithProgress));

      expect(find.text('43%'), findsOneWidget);
    });
  });
}

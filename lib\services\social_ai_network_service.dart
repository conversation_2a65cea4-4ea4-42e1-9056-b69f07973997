import 'dart:async';
import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';

// 소셜 사용자 모델
class SocialUser {
  final String id;
  final String username;
  final String avatar;
  final int level;
  final int totalPoints;
  final List<String> achievements;
  final DateTime lastActive;
  final bool isOnline;

  SocialUser({
    required this.id,
    required this.username,
    required this.avatar,
    required this.level,
    required this.totalPoints,
    required this.achievements,
    required this.lastActive,
    required this.isOnline,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'username': username,
    'avatar': avatar,
    'level': level,
    'totalPoints': totalPoints,
    'achievements': achievements,
    'lastActive': lastActive.toIso8601String(),
    'isOnline': isOnline,
  };

  factory SocialUser.fromJson(Map<String, dynamic> json) => SocialUser(
    id: json['id'],
    username: json['username'],
    avatar: json['avatar'],
    level: json['level'],
    totalPoints: json['totalPoints'],
    achievements: List<String>.from(json['achievements']),
    lastActive: DateTime.parse(json['lastActive']),
    isOnline: json['isOnline'],
  );
}

// AI 챌린지 모델
class AIChallenge {
  final String id;
  final String title;
  final String description;
  final String category;
  final int duration; // 일수
  final int targetValue;
  final String unit;
  final List<String> participants;
  final DateTime startDate;
  final DateTime endDate;
  final Map<String, int> leaderboard;
  final List<String> rewards;
  final double aiConfidence;

  AIChallenge({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.duration,
    required this.targetValue,
    required this.unit,
    required this.participants,
    required this.startDate,
    required this.endDate,
    required this.leaderboard,
    required this.rewards,
    required this.aiConfidence,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'title': title,
    'description': description,
    'category': category,
    'duration': duration,
    'targetValue': targetValue,
    'unit': unit,
    'participants': participants,
    'startDate': startDate.toIso8601String(),
    'endDate': endDate.toIso8601String(),
    'leaderboard': leaderboard,
    'rewards': rewards,
    'aiConfidence': aiConfidence,
  };

  factory AIChallenge.fromJson(Map<String, dynamic> json) => AIChallenge(
    id: json['id'],
    title: json['title'],
    description: json['description'],
    category: json['category'],
    duration: json['duration'],
    targetValue: json['targetValue'],
    unit: json['unit'],
    participants: List<String>.from(json['participants']),
    startDate: DateTime.parse(json['startDate']),
    endDate: DateTime.parse(json['endDate']),
    leaderboard: Map<String, int>.from(json['leaderboard']),
    rewards: List<String>.from(json['rewards']),
    aiConfidence: json['aiConfidence'],
  );
}

// 소셜 AI 네트워크 서비스
class SocialAINetworkService {
  static bool _isInitialized = false;
  static String _currentUserId = '';
  static List<SocialUser> _friends = [];
  static List<AIChallenge> _activeChallenges = [];
  static Timer? _socialSyncTimer;
  
  // 실시간 소셜 데이터 스트림
  static final StreamController<Map<String, dynamic>> _socialStreamController = 
      StreamController<Map<String, dynamic>>.broadcast();
  static Stream<Map<String, dynamic>> get socialStream => _socialStreamController.stream;

  // 초기화
  static Future<bool> initialize(String userId) async {
    try {
      _currentUserId = userId;
      
      // 소셜 네트워크 연결
      await _connectToSocialNetwork();
      
      // 친구 목록 로드
      await _loadFriends();
      
      // AI 챌린지 로드
      await _loadAIChallenges();
      
      // 실시간 동기화 시작
      _startRealTimeSync();
      
      _isInitialized = true;
      return true;
    } catch (e) {
      print('소셜 AI 네트워크 초기화 오류: $e');
      return false;
    }
  }

  // 소셜 네트워크 연결
  static Future<void> _connectToSocialNetwork() async {
    // 실제로는 WebSocket 또는 Firebase 연결
    print('소셜 네트워크에 연결 중...');
    await Future.delayed(const Duration(seconds: 1));
    print('소셜 네트워크 연결 완료');
  }

  // 친구 목록 로드
  static Future<void> _loadFriends() async {
    // 모의 친구 데이터 생성
    _friends = [
      SocialUser(
        id: 'friend1',
        username: '운동왕김철수',
        avatar: '💪',
        level: 15,
        totalPoints: 2500,
        achievements: ['연속 30일', '만보 달성', '운동 마스터'],
        lastActive: DateTime.now().subtract(const Duration(minutes: 5)),
        isOnline: true,
      ),
      SocialUser(
        id: 'friend2',
        username: '명상마스터이영희',
        avatar: '🧘‍♀️',
        level: 12,
        totalPoints: 1800,
        achievements: ['명상 달인', '스트레스 제로', '마음챙김'],
        lastActive: DateTime.now().subtract(const Duration(hours: 2)),
        isOnline: false,
      ),
      SocialUser(
        id: 'friend3',
        username: '독서광박민수',
        avatar: '📚',
        level: 18,
        totalPoints: 3200,
        achievements: ['독서왕', '지식 수집가', '학습 마니아'],
        lastActive: DateTime.now().subtract(const Duration(minutes: 30)),
        isOnline: true,
      ),
    ];
  }

  // AI 챌린지 로드
  static Future<void> _loadAIChallenges() async {
    _activeChallenges = await _generateAIChallenges();
  }

  // AI 기반 챌린지 생성
  static Future<List<AIChallenge>> _generateAIChallenges() async {
    final challenges = <AIChallenge>[];
    final random = Random();
    
    // 운동 챌린지
    challenges.add(AIChallenge(
      id: 'challenge_fitness_${DateTime.now().millisecondsSinceEpoch}',
      title: 'AI 추천: 7일 만보 챌린지',
      description: 'AI가 분석한 당신의 활동 패턴을 바탕으로 7일간 매일 만보 걷기에 도전하세요!',
      category: 'fitness',
      duration: 7,
      targetValue: 10000,
      unit: '걸음',
      participants: ['friend1', 'friend3', _currentUserId],
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 7)),
      leaderboard: {
        'friend1': 8500,
        'friend3': 7200,
        _currentUserId: 6800,
      },
      rewards: ['만보 배지', '100 포인트', '건강 마스터 칭호'],
      aiConfidence: 0.92,
    ));

    // 명상 챌린지
    challenges.add(AIChallenge(
      id: 'challenge_mindfulness_${DateTime.now().millisecondsSinceEpoch}',
      title: 'AI 추천: 마음챙김 21일 챌린지',
      description: 'AI가 당신의 스트레스 패턴을 분석하여 맞춤형 명상 챌린지를 제안합니다.',
      category: 'mindfulness',
      duration: 21,
      targetValue: 10,
      unit: '분',
      participants: ['friend2', _currentUserId],
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 21)),
      leaderboard: {
        'friend2': 15,
        _currentUserId: 8,
      },
      rewards: ['명상 마스터 배지', '200 포인트', '평온함 달성'],
      aiConfidence: 0.88,
    ));

    // 학습 챌린지
    challenges.add(AIChallenge(
      id: 'challenge_learning_${DateTime.now().millisecondsSinceEpoch}',
      title: 'AI 추천: 독서 마라톤',
      description: 'AI가 당신의 독서 선호도를 분석하여 최적의 독서 목표를 설정했습니다.',
      category: 'learning',
      duration: 30,
      targetValue: 30,
      unit: '페이지',
      participants: ['friend3', _currentUserId],
      startDate: DateTime.now(),
      endDate: DateTime.now().add(const Duration(days: 30)),
      leaderboard: {
        'friend3': 45,
        _currentUserId: 25,
      },
      rewards: ['독서왕 배지', '300 포인트', '지식의 탑'],
      aiConfidence: 0.95,
    ));

    return challenges;
  }

  // 실시간 동기화 시작
  static void _startRealTimeSync() {
    _socialSyncTimer = Timer.periodic(const Duration(seconds: 15), (timer) async {
      await _syncSocialData();
    });
  }

  // 소셜 데이터 동기화
  static Future<void> _syncSocialData() async {
    try {
      // 친구들의 실시간 활동 업데이트
      await _updateFriendsActivity();
      
      // 챌린지 진행 상황 업데이트
      await _updateChallengeProgress();
      
      // 실시간 알림 생성
      await _generateRealTimeNotifications();
      
    } catch (e) {
      print('소셜 데이터 동기화 오류: $e');
    }
  }

  // 친구들의 활동 업데이트
  static Future<void> _updateFriendsActivity() async {
    final random = Random();
    
    for (final friend in _friends) {
      // 랜덤하게 친구들의 활동 시뮬레이션
      if (random.nextDouble() < 0.3) { // 30% 확률로 활동
        final activityTypes = ['운동 완료', '명상 완료', '독서 완료', '물 마시기 완료'];
        final activity = activityTypes[random.nextInt(activityTypes.length)];
        
        _socialStreamController.add({
          'type': 'friend_activity',
          'userId': friend.id,
          'username': friend.username,
          'activity': activity,
          'timestamp': DateTime.now(),
        });
      }
    }
  }

  // 챌린지 진행 상황 업데이트
  static Future<void> _updateChallengeProgress() async {
    final random = Random();
    
    for (final challenge in _activeChallenges) {
      // 참가자들의 진행 상황 업데이트
      for (final participantId in challenge.participants) {
        if (participantId != _currentUserId && random.nextDouble() < 0.2) {
          final progress = random.nextInt(100) + challenge.leaderboard[participantId]!;
          challenge.leaderboard[participantId] = progress;
          
          _socialStreamController.add({
            'type': 'challenge_update',
            'challengeId': challenge.id,
            'participantId': participantId,
            'progress': progress,
            'timestamp': DateTime.now(),
          });
        }
      }
    }
  }

  // 실시간 알림 생성
  static Future<void> _generateRealTimeNotifications() async {
    final random = Random();
    
    // AI 기반 동기부여 메시지
    if (random.nextDouble() < 0.1) { // 10% 확률
      final motivationMessages = [
        '친구 운동왕김철수가 방금 운동을 완료했어요! 당신도 도전해보세요! 💪',
        'AI 분석: 지금이 명상하기 좋은 시간입니다. 친구 명상마스터이영희와 함께해보세요! 🧘‍♀️',
        '독서광박민수가 오늘 목표를 달성했습니다! 당신도 독서 시간을 가져보세요! 📚',
        'AI 추천: 현재 날씨가 산책하기 좋습니다. 친구들과 함께 걸어보세요! 🚶‍♂️',
      ];
      
      final message = motivationMessages[random.nextInt(motivationMessages.length)];
      
      _socialStreamController.add({
        'type': 'ai_motivation',
        'message': message,
        'timestamp': DateTime.now(),
      });
    }
    
    // 챌린지 순위 변동 알림
    if (random.nextDouble() < 0.05) { // 5% 확률
      _socialStreamController.add({
        'type': 'ranking_change',
        'message': '만보 챌린지에서 순위가 변동되었습니다! 현재 2위입니다! 🏃‍♂️',
        'timestamp': DateTime.now(),
      });
    }
  }

  // 친구 추가
  static Future<bool> addFriend(String friendId) async {
    try {
      // 실제로는 서버 API 호출
      await Future.delayed(const Duration(seconds: 1));
      
      // 모의 친구 데이터 생성
      final newFriend = SocialUser(
        id: friendId,
        username: '새친구$friendId',
        avatar: '😊',
        level: Random().nextInt(20) + 1,
        totalPoints: Random().nextInt(5000),
        achievements: ['신규 가입'],
        lastActive: DateTime.now(),
        isOnline: true,
      );
      
      _friends.add(newFriend);
      
      _socialStreamController.add({
        'type': 'friend_added',
        'friend': newFriend.toJson(),
        'timestamp': DateTime.now(),
      });
      
      return true;
    } catch (e) {
      print('친구 추가 오류: $e');
      return false;
    }
  }

  // 챌린지 참가
  static Future<bool> joinChallenge(String challengeId) async {
    try {
      final challenge = _activeChallenges.firstWhere((c) => c.id == challengeId);
      
      if (!challenge.participants.contains(_currentUserId)) {
        challenge.participants.add(_currentUserId);
        challenge.leaderboard[_currentUserId] = 0;
        
        _socialStreamController.add({
          'type': 'challenge_joined',
          'challengeId': challengeId,
          'challengeTitle': challenge.title,
          'timestamp': DateTime.now(),
        });
        
        return true;
      }
      
      return false;
    } catch (e) {
      print('챌린지 참가 오류: $e');
      return false;
    }
  }

  // 챌린지 진행 상황 업데이트
  static Future<void> updateChallengeProgress(String challengeId, int progress) async {
    try {
      final challenge = _activeChallenges.firstWhere((c) => c.id == challengeId);
      challenge.leaderboard[_currentUserId] = progress;
      
      _socialStreamController.add({
        'type': 'my_progress_update',
        'challengeId': challengeId,
        'progress': progress,
        'timestamp': DateTime.now(),
      });
      
    } catch (e) {
      print('챌린지 진행 상황 업데이트 오류: $e');
    }
  }

  // AI 기반 개인화된 챌린지 생성
  static Future<AIChallenge> createPersonalizedChallenge(UserStats userStats, List<Habit> habits) async {
    // 사용자의 습관 패턴 분석
    final mostActiveCategory = _analyzeMostActiveCategory(habits);
    final averageCompletion = _calculateAverageCompletion(habits);
    final preferredDifficulty = _calculatePreferredDifficulty(userStats);
    
    // AI 기반 챌린지 생성
    final challengeTemplates = {
      'fitness': {
        'title': 'AI 맞춤 피트니스 챌린지',
        'description': 'AI가 당신의 운동 패턴을 분석하여 최적의 운동 목표를 설정했습니다.',
        'targetValue': (averageCompletion * 1.2).round(),
        'unit': '회',
      },
      'mindfulness': {
        'title': 'AI 맞춤 마음챙김 챌린지',
        'description': 'AI가 당신의 스트레스 패턴을 분석하여 맞춤형 명상 프로그램을 제안합니다.',
        'targetValue': (averageCompletion * 1.1).round(),
        'unit': '분',
      },
      'learning': {
        'title': 'AI 맞춤 학습 챌린지',
        'description': 'AI가 당신의 학습 선호도를 분석하여 최적의 학습 목표를 설정했습니다.',
        'targetValue': (averageCompletion * 1.3).round(),
        'unit': '페이지',
      },
    };
    
    final template = challengeTemplates[mostActiveCategory] ?? challengeTemplates['fitness']!;
    
    return AIChallenge(
      id: 'personal_challenge_${DateTime.now().millisecondsSinceEpoch}',
      title: template['title'] as String,
      description: template['description'] as String,
      category: mostActiveCategory,
      duration: preferredDifficulty == 'easy' ? 7 : preferredDifficulty == 'medium' ? 14 : 21,
      targetValue: template['targetValue'] as int,
      unit: template['unit'] as String,
      participants: [_currentUserId],
      startDate: DateTime.now(),
      endDate: DateTime.now().add(Duration(days: preferredDifficulty == 'easy' ? 7 : preferredDifficulty == 'medium' ? 14 : 21)),
      leaderboard: {_currentUserId: 0},
      rewards: ['개인 맞춤 배지', '500 포인트', 'AI 추천 달성'],
      aiConfidence: 0.98,
    );
  }

  // 보조 메서드들
  static String _analyzeMostActiveCategory(List<Habit> habits) {
    final categoryCount = <String, int>{};
    for (final habit in habits) {
      categoryCount[habit.categoryId] = (categoryCount[habit.categoryId] ?? 0) + 1;
    }
    
    if (categoryCount.isEmpty) return 'fitness';
    
    return categoryCount.entries.reduce((a, b) => a.value > b.value ? a : b).key;
  }

  static double _calculateAverageCompletion(List<Habit> habits) {
    if (habits.isEmpty) return 10.0;
    
    double total = 0;
    for (final habit in habits) {
      total += habit.getWeeklyCompletionRate() * 100;
    }
    
    return total / habits.length;
  }

  static String _calculatePreferredDifficulty(UserStats userStats) {
    if (userStats.level < 5) return 'easy';
    if (userStats.level < 15) return 'medium';
    return 'hard';
  }

  // Getter 메서드들
  static List<SocialUser> get friends => _friends;
  static List<AIChallenge> get activeChallenges => _activeChallenges;
  static bool get isInitialized => _isInitialized;

  // 정리
  static void dispose() {
    _socialSyncTimer?.cancel();
    _socialStreamController.close();
    _isInitialized = false;
  }
}

import 'dart:async';
import 'dart:convert';
import 'package:health/health.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../models/habit.dart';
import '../models/user_stats.dart';
import 'habit_service.dart';
import 'user_stats_service.dart';

class WearableData {
  final int steps;
  final double heartRate;
  final int activeMinutes;
  final double caloriesBurned;
  final DateTime timestamp;
  final String deviceType;

  WearableData({
    required this.steps,
    required this.heartRate,
    required this.activeMinutes,
    required this.caloriesBurned,
    required this.timestamp,
    required this.deviceType,
  });

  Map<String, dynamic> toJson() {
    return {
      'steps': steps,
      'heartRate': heartRate,
      'activeMinutes': activeMinutes,
      'caloriesBurned': caloriesBurned,
      'timestamp': timestamp.toIso8601String(),
      'deviceType': deviceType,
    };
  }

  factory WearableData.fromJson(Map<String, dynamic> json) {
    return WearableData(
      steps: json['steps'] ?? 0,
      heartRate: json['heartRate']?.toDouble() ?? 0.0,
      activeMinutes: json['activeMinutes'] ?? 0,
      caloriesBurned: json['caloriesBurned']?.toDouble() ?? 0.0,
      timestamp: DateTime.parse(json['timestamp']),
      deviceType: json['deviceType'] ?? 'unknown',
    );
  }
}

class WearableSyncService {
  static Health? _health;
  static bool _isInitialized = false;
  static StreamSubscription<List<HealthDataPoint>>? _healthStreamSubscription;
  static Timer? _syncTimer;
  
  // 지원되는 헬스 데이터 타입
  static const List<HealthDataType> _healthDataTypes = [
    HealthDataType.STEPS,
    HealthDataType.HEART_RATE,
    HealthDataType.ACTIVE_ENERGY_BURNED,
    HealthDataType.WORKOUT,
    HealthDataType.SLEEP_IN_BED,
    HealthDataType.WATER,
  ];

  // 초기화
  static Future<bool> initialize() async {
    try {
      _health = Health();
      
      // 헬스 데이터 권한 요청
      final permissions = _healthDataTypes.map((type) => 
        HealthDataAccess.READ_WRITE).toList();
      
      final authorized = await _health!.requestAuthorization(
        _healthDataTypes,
        permissions: permissions,
      );

      if (authorized) {
        _isInitialized = true;
        await _startRealTimeSync();
        return true;
      }
      
      return false;
    } catch (e) {
      print('웨어러블 동기화 초기화 오류: $e');
      return false;
    }
  }

  // 실시간 동기화 시작
  static Future<void> _startRealTimeSync() async {
    if (!_isInitialized) return;

    // 5분마다 동기화
    _syncTimer = Timer.periodic(const Duration(minutes: 5), (timer) {
      _syncHealthData();
    });

    // 실시간 헬스 데이터 스트림 구독
    try {
      _healthStreamSubscription = _health!.healthDataStream.listen(
        (List<HealthDataPoint> healthDataList) {
          _processHealthDataPoints(healthDataList);
        },
        onError: (error) {
          print('헬스 데이터 스트림 오류: $error');
        },
      );
    } catch (e) {
      print('헬스 데이터 스트림 구독 오류: $e');
    }
  }

  // 헬스 데이터 동기화
  static Future<void> _syncHealthData() async {
    if (!_isInitialized) return;

    try {
      final now = DateTime.now();
      final yesterday = now.subtract(const Duration(days: 1));

      // 어제부터 오늘까지의 데이터 가져오기
      final healthData = await _health!.getHealthDataFromTypes(
        yesterday,
        now,
        _healthDataTypes,
      );

      await _processHealthDataPoints(healthData);
    } catch (e) {
      print('헬스 데이터 동기화 오류: $e');
    }
  }

  // 헬스 데이터 포인트 처리
  static Future<void> _processHealthDataPoints(List<HealthDataPoint> dataPoints) async {
    try {
      final wearableData = await _convertToWearableData(dataPoints);
      await _updateHabitsBasedOnWearableData(wearableData);
      await _sendDataToCloud(wearableData);
    } catch (e) {
      print('헬스 데이터 처리 오류: $e');
    }
  }

  // 헬스 데이터를 웨어러블 데이터로 변환
  static Future<WearableData> _convertToWearableData(List<HealthDataPoint> dataPoints) async {
    int steps = 0;
    double heartRate = 0.0;
    int activeMinutes = 0;
    double caloriesBurned = 0.0;
    int heartRateCount = 0;

    for (final dataPoint in dataPoints) {
      switch (dataPoint.type) {
        case HealthDataType.STEPS:
          steps += (dataPoint.value as num).toInt();
          break;
        case HealthDataType.HEART_RATE:
          heartRate += (dataPoint.value as num).toDouble();
          heartRateCount++;
          break;
        case HealthDataType.ACTIVE_ENERGY_BURNED:
          caloriesBurned += (dataPoint.value as num).toDouble();
          break;
        case HealthDataType.WORKOUT:
          // 운동 시간을 활동 시간으로 계산
          final duration = dataPoint.dateTo.difference(dataPoint.dateFrom);
          activeMinutes += duration.inMinutes;
          break;
        default:
          break;
      }
    }

    // 평균 심박수 계산
    if (heartRateCount > 0) {
      heartRate = heartRate / heartRateCount;
    }

    return WearableData(
      steps: steps,
      heartRate: heartRate,
      activeMinutes: activeMinutes,
      caloriesBurned: caloriesBurned,
      timestamp: DateTime.now(),
      deviceType: await _getDeviceType(),
    );
  }

  // 웨어러블 데이터를 기반으로 습관 업데이트
  static Future<void> _updateHabitsBasedOnWearableData(WearableData data) async {
    try {
      final habits = await HabitService.loadHabits();
      
      for (final habit in habits) {
        bool shouldComplete = false;
        
        // 습관 이름을 기반으로 자동 완료 판단
        final habitName = habit.name.toLowerCase();
        
        if (habitName.contains('걷기') || habitName.contains('산책')) {
          // 걸음 수 기반 (예: 10,000보 이상)
          shouldComplete = data.steps >= 10000;
        } else if (habitName.contains('운동') || habitName.contains('헬스')) {
          // 활동 시간 기반 (예: 30분 이상)
          shouldComplete = data.activeMinutes >= 30;
        } else if (habitName.contains('칼로리') || habitName.contains('다이어트')) {
          // 칼로리 소모 기반 (예: 300칼로리 이상)
          shouldComplete = data.caloriesBurned >= 300;
        } else if (habitName.contains('심박') || habitName.contains('유산소')) {
          // 심박수 기반 (예: 평균 심박수 100 이상)
          shouldComplete = data.heartRate >= 100;
        }
        
        if (shouldComplete && !habit.isCompletedToday()) {
          await HabitService.toggleHabitCompletion(habit.id, DateTime.now());
          
          // 웨어러블 기반 자동 완료 알림
          await _sendWearableCompletionNotification(habit.name, data);
        }
      }
    } catch (e) {
      print('웨어러블 기반 습관 업데이트 오류: $e');
    }
  }

  // 클라우드로 데이터 전송 (향후 구현)
  static Future<void> _sendDataToCloud(WearableData data) async {
    try {
      // 클라우드 API 호출 로직
      print('웨어러블 데이터 클라우드 전송: ${data.toJson()}');
    } catch (e) {
      print('클라우드 데이터 전송 오류: $e');
    }
  }

  // 웨어러블 완료 알림
  static Future<void> _sendWearableCompletionNotification(String habitName, WearableData data) async {
    try {
      // 로컬 알림 또는 웨어러블 알림 전송
      print('웨어러블 자동 완료: $habitName (걸음수: ${data.steps}, 활동시간: ${data.activeMinutes}분)');
    } catch (e) {
      print('웨어러블 알림 전송 오류: $e');
    }
  }

  // 디바이스 타입 확인
  static Future<String> _getDeviceType() async {
    try {
      // 실제로는 연결된 웨어러블 디바이스 정보를 가져와야 함
      return 'Apple Watch'; // 또는 'Galaxy Watch', 'Fitbit' 등
    } catch (e) {
      return 'Unknown';
    }
  }

  // 수동 동기화
  static Future<WearableData?> manualSync() async {
    if (!_isInitialized) {
      throw Exception('웨어러블 동기화가 초기화되지 않았습니다.');
    }

    try {
      await _syncHealthData();
      
      // 최신 데이터 반환
      final now = DateTime.now();
      final oneHourAgo = now.subtract(const Duration(hours: 1));
      
      final healthData = await _health!.getHealthDataFromTypes(
        oneHourAgo,
        now,
        _healthDataTypes,
      );

      return await _convertToWearableData(healthData);
    } catch (e) {
      print('수동 동기화 오류: $e');
      return null;
    }
  }

  // 특정 운동 데이터 기록
  static Future<bool> recordWorkout({
    required String workoutType,
    required DateTime startTime,
    required DateTime endTime,
    required double caloriesBurned,
  }) async {
    if (!_isInitialized) return false;

    try {
      final workoutData = HealthDataPoint(
        value: caloriesBurned,
        type: HealthDataType.WORKOUT,
        unit: HealthDataUnit.KILOCALORIE,
        dateFrom: startTime,
        dateTo: endTime,
        sourcePlatform: HealthPlatformType.appleHealth,
        sourceDeviceId: 'habit_tracker_app',
        sourceId: 'com.habittracker.habit_tracker',
        sourceName: 'Habit Tracker',
      );

      return await _health!.writeHealthData(
        caloriesBurned,
        HealthDataType.ACTIVE_ENERGY_BURNED,
        startTime,
        endTime,
      );
    } catch (e) {
      print('운동 데이터 기록 오류: $e');
      return false;
    }
  }

  // 물 섭취량 기록
  static Future<bool> recordWaterIntake(double liters) async {
    if (!_isInitialized) return false;

    try {
      final now = DateTime.now();
      return await _health!.writeHealthData(
        liters,
        HealthDataType.WATER,
        now.subtract(const Duration(minutes: 1)),
        now,
      );
    } catch (e) {
      print('물 섭취량 기록 오류: $e');
      return false;
    }
  }

  // 오늘의 웨어러블 요약 데이터
  static Future<Map<String, dynamic>> getTodaySummary() async {
    if (!_isInitialized) {
      return {
        'steps': 0,
        'heartRate': 0.0,
        'activeMinutes': 0,
        'caloriesBurned': 0.0,
        'isConnected': false,
      };
    }

    try {
      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      
      final healthData = await _health!.getHealthDataFromTypes(
        startOfDay,
        now,
        _healthDataTypes,
      );

      final wearableData = await _convertToWearableData(healthData);
      
      return {
        'steps': wearableData.steps,
        'heartRate': wearableData.heartRate,
        'activeMinutes': wearableData.activeMinutes,
        'caloriesBurned': wearableData.caloriesBurned,
        'isConnected': true,
        'deviceType': wearableData.deviceType,
        'lastSync': wearableData.timestamp,
      };
    } catch (e) {
      print('오늘 요약 데이터 가져오기 오류: $e');
      return {
        'steps': 0,
        'heartRate': 0.0,
        'activeMinutes': 0,
        'caloriesBurned': 0.0,
        'isConnected': false,
      };
    }
  }

  // 연결 상태 확인
  static bool get isConnected => _isInitialized;

  // 지원되는 웨어러블 디바이스 목록
  static List<String> getSupportedDevices() {
    return [
      'Apple Watch',
      'Galaxy Watch',
      'Fitbit',
      'Garmin',
      'Wear OS',
      'Huawei Watch',
    ];
  }

  // 정리
  static Future<void> dispose() async {
    _syncTimer?.cancel();
    await _healthStreamSubscription?.cancel();
    _isInitialized = false;
  }
}

import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/habit.dart';

class HabitService {
  static const String _habitsKey = 'habits';

  // 습관 목록 로드
  static Future<List<Habit>> loadHabits() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final habitsJson = prefs.getString(_habitsKey);

      if (habitsJson == null) {
        return [];
      }

      final List<dynamic> habitsList = json.decode(habitsJson);
      return habitsList.map((json) => Habit.fromJson(json)).toList();
    } catch (e) {
      // 로그 출력 (개발 환경에서만)
      print('습관 로드 중 오류 발생: $e');
      // 프로덕션에서는 에러 리포팅 서비스로 전송할 수 있음
      return [];
    }
  }

  // 습관 목록 저장
  static Future<bool> saveHabits(List<Habit> habits) async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final habitsJson =
          json.encode(habits.map((habit) => habit.toJson()).toList());
      return await prefs.setString(_habitsK<PERSON>, habitsJson);
    } catch (e) {
      print('습관 저장 중 오류 발생: $e');
      return false;
    }
  }

  // 새로운 습관 추가
  static Future<bool> addHabit(String name,
      {String? categoryId, String? description}) async {
    try {
      final habits = await loadHabits();
      final newHabit =
          Habit.create(name, categoryId: categoryId, description: description);
      habits.add(newHabit);
      return await saveHabits(habits);
    } catch (e) {
      print('습관 추가 중 오류 발생: $e');
      return false;
    }
  }

  // 습관 완료 상태 토글
  static Future<bool> toggleHabitCompletion(
      String habitId, DateTime date) async {
    try {
      final habits = await loadHabits();
      final habitIndex = habits.indexWhere((habit) => habit.id == habitId);

      if (habitIndex == -1) {
        return false;
      }

      habits[habitIndex] = habits[habitIndex].toggleCompletion(date);
      return await saveHabits(habits);
    } catch (e) {
      print('습관 완료 상태 변경 중 오류 발생: $e');
      return false;
    }
  }

  // 습관 삭제
  static Future<bool> deleteHabit(String habitId) async {
    try {
      final habits = await loadHabits();
      habits.removeWhere((habit) => habit.id == habitId);
      return await saveHabits(habits);
    } catch (e) {
      print('습관 삭제 중 오류 발생: $e');
      return false;
    }
  }

  // 전체 달성률 계산
  static Future<double> calculateOverallCompletionRate() async {
    try {
      final habits = await loadHabits();
      if (habits.isEmpty) return 0.0;

      double totalRate = 0.0;
      for (final habit in habits) {
        totalRate += habit.getWeeklyCompletionRate();
      }

      return totalRate / habits.length;
    } catch (e) {
      print('전체 달성률 계산 중 오류 발생: $e');
      return 0.0;
    }
  }
}

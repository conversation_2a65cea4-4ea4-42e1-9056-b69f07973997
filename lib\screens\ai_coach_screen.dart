import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../services/personal_ai_coach_service.dart';
import '../services/habit_service.dart';
import '../services/user_stats_service.dart';

class AICoachScreen extends StatefulWidget {
  const AICoachScreen({super.key});

  @override
  State<AICoachScreen> createState() => _AICoachScreenState();
}

class _AICoachScreenState extends State<AICoachScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<CoachingMessage> _todayMessages = [];
  List<CoachingMessage> _allMessages = [];
  bool _isCoachingActive = false;
  late AnimationController _pulseController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _loadCoachingData();
    _setupCoachingListener();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _loadCoachingData() {
    setState(() {
      _todayMessages = PersonalAICoachService.getTodayMessages();
      _allMessages = PersonalAICoachService.getMessageHistory();
      _isCoachingActive = PersonalAICoachService.isActive;
    });

    if (_isCoachingActive) {
      _pulseController.repeat();
    }
  }

  void _setupCoachingListener() {
    PersonalAICoachService.messageStream.listen((message) {
      if (mounted) {
        setState(() {
          _todayMessages = PersonalAICoachService.getTodayMessages();
          _allMessages = PersonalAICoachService.getMessageHistory();
        });
      }
    });
  }

  Future<void> _toggleCoaching() async {
    if (_isCoachingActive) {
      PersonalAICoachService.stopCoaching();
      _pulseController.stop();
    } else {
      await PersonalAICoachService.startCoaching();
      _pulseController.repeat();
    }
    
    setState(() {
      _isCoachingActive = PersonalAICoachService.isActive;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text(
          '🧠 AI 코치',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.today), text: '오늘'),
            Tab(icon: Icon(Icons.history), text: '히스토리'),
            Tab(icon: Icon(Icons.settings), text: '설정'),
          ],
        ),
        actions: [
          AnimatedBuilder(
            animation: _pulseController,
            builder: (context, child) {
              return Transform.scale(
                scale: 1.0 + (_pulseController.value * 0.1),
                child: IconButton(
                  icon: Icon(
                    _isCoachingActive ? Icons.pause : Icons.play_arrow,
                    color: _isCoachingActive ? Colors.red : Colors.green,
                  ),
                  onPressed: _toggleCoaching,
                  tooltip: _isCoachingActive ? 'AI 코치 일시정지' : 'AI 코치 시작',
                ),
              );
            },
          ),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildTodayTab(),
          _buildHistoryTab(),
          _buildSettingsTab(),
        ],
      ),
    );
  }

  Widget _buildTodayTab() {
    return Column(
      children: [
        // 코치 상태 카드
        Container(
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: _isCoachingActive 
                ? [Colors.green.shade400, Colors.green.shade600]
                : [Colors.grey.shade400, Colors.grey.shade600],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Row(
            children: [
              AnimatedBuilder(
                animation: _pulseController,
                builder: (context, child) {
                  return Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(50),
                    ),
                    child: Icon(
                      _isCoachingActive ? Icons.psychology : Icons.psychology_outlined,
                      size: 32,
                      color: Colors.white,
                    ),
                  );
                },
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      _isCoachingActive ? 'AI 코치 활성화' : 'AI 코치 비활성화',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      _isCoachingActive 
                        ? '24/7 맞춤형 코칭이 진행 중입니다'
                        : '코칭을 시작하려면 재생 버튼을 누르세요',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ).animate().fadeIn(duration: 600.ms).slideY(begin: -0.3, end: 0),

        // 오늘의 메시지
        Expanded(
          child: _todayMessages.isEmpty
            ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.chat_bubble_outline,
                      size: 64,
                      color: Colors.grey[400],
                    ),
                    const SizedBox(height: 16),
                    Text(
                      '오늘의 코칭 메시지가 없습니다',
                      style: TextStyle(
                        fontSize: 18,
                        color: Colors.grey[600],
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      _isCoachingActive 
                        ? '곧 AI 코치가 메시지를 보낼 예정입니다'
                        : 'AI 코치를 활성화해보세요',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey[500],
                      ),
                    ),
                  ],
                ),
              )
            : ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _todayMessages.length,
                itemBuilder: (context, index) {
                  final message = _todayMessages[index];
                  return _buildMessageCard(message, index);
                },
              ),
        ),
      ],
    );
  }

  Widget _buildHistoryTab() {
    final messagesByType = <String, List<CoachingMessage>>{};
    for (final message in _allMessages) {
      messagesByType.putIfAbsent(message.type, () => []).add(message);
    }

    return _allMessages.isEmpty
      ? Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.history,
                size: 64,
                color: Colors.grey[400],
              ),
              const SizedBox(height: 16),
              Text(
                '코칭 히스토리가 없습니다',
                style: TextStyle(
                  fontSize: 18,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        )
      : ListView(
          padding: const EdgeInsets.all(16),
          children: [
            // 타입별 통계
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      '코칭 통계',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 12),
                    ...messagesByType.entries.map((entry) {
                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 4),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(_getTypeDisplayName(entry.key)),
                            Chip(
                              label: Text('${entry.value.length}'),
                              backgroundColor: _getTypeColor(entry.key).withValues(alpha: 0.2),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ],
                ),
              ),
            ),
            const SizedBox(height: 16),
            
            // 최근 메시지들
            const Text(
              '최근 메시지',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...(_allMessages.take(20).toList().asMap().entries.map((entry) {
              return _buildMessageCard(entry.value, entry.key);
            }).toList()),
          ],
        );
  }

  Widget _buildSettingsTab() {
    return ListView(
      padding: const EdgeInsets.all(16),
      children: [
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.notifications),
                title: const Text('알림 설정'),
                subtitle: const Text('코칭 메시지 알림 관리'),
                trailing: Switch(
                  value: true, // 실제로는 설정값 연동 필요
                  onChanged: (value) {
                    // 알림 설정 변경 로직
                  },
                ),
              ),
              ListTile(
                leading: const Icon(Icons.schedule),
                title: const Text('코칭 빈도'),
                subtitle: const Text('30분마다'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showFrequencyDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.psychology),
                title: const Text('코칭 스타일'),
                subtitle: const Text('격려형'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showStyleDialog();
                },
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),
        
        Card(
          child: Column(
            children: [
              ListTile(
                leading: const Icon(Icons.analytics),
                title: const Text('코칭 분석'),
                subtitle: const Text('AI 코치 성과 분석'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showAnalysisDialog();
                },
              ),
              ListTile(
                leading: const Icon(Icons.delete),
                title: const Text('히스토리 삭제'),
                subtitle: const Text('모든 코칭 메시지 삭제'),
                trailing: const Icon(Icons.arrow_forward_ios),
                onTap: () {
                  _showDeleteDialog();
                },
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMessageCard(CoachingMessage message, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Card(
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: _getTypeColor(message.type).withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _getTypeDisplayName(message.type),
                      style: TextStyle(
                        fontSize: 12,
                        color: _getTypeColor(message.type),
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const Spacer(),
                  Text(
                    _formatTime(message.timestamp),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              Text(
                message.title,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                message.message,
                style: const TextStyle(fontSize: 14),
              ),
              if (message.actionText != null) ...[
                const SizedBox(height: 12),
                ElevatedButton(
                  onPressed: () {
                    // 액션 처리
                    ScaffoldMessenger.of(context).showSnackBar(
                      SnackBar(content: Text('${message.actionText} 실행됨')),
                    );
                  },
                  child: Text(message.actionText!),
                ),
              ],
            ],
          ),
        ),
      ),
    ).animate(delay: (index * 100).ms).fadeIn(duration: 400.ms).slideX(begin: 0.3, end: 0);
  }

  String _getTypeDisplayName(String type) {
    switch (type) {
      case 'motivation': return '동기부여';
      case 'reminder': return '리마인더';
      case 'tip': return '팁';
      case 'warning': return '경고';
      case 'celebration': return '축하';
      default: return '메시지';
    }
  }

  Color _getTypeColor(String type) {
    switch (type) {
      case 'motivation': return Colors.blue;
      case 'reminder': return Colors.orange;
      case 'tip': return Colors.green;
      case 'warning': return Colors.red;
      case 'celebration': return Colors.purple;
      default: return Colors.grey;
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 60) {
      return '${difference.inMinutes}분 전';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}시간 전';
    } else {
      return '${difference.inDays}일 전';
    }
  }

  void _showFrequencyDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('코칭 빈도 설정'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('15분마다'),
              leading: Radio(value: 15, groupValue: 30, onChanged: (value) {}),
            ),
            ListTile(
              title: const Text('30분마다'),
              leading: Radio(value: 30, groupValue: 30, onChanged: (value) {}),
            ),
            ListTile(
              title: const Text('1시간마다'),
              leading: Radio(value: 60, groupValue: 30, onChanged: (value) {}),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _showStyleDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('코칭 스타일 설정'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('격려형'),
              subtitle: const Text('긍정적이고 격려하는 메시지'),
              leading: Radio(value: 'encouraging', groupValue: 'encouraging', onChanged: (value) {}),
            ),
            ListTile(
              title: const Text('직설형'),
              subtitle: const Text('직접적이고 명확한 메시지'),
              leading: Radio(value: 'direct', groupValue: 'encouraging', onChanged: (value) {}),
            ),
            ListTile(
              title: const Text('친근형'),
              subtitle: const Text('친근하고 편안한 메시지'),
              leading: Radio(value: 'friendly', groupValue: 'encouraging', onChanged: (value) {}),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _showAnalysisDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('코칭 분석'),
        content: const Text('AI 코치가 당신의 습관 형성에 도움이 되고 있나요?\n\n• 총 메시지: 47개\n• 완료된 액션: 23개\n• 성공률: 89%'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('확인'),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('히스토리 삭제'),
        content: const Text('모든 코칭 메시지를 삭제하시겠습니까?\n이 작업은 되돌릴 수 없습니다.'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('취소'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('히스토리가 삭제되었습니다')),
              );
            },
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('삭제'),
          ),
        ],
      ),
    );
  }
}

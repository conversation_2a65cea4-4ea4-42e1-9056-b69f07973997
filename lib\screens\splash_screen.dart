import 'package:flutter/material.dart';
import 'dart:async';
import '../screens/home_screen.dart';
import '../services/personal_ai_coach_service.dart';
import '../services/cloud_ai_service.dart';
import '../services/ar_pose_analysis_service.dart';
import '../services/wearable_integration_service.dart';
import '../services/social_ai_network_service.dart';
import '../services/ai_coach_service.dart';
import '../services/advanced_analytics_service.dart';
import '../services/brainwave_monitoring_service.dart';
import '../services/hologram_trainer_service.dart';
import '../services/blockchain_nft_service.dart';
import '../services/ai_voice_synthesis_service.dart';
import '../services/metaverse_habit_space_service.dart';
import '../services/quantum_pattern_analysis_service.dart';
import '../utils/permission_handler.dart';

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen>
    with TickerProviderStateMixin {
  late AnimationController _logoController;
  late AnimationController _progressController;
  late Animation<double> _logoAnimation;
  late Animation<double> _progressAnimation;
  
  String _currentStatus = '시작 중...';
  double _progress = 0.0;
  List<String> _initializationSteps = [];

  @override
  void initState() {
    super.initState();
    
    _logoController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    
    _progressController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    );
    
    _logoAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _logoController,
      curve: Curves.elasticOut,
    ));
    
    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));
    
    _logoController.forward();
    _startInitialization();
  }

  Future<void> _startInitialization() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    try {
      // 1. 권한 요청 (빠르게)
      await _updateProgress('🔐 권한 확인 중...', 0.1);
      await PermissionHandler.requestAllPermissions();
      await PermissionHandler.handleSamsungSpecificPermissions();
      
      // 2. 기본 서비스들 (백그라운드에서 병렬로)
      await _updateProgress('🚀 기본 시스템 초기화...', 0.2);
      await Future.wait([
        PersonalAICoachService.initialize(),
        CloudAIService.initialize(),
      ]);
      
      // 3. AR 및 웨어러블 (병렬)
      await _updateProgress('📱 디바이스 연동 중...', 0.4);
      await Future.wait([
        ARPoseAnalysisService.initialize(),
        WearableIntegrationService.initialize(),
      ]);
      
      // 4. AI 서비스들 (병렬)
      await _updateProgress('🧠 AI 시스템 활성화...', 0.6);
      await Future.wait([
        SocialAINetworkService.initialize('user_${DateTime.now().millisecondsSinceEpoch}'),
        AICoachService.initialize(),
        AdvancedAnalyticsService.initialize(),
      ]);
      
      // 5. 혁신적 기능들 (병렬)
      await _updateProgress('🌟 혁신 기능 로딩...', 0.8);
      await Future.wait([
        BrainwaveMonitoringService.initialize(),
        HologramTrainerService.initialize(),
        BlockchainNFTService.initialize(),
      ]);
      
      // 6. 최종 기능들
      await _updateProgress('⚡ 최종 설정 중...', 0.9);
      await Future.wait([
        AIVoiceSynthesisService.initialize(),
        MetaverseHabitSpaceService.initialize(),
        QuantumPatternAnalysisService.initialize(),
      ]);
      
      await _updateProgress('✅ 완료!', 1.0);
      
      // 성공 메시지 표시
      setState(() {
        _currentStatus = '🎉 모든 시스템 준비 완료!';
      });
      
      await Future.delayed(const Duration(milliseconds: 1000));
      
      // 홈 화면으로 이동
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
      
    } catch (e) {
      // 오류 발생 시에도 앱 실행
      print('초기화 오류: $e');
      setState(() {
        _currentStatus = '⚠️ 일부 기능 제한 모드로 시작';
      });
      
      await Future.delayed(const Duration(milliseconds: 1500));
      
      if (mounted) {
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const HomeScreen()),
        );
      }
    }
  }

  Future<void> _updateProgress(String status, double progress) async {
    setState(() {
      _currentStatus = status;
      _progress = progress;
    });
    
    _progressController.animateTo(progress);
    await Future.delayed(const Duration(milliseconds: 300));
  }

  @override
  void dispose() {
    _logoController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0A0A0A),
      body: Container(
        decoration: const BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Color(0xFF1A1A2E),
              Color(0xFF16213E),
              Color(0xFF0A0A0A),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),
              
              // 로고 애니메이션
              AnimatedBuilder(
                animation: _logoAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: _logoAnimation.value,
                    child: Container(
                      width: 120,
                      height: 120,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        gradient: const LinearGradient(
                          colors: [Colors.purple, Colors.blue, Colors.cyan],
                        ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.purple.withOpacity(0.5),
                            blurRadius: 30,
                            spreadRadius: 10,
                          ),
                        ],
                      ),
                      child: const Icon(
                        Icons.psychology,
                        size: 60,
                        color: Colors.white,
                      ),
                    ),
                  );
                },
              ),
              
              const SizedBox(height: 30),
              
              // 앱 제목
              const Text(
                '습관 추적기',
                style: TextStyle(
                  fontSize: 32,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
              ),
              
              const SizedBox(height: 8),
              
              const Text(
                '세계 최초 차세대 AI 습관 관리',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.white70,
                ),
              ),
              
              const Spacer(),
              
              // 진행 상태
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 40),
                child: Column(
                  children: [
                    Text(
                      _currentStatus,
                      style: const TextStyle(
                        fontSize: 16,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    
                    const SizedBox(height: 20),
                    
                    // 진행률 바
                    AnimatedBuilder(
                      animation: _progressAnimation,
                      builder: (context, child) {
                        return Column(
                          children: [
                            LinearProgressIndicator(
                              value: _progress,
                              backgroundColor: Colors.white24,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Color.lerp(Colors.purple, Colors.cyan, _progress)!,
                              ),
                              minHeight: 6,
                            ),
                            
                            const SizedBox(height: 10),
                            
                            Text(
                              '${(_progress * 100).toInt()}%',
                              style: const TextStyle(
                                fontSize: 14,
                                color: Colors.white70,
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 60),
              
              // 하단 텍스트
              const Text(
                'Powered by Quantum AI',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.white38,
                ),
              ),
              
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }
}

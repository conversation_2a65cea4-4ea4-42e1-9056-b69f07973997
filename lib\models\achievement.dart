import 'package:flutter/material.dart';

class Achievement {
  final String id;
  final String title;
  final String description;
  final IconData icon;
  final Color color;
  final int requiredValue;
  final AchievementType type;

  const Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.icon,
    required this.color,
    required this.requiredValue,
    required this.type,
  });

  // 업적 달성 여부 확인
  bool isUnlocked(List<String> userAchievements) {
    return userAchievements.contains(id);
  }
}

enum AchievementType {
  streak,
  total,
  special,
}

class AchievementData {
  static const List<Achievement> allAchievements = [
    // 스트릭 업적
    Achievement(
      id: 'first_step',
      title: '첫 걸음',
      description: '첫 번째 습관을 완료하세요',
      icon: Icons.star,
      color: Colors.amber,
      requiredValue: 1,
      type: AchievementType.total,
    ),
    Achievement(
      id: 'week_warrior',
      title: '일주일 전사',
      description: '7일 연속 습관을 완료하세요',
      icon: Icons.local_fire_department,
      color: Colors.orange,
      requiredValue: 7,
      type: AchievementType.streak,
    ),
    Achievement(
      id: 'month_master',
      title: '한 달 마스터',
      description: '30일 연속 습관을 완료하세요',
      icon: Icons.emoji_events,
      color: Colors.purple,
      requiredValue: 30,
      type: AchievementType.streak,
    ),
    Achievement(
      id: 'century_champion',
      title: '백일장 챔피언',
      description: '100일 연속 습관을 완료하세요',
      icon: Icons.military_tech,
      color: Colors.red,
      requiredValue: 100,
      type: AchievementType.streak,
    ),
    
    // 총 완료 업적
    Achievement(
      id: 'half_century',
      title: '반백 달성',
      description: '총 50개의 습관을 완료하세요',
      icon: Icons.trending_up,
      color: Colors.green,
      requiredValue: 50,
      type: AchievementType.total,
    ),
    Achievement(
      id: 'centurion',
      title: '백전백승',
      description: '총 100개의 습관을 완료하세요',
      icon: Icons.workspace_premium,
      color: Colors.blue,
      requiredValue: 100,
      type: AchievementType.total,
    ),
    Achievement(
      id: 'legend',
      title: '전설',
      description: '총 500개의 습관을 완료하세요',
      icon: Icons.diamond,
      color: Colors.indigo,
      requiredValue: 500,
      type: AchievementType.total,
    ),
    
    // 특별 업적
    Achievement(
      id: 'early_bird',
      title: '얼리버드',
      description: '오전 6시 전에 습관을 완료하세요',
      icon: Icons.wb_sunny,
      color: Colors.yellow,
      requiredValue: 1,
      type: AchievementType.special,
    ),
    Achievement(
      id: 'night_owl',
      title: '올빼미',
      description: '밤 10시 후에 습관을 완료하세요',
      icon: Icons.nightlight,
      color: Colors.deepPurple,
      requiredValue: 1,
      type: AchievementType.special,
    ),
    Achievement(
      id: 'weekend_warrior',
      title: '주말 전사',
      description: '주말에도 습관을 완료하세요',
      icon: Icons.weekend,
      color: Colors.teal,
      requiredValue: 1,
      type: AchievementType.special,
    ),
  ];

  // 업적 ID로 업적 찾기
  static Achievement? getAchievementById(String id) {
    try {
      return allAchievements.firstWhere((achievement) => achievement.id == id);
    } catch (e) {
      return null;
    }
  }

  // 사용자가 달성한 업적 목록 반환
  static List<Achievement> getUnlockedAchievements(List<String> userAchievements) {
    return allAchievements
        .where((achievement) => achievement.isUnlocked(userAchievements))
        .toList();
  }

  // 아직 달성하지 못한 업적 목록 반환
  static List<Achievement> getLockedAchievements(List<String> userAchievements) {
    return allAchievements
        .where((achievement) => !achievement.isUnlocked(userAchievements))
        .toList();
  }

  // 업적 진행률 계산 (0.0 ~ 1.0)
  static double getAchievementProgress(Achievement achievement, int currentValue) {
    if (achievement.requiredValue == 0) return 1.0;
    return (currentValue / achievement.requiredValue).clamp(0.0, 1.0);
  }
}
